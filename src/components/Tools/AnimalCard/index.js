// modules
import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import { Text } from 'native-base';

// styles
import styles from './styles';

// localization
import i18n from '../../../localization/i18n';

// constants
import { TRASH_ICON } from '../../../constants/AssetSVGConstants';
import { normalize } from '../../../constants/theme/variables/customFont';
import { stringIsEmpty } from '../../../helpers/alphaNumericHelper';
import { convertInputNumbersToRegionalBasis } from '../../../helpers/genericHelper';

const AnimalCard = props => {
  const {
    title,
    earTag,
    daysInMilk,
    bcs,
    locomotion,
    onDeletePress,
    onCardPress,
    screenDisabled,
  } = props;
  const { customContainerStyle, customLabelStyle, customValueStyle } = props;

  return (
    <TouchableOpacity onPress={onCardPress} disabled={screenDisabled}>
      <View style={[styles.container, customContainerStyle]}>
        <View style={styles.titleRow}>
          <Text style={styles.titleText}>{title}</Text>
          {screenDisabled ? null : (
            <TouchableOpacity
              onPress={onDeletePress}
              style={styles.iconContainer}>
              <TRASH_ICON width={normalize(14)} height={normalize(16)} />
            </TouchableOpacity>
          )}
        </View>
        <View style={styles.infoRow}>
          <Text style={[styles.infoHeading, customLabelStyle]}>{`${i18n.t(
            'earTag',
          )}${i18n.t(':')}`}</Text>
          <Text style={[styles.infoValue, customValueStyle]}>
            {convertInputNumbersToRegionalBasis(earTag)}
          </Text> 
        </View>
        <View style={styles.infoRow}>
          <Text style={[styles.infoHeading, customLabelStyle]}>{`${i18n.t(
            'BCS',
          )}${i18n.t(':')}`}</Text>
          <Text style={[styles.infoValue, customValueStyle]}>
            {!stringIsEmpty(bcs) ? bcs : '-'}
          </Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={[styles.infoHeading, customLabelStyle]}>{`${i18n.t(
            'locomotion',
          )}${i18n.t(':')}`}</Text>
          <Text style={[styles.infoValue, customValueStyle]}>
            {!stringIsEmpty(locomotion) ? locomotion : '-'}
          </Text>
        </View>
        <View style={styles.infoRow}>
          <Text style={[styles.infoHeading, customLabelStyle]}>{`${i18n.t(
            'DIM',
          )}${i18n.t(':')}`}</Text>
          <Text style={[styles.infoValue, customValueStyle]}>
            {!stringIsEmpty(daysInMilk)
              ? `${daysInMilk} ${i18n.t('days')}`
              : '-'}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default AnimalCard;
