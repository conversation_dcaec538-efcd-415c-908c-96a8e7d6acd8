// modules
import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView } from 'react-native';
import { VictoryLabel } from 'victory-native';

// components
import CustomLineGraph from '../../../../../common/LineGraph';
import ToolGraph from '../../../common/ToolGraph';

// localization
import i18n from '../../../../../../localization/i18n';

//  helpers
import {
  getCalculateHerdAvgLocomotionScore,
  getLocomotionHerdStdDeviation,
  setHerdGraphData,
} from '../../../../../../helpers/locomotionHelper';
import { convertInputNumbersToRegionalBasis } from '../../../../../../helpers/genericHelper';

// styles
import styles from './styles';
import { normalize } from '../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../constants/theme/variables/customColor';

const ResultHerdAnalysis = ({
  herdData,
  selectedVisits,
  onDownloadPress,
  onShareHerdAnalysisData,
}) => {
  const [layout, setLayout] = useState(null);
  const [lineGraphData, setLineGraphData] = useState([]);
  const [landscapeModalVisible, setLandscapeModalVisible] = useState(false);

  useEffect(() => {
    let graphData = setHerdGraphData(herdData);
    setLineGraphData(graphData);
  }, [selectedVisits]);

  const onExpandIconPress = () => {
    setLandscapeModalVisible(!landscapeModalVisible);
  };

  const graphComponent = (
    <ScrollView>
      <CustomLineGraph
        showYAxis={
          <VictoryLabel
            x={layout?.width / 2}
            y={
              landscapeModalVisible
                ? styles.graphHeightLandscape.height - 20
                : styles.graphHeight.height - 20
            }
            textAnchor="middle"
            style={styles.yAxisLabel}
          />
        }
        showVerticalYAxis={true}
        verticalAxisLabel={`${i18n.t('percent')} ${i18n.t('%')}`}
        showVictory={true}
        animate={false}
        data={lineGraphData}
        width={
          landscapeModalVisible
            ? styles.graphWidthLandscape.width
            : styles.graphWidth.width
        }
        height={
          landscapeModalVisible
            ? styles.graphHeightLandscape.height
            : styles.graphHeight.height
        }
        showLabelsValue={true}
        showGradient={false}
      />
      <Text style={styles.yAxisLabel}>{i18n.t('locomotionScore')}</Text>
    </ScrollView>
  );

  return (
    <ScrollView showsVerticalScrollIndicator={false} style={styles.scrollView}>
      <View
        onLayout={({ nativeEvent }) => setLayout(nativeEvent.layout)}
        style={styles.container}>
        <ToolGraph
          showExpandIcon
          handleExpandIconPress={onExpandIconPress}
          showDownloadIcon={!landscapeModalVisible}
          showShareIcon={!landscapeModalVisible}
          landscapeModalVisible={landscapeModalVisible}
          onDownloadPress={option =>
            onDownloadPress(lineGraphData, option, herdData)
          }
          onSharePress={(option, exportMethod) => {
            onShareHerdAnalysisData(
              lineGraphData,
              option,
              herdData,
              exportMethod,
            );
          }}
          customGraphTitleComponent={
            <View style={styles.infoColumn}>
              <View>
                <Text style={styles.labelValue}>
                  {' '}
                  {`${i18n.t('locomotionScore')}`}
                </Text>
              </View>
              <View style={styles.statsRow}>
                <Text style={styles.statsTitle}>
                  {`${i18n.t('avg')}: `}
                  <Text style={styles.statsValue}>
                    {convertInputNumbersToRegionalBasis(
                      getCalculateHerdAvgLocomotionScore(herdData),
                      2,
                      true,
                    )}
                  </Text>
                </Text>
                <Text style={[styles.statsTitle, styles.leftMargin]}>
                  {`${i18n.t('std')}: `}
                  <Text style={styles.statsValue}>
                    {convertInputNumbersToRegionalBasis(
                      getLocomotionHerdStdDeviation(herdData).toFixed(2),
                      2,
                      true,
                    )}
                  </Text>
                </Text>
              </View>
            </View>
          }
          graphComponent={layout && graphComponent}
        />
        {layout && (
          <View style={styles.labelsView}>
            <View style={styles.herdAvg}></View>
            <Text style={styles.labelsTitle}>{i18n.t('herdAverage')}</Text>
            <View style={styles.herdGoal}></View>
            <Text style={styles.labelsTitle}>{i18n.t('goal')}</Text>
          </View>
        )}
      </View>
    </ScrollView>
  );
};
const sharedAxisStyles = {
  axisLabel: {
    padding: normalize(29),
    fontSize: normalize(11),
    fill: colors.alphabetIndex,
  },
};
export default ResultHerdAnalysis;
