// modules
import { useEffect, useRef } from 'react';
import {
  View,
  Text,
  Platform,
  ScrollView,
  KeyboardAvoidingView,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// localization
import i18n from '../../../../../localization/i18n';

//components
import ToolAlert from '../../common/ToolAlert';
import HerdSiteForm from './HerdSiteForm/index.js';
import ResultHerdAnalysis from './ResultHerdAnalysis';
import HerdAnalysisCategories from './HerdCategories/index.js';

// styles
import styles from './styles';

// helpers
import { shouldEnableResultsButton } from '../../../../../helpers/locomotionHelper';

//actions
import { hideLocomotionToolToastRequest } from '../../../../../store/actions/userPreferences';

// constants
import { TOOL_ANALYSIS_TYPES } from '../../../../../constants/AppConstants';

//lodash
import _ from 'lodash';

const HerdAnalysis = props => {
  const {
    herdData,
    currentStep,
    totalToolSteps,
    selectedVisits,
    onDownloadPress,
    onShareHerdAnalysisData,
    setEnableResults,
    herdDataForHerdSum,
  } = props;

  const totalAnimalsRef = useRef();

  const dispatch = useDispatch();

  //redux states
  const visitState = useSelector(state => state.visit.visit);
  const { isEditable = false } = visitState;
  const userPreferencesState = useSelector(state => state.userPreferences);
  const locomotionHerdData = useSelector(
    state => state.locomotionScore.locomotionToolData?.herd,
  );

  // useEffect(() => {
  //   herdDataForHerdSum.current = herdData;
  //   debounce_fun(herdData);
  // }, [herdData]);

  useEffect(() => {
    herdDataForHerdSum.current = locomotionHerdData;
    debounce_fun(locomotionHerdData);
  }, [herdData]);

  //disables results button if herd's values sum to 0
  const debounce_fun = _.debounce(penAnalysisArray => {
    setEnableResults(
      shouldEnableResultsButton(
        TOOL_ANALYSIS_TYPES.HERD_ANALYSIS,
        penAnalysisArray,
      ),
    );
  }, 1000);

  const onCloseToast = () => {
    dispatch(hideLocomotionToolToastRequest(true));
  };

  const getToolToast = () => {
    const {
      userPreferences: { defaultValues },
    } = userPreferencesState || {};
    const { ['LocomotionHerdAnalysis']: locomotionHerdToast } =
      defaultValues || false;
    return isEditable ? locomotionHerdToast : false;
  };

  const renderHerdAnalysisResults = () => {
    return (
      <ResultHerdAnalysis
        herdData={herdData}
        selectedVisits={selectedVisits}
        onDownloadPress={onDownloadPress}
        onShareHerdAnalysisData={onShareHerdAnalysisData}
      />
    );
  };

  return (
    <>
      {currentStep === totalToolSteps ? (
        renderHerdAnalysisResults()
      ) : (
        <View style={styles.flexOne}>
          <KeyboardAvoidingView
            style={styles.keyboardContainer}
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            keyboardVerticalOffset={
              Platform.OS === 'ios'
                ? styles.keyboardVerticalOffsetIOS
                : styles.keyboardVerticalOffsetAndroid
            }>
            <ScrollView showsVerticalScrollIndicator={false}>
              <View style={styles.container}>
                <Text style={styles.headingText}>
                  {i18n.t('locomotionScoreAnalysis')}
                </Text>

                <HerdAnalysisCategories totalAnimalsRef={totalAnimalsRef} />

                <HerdSiteForm totalAnimalsRef={totalAnimalsRef} />
              </View>
            </ScrollView>
          </KeyboardAvoidingView>

          {!!getToolToast() && (
            <View style={styles.alert}>
              <ToolAlert onCloseToast={onCloseToast} />
            </View>
          )}
        </View>
      )}
    </>
  );
};

export default HerdAnalysis;
