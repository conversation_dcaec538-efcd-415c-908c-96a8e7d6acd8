// modules
import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  Platform,
  Keyboard,
  KeyboardAvoidingView,
  ScrollView,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

import i18n from '../../../../../localization/i18n';

//components
import ToolAlert from '../../common/ToolAlert';
import ResultHerdAnalysis from './ResultHerdAnalysis';
import CustomInputAccessoryView from '../../../../Accounts/AddEdit/CustomInput';
import NumberFormInput from '../../../../common/NumberFormInput/index.js';

// styles
import styles from './styles';

import {
  convertStringToNumber,
  removeStringCommas,
  stringIsEmpty,
} from '../../../../../helpers/alphaNumericHelper';
import {
  getCalculateHerdAverage,
  getCalculateMilkLoss,
  getCalculateTotalAnimals,
  getCategoryWiseAnimalObserved,
  onUpdateSiteObj,
  onValueChange,
  shouldEnableResultsButton,
} from '../../../../../helpers/locomotionHelper';
import {
  convertWeightToImperial,
  convertWeightToMetric,
  getWeightUnitByMeasure,
} from '../../../../../helpers/appSettingsHelper';
import { convertInputNumbersToRegionalBasis } from '../../../../../helpers/genericHelper.js';

//actions
import { hideLocomotionToolToastRequest } from '../../../../../store/actions/userPreferences';
import {
  getSiteByVisitRequest,
  updateToolSiteRequest,
} from '../../../../../store/actions/site';

import {
  CONTENT_TYPE,
  KEYBOARD_TYPE,
} from '../../../../../constants/FormConstants';
import {
  NEXT_FIELD_TEXT,
  TOOL_ANALYSIS_TYPES,
  UNIT_OF_MEASURE,
} from '../../../../../constants/AppConstants';


//lodash
import _ from 'lodash';

const HerdAnalysis = props => {
  const {
    herdData,
    setHerdData,
    animalsInHerd,
    setAnimalsInHerd,
    daysInMilk,
    setDaysInMilk,
    milkProduction,
    setMilkProduction,
    openToolSheet,
    currentStep,
    totalToolSteps,
    selectedVisits,
    onDownloadPress,
    onShareHerdAnalysisData,
    isDirty,
    setIsDirty,
    setEnableResults,
    herdDataForHerdSum,
  } = props;

  useEffect(() => {
    herdDataForHerdSum.current = herdData;
    debounce_fun(herdData);
  }, [herdData]);

  //disables results button if herd's values sum to 0
  const debounce_fun = _.debounce(penAnalysisArray => {
    setEnableResults(
      shouldEnableResultsButton(
        TOOL_ANALYSIS_TYPES.HERD_ANALYSIS,
        penAnalysisArray,
      ),
    );
  }, 1000);

  //ref
  const inputRef = useRef([]);

  let totalAnimalsRef = useRef();
  let DIMRef = useRef();
  let milkProductionRef = useRef();

  //helpers
  const focusTotalAnimalsRef = () => {
    totalAnimalsRef?.focus?.();
  };
  const focusDIMRef = () => {
    DIMRef?.focus?.();
  };
  const focusMilkProductionRef = () => {
    milkProductionRef?.focus?.();
  };

  const dispatch = useDispatch();

  //redux states
  const visitState = useSelector(state => state.visit);
  const siteState = useSelector(state => state.site);
  const { isEditable = false } = visitState?.visit;
  const userPreferencesState = useSelector(state => state.userPreferences);

  const unitOfMeasure = visitState?.visit?.unitOfMeasure || {};
  const weightUnit = getWeightUnitByMeasure(unitOfMeasure);

  //local states
  const [onFocusField, setonFocusField] = useState('');
  const [type, setType] = useState(CONTENT_TYPE.TEXT);
  const [action, setAction] = useState(() => {});

  useEffect(() => {
    const { siteId, localSiteId } = visitState.visit;
    dispatch(getSiteByVisitRequest({ siteId, localSiteId }));
  }, []);

  // user manually change animal in per pen form pen setup calculate run time pens% and animal in per pen
  const onChangeAnimalInHerd = value => {
    value = removeStringCommas(value);
    if (onValueChange(value, 0, 99999, true, 0)) {
      setAnimalsInHerd(value);
      setIsDirty(true);
    }
  };

  const onChangeMilkProduction = value => {
    if (onValueChange(value, 0, 999, false, 1)) {
      setMilkProduction(value);
      setIsDirty(true);
    }
  };

  const onChangeGoal = (value, index) => {
    let data = { ...herdData };
    if (onValueChange(value)) {
      data.categories[index].herdGoal = value;
      setHerdData(data);
      setIsDirty(true);
    }
  };

  const onChangeDaysInMilk = value => {
    // if (+value >= -100 && +value <= 999) {
    //   return;
    // }

    if (onValueChange(value, -100, 999, true, 0, true)) {
      setDaysInMilk(value);
      setIsDirty(true);
    }
  };

  const onSiteUpdate = () => {
    const updateSiteObj = onUpdateSiteObj(
      siteState.visitSite,
      unitOfMeasure === UNIT_OF_MEASURE.IMPERIAL
        ? convertWeightToMetric(convertStringToNumber(milkProduction))
        : convertStringToNumber(milkProduction),
      daysInMilk,
      animalsInHerd,
    );
    dispatch(updateToolSiteRequest(updateSiteObj));
  };

  const onBlurInput = (newValue, oldValue) => {
    if (newValue != oldValue) {
      onSiteUpdate();
    }
  };

  const closeValidatorAlert = () => {
    switch (onFocusField) {
      case i18n.t('milkProduction'):
        setMilkProduction(siteState?.visitSite?.milk);
        break;
      case i18n.t('daysInMilk'):
        setDaysInMilk(
          stringIsEmpty(siteState?.visitSite?.daysInMilk)
            ? '-'
            : siteState?.visitSite?.daysInMilk,
        );
        break;
      case i18n.t('animalInHerd'):
        setAnimalsInHerd(siteState?.visitSite?.lactatingAnimal);
        break;
      default:
        break;
    }
    setonFocusField('');
  };

  const onFocusFieldUpdate = field => {
    if (stringIsEmpty(onFocusField)) {
      setonFocusField(field);
    }
  };

  const renderHerdAvg = item => {
    let herdAvg = getCalculateHerdAverage(item, herdData);
    if (herdAvg <= 0) {
      return '-';
    } else {
      return herdAvg;
    }
  };

  const renderTotalAnimals = item => {
    let calculateTotalAnimals = getCalculateTotalAnimals(
      item,
      herdData,
      animalsInHerd,
    );
    if (calculateTotalAnimals <= 0) {
      return '-';
    } else {
      return calculateTotalAnimals;
    }
  };

  const onCloseToast = () => {
    dispatch(hideLocomotionToolToastRequest(true));
  };

  const getToolToast = () => {
    const {
      userPreferences: { defaultValues },
    } = userPreferencesState || {};
    const { ['LocomotionHerdAnalysis']: locomotionHerdToast } =
      defaultValues || false;
    return isEditable ? locomotionHerdToast : false;
  };

  const renderHerdAnalysisResults = () => {
    return (
      <ResultHerdAnalysis
        herdData={herdData}
        selectedVisits={selectedVisits}
        onDownloadPress={onDownloadPress}
        onShareHerdAnalysisData={onShareHerdAnalysisData}
      />
    );
  };

  return (
    <>
      {currentStep === totalToolSteps ? (
        renderHerdAnalysisResults()
      ) : (
        <View onTouchStart={() => Keyboard.dismiss()} style={styles.flexOne}>
          {/* <KeyboardAwareScrollView
            style={styles.flexOne}
            enableOnAndroid
            enableAutomaticScroll
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="always"
            keyboardOpeningTime={0}
            extraScrollHeight={220}
            scrollToOverflowEnabled={true}
            contentInset={{ top: 0 }}
            extraHeight={Platform.select({
              android: 200,
            })}> */}

          <KeyboardAvoidingView
            style={styles.keyboardContainer}
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            keyboardVerticalOffset={
              Platform.OS === 'ios'
                ? styles.keyboardVerticalOffsetIOS
                : styles.keyboardVerticalOffsetAndroid
            }>
            <CustomInputAccessoryView doneAction={action} type={type} />
            <ScrollView showsVerticalScrollIndicator={false}>
              <View style={styles.container}>
                <Text style={styles.headingText}>
                  {i18n.t('locomotionScoreAnalysis')}
                </Text>
                {herdData?.categories?.map((item, index) => (
                  <View key={index}>
                    <View style={styles.listView}>
                      <Text style={styles.catName}>
                        {`${i18n.t('cat')} ${item.category}`}
                      </Text>
                      <Text style={styles.animalObserved}>
                        {`(${i18n.t(
                          'animalObserved',
                        )}: ${getCategoryWiseAnimalObserved(item, herdData)})`}
                      </Text>
                    </View>
                    <View style={styles.listChildView}>
                      <View style={styles.animalObservedView}>
                        <View style={styles.listHeaderView}>
                          <Text style={styles.listHeaderText}>
                            {i18n.t('herdGoal')} ({i18n.t('%')})
                          </Text>
                        </View>

                        <View style={styles.inputView}>
                          <NumberFormInput
                            disabled={!isEditable}
                            keyboardType={KEYBOARD_TYPE.DECIMAL}
                            onChange={text => onChangeGoal(text, index)}
                            decimalPoints={2}
                            value={String(item?.herdGoal)}
                            style={styles.input}
                            reference={e => {
                              inputRef.current[index] = e;
                            }}
                            minValue={0}
                            maxValue={100}
                            blurOnSubmit={false}
                            onSubmitEditing={() => {
                              if (index < inputRef.current.length - 1) {
                                inputRef?.current[index + 1]?.focus();
                              } else {
                                focusTotalAnimalsRef();
                              }
                            }}
                            inputAccessoryViewID="customInputAccessoryView"
                            returnKeyType={NEXT_FIELD_TEXT.NEXT}
                            onFocus={() => {
                              setType(CONTENT_TYPE.NUMBER);
                              setAction({
                                currentRef:
                                  index < inputRef.current.length - 1
                                    ? inputRef?.current[index + 1]
                                    : totalAnimalsRef,
                              });
                            }}
                          />
                        </View>
                      </View>
                      <View style={styles.animalsInPen}>
                        <View style={styles.listHeaderView}>
                          <Text style={styles.listHeaderText}>
                            {i18n.t('herdAverage')} ({i18n.t('%')})
                          </Text>
                        </View>
                        <View style={styles.listValue}>
                          <Text style={styles.listViewText}>
                            {convertInputNumbersToRegionalBasis(
                              renderHerdAvg(item),
                              2,
                            )}
                          </Text>
                        </View>
                      </View>
                      <View style={styles.animalsInPen}>
                        <View style={styles.listHeaderView}>
                          <Text style={styles.listHeaderText}>
                            {i18n.t('totalAnimals')}
                          </Text>
                        </View>
                        <View style={styles.listValue}>
                          <Text style={styles.listViewText}>
                            {convertInputNumbersToRegionalBasis(
                              renderTotalAnimals(item),
                              0,
                              true,
                            )}
                          </Text>
                        </View>
                      </View>
                    </View>
                  </View>
                ))}
                <View style={styles.penSetupContainer}>
                  <View style={styles.penSetupView}>
                    <Text style={styles.penSetupText}>
                      {i18n.t('fromSiteSetup')}
                    </Text>
                  </View>

                  <View style={styles.penSetupChildContainer}>
                    <View style={styles.penViewChildContainer}>
                      <View style={styles.justifyCenter}>
                        <Text style={styles.penChildText}>
                          {i18n.t('animalInHerd')}
                        </Text>
                      </View>
                      <NumberFormInput
                        disabled={!isEditable}
                        keyboardType={KEYBOARD_TYPE.NUMBER_PAD}
                        placeholder={'0'}
                        value={String(animalsInHerd)}
                        onBlur={() =>
                          onBlurInput(
                            animalsInHerd,
                            siteState?.visitSite?.lactatingAnimal,
                          )
                        }
                        hasCommas={true}
                        blurOnSubmit={false}
                        onChange={onChangeAnimalInHerd}
                        style={styles.input}
                        reference={input => (totalAnimalsRef = input)}
                        onSubmitEditing={() => {
                          focusDIMRef();
                        }}
                        inputAccessoryViewID="customInputAccessoryView"
                        returnKeyType={NEXT_FIELD_TEXT.NEXT}
                        onFocus={() => {
                          setType(CONTENT_TYPE.NUMBER);
                          onFocusFieldUpdate(i18n.t('animalInHerd'));
                          setAction({
                            currentRef: DIMRef,
                          });
                        }}
                      />
                    </View>

                    <View style={styles.penViewChildContainer}>
                      <View style={styles.justifyCenter}>
                        <Text style={styles.penChildText}>
                          {i18n.t('daysInMilk')}
                        </Text>
                      </View>
                      <NumberFormInput
                        disabled={!isEditable}
                        keyboardType={
                          Platform.OS === 'ios'
                            ? KEYBOARD_TYPE.NUMBERS_AND_PUNCTUATION
                            : KEYBOARD_TYPE.NUMBER_PAD
                        }
                        placeholder="0"
                        blurOnSubmit={false}
                        isNegative={true}
                        value={String(daysInMilk)}
                        onBlur={() =>
                          onBlurInput(
                            daysInMilk,
                            siteState?.visitSite?.daysInMilk,
                          )
                        }
                        hasCommas={true}
                        onChange={onChangeDaysInMilk}
                        style={styles.input}
                        reference={input => (DIMRef = input)}
                        onSubmitEditing={() => {
                          focusMilkProductionRef();
                        }}
                        inputAccessoryViewID="customInputAccessoryView"
                        returnKeyType={NEXT_FIELD_TEXT.NEXT}
                        onFocus={() => {
                          setType(CONTENT_TYPE.NUMBER);
                          onFocusFieldUpdate(i18n.t('daysInMilk'));
                          setAction({
                            currentRef: milkProductionRef,
                          });
                        }}
                      />
                    </View>

                    <View style={styles.penViewChildContainer}>
                      <View style={styles.justifyCenter}>
                        <Text style={styles.penChildText}>
                          {`${i18n.t('milkProduction')} (${weightUnit})`}
                        </Text>
                      </View>
                      <NumberFormInput
                        disabled={!isEditable}
                        keyboardType={KEYBOARD_TYPE.DECIMAL}
                        placeholder={i18n.t('singleDecimalNumberPlaceholder')}
                        blurOnSubmit={false}
                        onChange={onChangeMilkProduction}
                        onBlur={() =>
                          onBlurInput(
                            milkProduction,
                            unitOfMeasure === UNIT_OF_MEASURE.IMPERIAL
                              ? convertWeightToImperial(
                                  siteState?.visitSite?.milk,
                                  1,
                                )
                              : siteState?.visitSite?.milk,
                          )
                        }
                        value={String(milkProduction)}
                        style={styles.input}
                        reference={input => (milkProductionRef = input)}
                        onSubmitEditing={() => {
                          Keyboard?.dismiss();
                        }}
                        inputAccessoryViewID="customInputAccessoryView"
                        returnKeyType={NEXT_FIELD_TEXT.DONE}
                        onFocus={() => {
                          setType(CONTENT_TYPE.NUMBER);
                          onFocusFieldUpdate(i18n.t('milkProduction'));
                          setAction({
                            dismiss: true,
                          });
                        }}
                      />
                    </View>

                    <View style={styles.penViewChildContainer}>
                      <View style={styles.milkLossViewHeader}>
                        <Text style={styles.penChildText}>
                          {i18n.t('milkLoss')}{' '}
                        </Text>
                        <Text style={styles.penChildText}>({weightUnit})</Text>
                      </View>
                      <View style={styles.milkLossView}>
                        <Text style={styles.milkLossText}>
                          {convertInputNumbersToRegionalBasis(
                            getCalculateMilkLoss(
                              herdData,
                              milkProduction,
                              true,
                            ),
                            4,
                          )}
                        </Text>
                      </View>
                    </View>
                  </View>
                </View>
              </View>
              {/* </KeyboardAwareScrollView> */}
            </ScrollView>
          </KeyboardAvoidingView>

          {!!getToolToast() && (
            <View style={styles.alert}>
              <ToolAlert onCloseToast={onCloseToast} />
            </View>
          )}
        </View>
      )}
    </>
  );
};
export default HerdAnalysis;
