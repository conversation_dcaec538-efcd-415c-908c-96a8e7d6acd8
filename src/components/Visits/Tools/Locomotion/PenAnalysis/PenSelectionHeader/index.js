// modules
import { View, Text } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// localization
import i18n from '../../../../../../localization/i18n';

// styles
import styles from './styles';

// components
import CustomBottomSheet from '../../../../../common/CustomBottomSheet';

// constants
import { BOTTOM_SHEET_TYPE } from '../../../../../../constants/FormConstants';

// helpers
import { saveSelectedPenInReducer } from '../../../../../../helpers/visitHelper';

// actions
import { changeLocomotionPen } from '../../../../../../store/actions/tools/locomotionScore';

const PenSelectionAndCount = ({ setIsDirty }) => {
  const dispatch = useDispatch();

  const pensList = useSelector(state => state.tool.pensList);
  const selectedPen = useSelector(state => state.locomotionScore.selectedPen);

  const _handleChangePen = item => {
    saveSelectedPenInReducer(dispatch, item);
    setIsDirty(false);
    dispatch(changeLocomotionPen(item));
  };

  const totalCowsCount = selectedPen?.categories?.reduce((acc, item) => {
    return acc + item.animalsObserved;
  }, 0);

  const selectedPenLocalId = pensList?.find(
    item => item.id === selectedPen?.penId || item.sv_id === selectedPen?.penId,
  );

  return (
    <View style={styles.penNameRow}>
      <CustomBottomSheet
        type={BOTTOM_SHEET_TYPE.SIMPLE_LIST}
        selectLabel={i18n.t('selectPen')}
        searchPlaceHolder={i18n.t('searchPen')}
        infoText={i18n.t('selectOne')}
        data={pensList}
        value={selectedPenLocalId?.id || selectedPen?.penId || ''}
        onChange={_handleChangePen}
        customInputStyle={styles.firstDropdown}
        customValueStyle={styles.customValue}
        customIconStyles={styles.dropdownIcon}
        iconStroke={styles.iconStrokeColor}
        customLabelStyle={styles.customFieldLabel}
      />

      <View style={styles.totalCowsCountView}>
        <Text style={styles.totalCowsCountText}>
          {`${i18n.t('animalsObserved')}: ${totalCowsCount}`}
        </Text>
      </View>
    </View>
  );
};

export default PenSelectionAndCount;
