// modules
import React, { useState, useEffect } from 'react';
import { View, useWindowDimensions, Text, ScrollView } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// components
import CustomLineGraph from '../../../../../common/LineGraph';
import ToolGraph from '../../../common/ToolGraph';

// localization
import i18n from '../../../../../../localization/i18n';

// styles
import styles from './styles';

// constants
import {
  DATE_FORMATS,
  VISIT_TABLE_FIELDS,
} from '../../../../../../constants/AppConstants';

// helpers
import { sortRecentVisitsForGraph } from '../../../../../../helpers/toolHelper';
import { stringIsEmpty } from '../../../../../../helpers/alphaNumericHelper';
import { getFormattedDate } from '../../../../../../helpers/dateHelper';
import {
  getAllLocomotionScoreData,
  getLocomotionAvg,
  getLocomotionStdDeviation,
  getTotalCowsCountLocomotion,
  setAnimalAnalysisGraphData,
} from '../../../../../../helpers/locomotionHelper';

// actions
import { getRecentVisitsForToolRequest } from '../../../../../../store/actions/tool';
import { convertInputNumbersToRegionalBasis } from '../../../../../../helpers/genericHelper';

const PenAnalysisResults = ({
  selectedPen,
  penData,
  selectedVisits,
  onDownloadPress,
  onSharePenAnalysisData,
}) => {
  const dispatch = useDispatch();
  const visitState = useSelector(state => state.visit);
  const toolState = useSelector(state => state.tool);
  const [landscapeModalVisible, setLandscapeModalVisible] = useState(false);
  const [layout, setLayout] = useState(null);
  const [recentVisits, setRecentVisits] = useState([]);
  const [penGraphData, setPenGraphData] = useState([]);
  const [lineGraphData, setLineGraphData] = useState({
    labels: [],
    datasets: [
      {
        data: [],
        color: () => styles.dataLineColor,
      },
    ],
  });

  useEffect(() => {
    const visit = visitState.visit;
    const siteId = !stringIsEmpty(visit.siteId)
      ? visit.siteId
      : visit.localSiteId;
    const accountId = !stringIsEmpty(visit.customerId)
      ? visit.customerId
      : visit.localCustomerId;

    dispatch(
      getRecentVisitsForToolRequest({
        siteId,
        accountId,
        localVisitId: visit.id,
        tool: VISIT_TABLE_FIELDS.LOCOMOTION_SCORE,
      }),
    );
  }, []);

  useEffect(() => {
    if (!toolState.recentVisitsLoading) {
      const data = toolState.recentVisits.map(visitObj => {
        const allData = visitObj?.locomotionScore
          ? getAllLocomotionScoreData(JSON.parse(visitObj?.locomotionScore))
          : [];
        if (allData?.length > 0 && selectedPen) {
          let filteredPenArray = [];
          if (!stringIsEmpty(selectedPen?.sv_id)) {
            filteredPenArray = allData.filter(
              penObj => penObj.penId === selectedPen?.sv_id,
            );
          } else {
            filteredPenArray = allData.filter(
              penObj => penObj?.penId === selectedPen?.id,
            );
          }
          if (filteredPenArray && filteredPenArray?.length > 0) {
            const currentPenObject = filteredPenArray[0];
            return {
              ...currentPenObject,
              visitId: visitObj.id,
              date: visitObj.visitDate,
            };
          }
          return { visitId: visitObj.id, date: visitObj.visitDate };
        }
        return { visitId: visitObj.id, date: visitObj.visitDate };
      });
      setRecentVisits(data);
    }
  }, [toolState.recentVisitsLoading]);

  useEffect(() => {
    let selectedRecentVisits = recentVisits.slice(1);
    selectedRecentVisits = selectedRecentVisits.filter(visit =>
      selectedVisits?.includes(visit.visitId),
    );
    selectedRecentVisits.push(penData);
    selectedRecentVisits = sortRecentVisitsForGraph(selectedRecentVisits);

    const graphData = {
      labels: [
        ...selectedRecentVisits.map(visit =>
          getFormattedDate(visit.date, DATE_FORMATS.MM_dd),
        ),
      ],
      datasets: [
        {
          data: [...selectedRecentVisits.map(visit => getLocomotionAvg(visit))],
          color: () => styles.dataLineColor,
        },
      ],
    };
    setPenGraphData(setAnimalAnalysisGraphData(selectedRecentVisits));
    setLineGraphData(graphData);
  }, [penData, recentVisits, selectedVisits]);

  const onExpandIconPress = () => {
    setLandscapeModalVisible(!landscapeModalVisible);
  };

  const customGraphTitleComponent = (
    <View style={styles.infoColumn}>
      <View style={styles.statsRow}>
        <Text style={styles.statsTitle}>
          {`${i18n.t('avg')}: `}
          <Text style={styles.statsValue}>
            {convertInputNumbersToRegionalBasis(
              getLocomotionAvg(penData).toFixed(2),
              2,
              true,
            )}
          </Text>
        </Text>
        <Text style={[styles.statsTitle, styles.leftMargin]}>
          {`${i18n.t('std')}: `}
          <Text style={styles.statsValue}>
            {/* {getLocomotionStdDeviation(penData).toFixed(2)} */}
            {convertInputNumbersToRegionalBasis(
              getLocomotionStdDeviation(penData).toFixed(2),
              2,
              true,
            )}
          </Text>
        </Text>
      </View>
    </View>
  );

  const graphComponent = (
    <View>
      <View style={styles.textContainer}>
        <Text style={styles.penAnalysisTitle}>
          {i18n.t('pen') || ''}:{' '}
          <Text style={styles.penAnalysisValue}>{selectedPen?.name || ''}</Text>
        </Text>
        <Text style={styles.penAnalysisTitle}>
          {i18n.t('animalsObserved') || ''}:{' '}
          <Text style={styles.penAnalysisValue}>
            {getTotalCowsCountLocomotion(penData)}
          </Text>
        </Text>
      </View>
      <CustomLineGraph
        verticalAxisDomain
        showVerticalYAxis={true}
        verticalAxisLabel={`${i18n.t('category')}`}
        showVictory={true}
        animate={false}
        data={penGraphData}
        width={
          landscapeModalVisible
            ? styles.graphWidthLandscape.width
            : styles.graphWidth.width
        }
        height={
          landscapeModalVisible
            ? styles.graphHeightLandscape.height
            : styles.graphHeight.height
        }
        showLabelsValue={true}
      />
    </View>
  );
  return (
    <ScrollView
      style={styles.container}
      onLayout={({ nativeEvent }) => setLayout(nativeEvent.layout)}>
      <ToolGraph
        showExpandIcon
        handleExpandIconPress={onExpandIconPress}
        showDownloadIcon={!landscapeModalVisible}
        onDownloadPress={option =>
          onDownloadPress(penGraphData, option, selectedPen, penData)
        }
        onSharePress={(option, exportMethod) =>
          onSharePenAnalysisData(
            penGraphData,
            option,
            selectedPen,
            penData,
            exportMethod,
          )
        }
        showShareIcon={!landscapeModalVisible}
        landscapeModalVisible={landscapeModalVisible}
        // onExpandIconPress={onExpandIconPress}
        customGraphTitleComponent={customGraphTitleComponent}
        graphComponent={layout && graphComponent}
      />
    </ScrollView>
  );
};

export default PenAnalysisResults;
