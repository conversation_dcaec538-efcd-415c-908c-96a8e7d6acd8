// modules
import { useIsFocused } from '@react-navigation/native';
import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Platform,
  ScrollView,
  Keyboard,
  KeyboardAvoidingView,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
//lodash
import _ from 'lodash';

// Reusable Components
import CircularCounter from '../../../../common/CircularCounter';
import CustomBottomSheet from '../../../../common/CustomBottomSheet';
import AccordionInActiveIcon from '../../../../common/AccordionInActiveIcon';
import AccordionActiveIcon from '../../../../common/AccordionActiveIcon';
import PenAnalysisGraph from './ResultPenAnalysis';
import ToolAlert from '../../common/ToolAlert';
import CustomInputAccessoryView from '../../../../Accounts/AddEdit/CustomInput';
import NumberFormInput from '../../../../common/NumberFormInput';

// SVG_ICONS
import { CHEVRON_DOWN_BLUE_ICON } from '../../../../../constants/AssetSVGConstants';

// FORM CONSTANT
import {
  BOTTOM_SHEET_TYPE,
  CONTENT_TYPE,
  KEYBOARD_TYPE,
} from '../../../../../constants/FormConstants';

// localization
import i18n from '../../../../../localization/i18n';

// styles
import styles from './styles';
import { normalize } from '../../../../../constants/theme/variables/customFont';

// helpers
import {
  convertStringToNumber,
  getFormattedCommaNumber,
  getFormattedDecimalValues,
  removeStringCommas,
  stringIsEmpty,
} from '../../../../../helpers/alphaNumericHelper';
import {
  animalInPerPen,
  calculateMilkLoss,
  pensInPercent,
  getSelectedPen,
  onUpdatePen,
  onUpdatedLocomotionScoreObj,
  onValueChange,
  allPenAnalysis,
  onSavePenData,
  shouldEnableResultsButton,
  getPensWithAnimalObserved,
  getTotalCowsCountLocomotion,
} from '../../../../../helpers/locomotionHelper';
import { dateHelper } from '../../../../../helpers/dateHelper';
import {
  pickPenInReducerFromPensList,
  saveSelectedPenInReducer,
} from '../../../../../helpers/visitHelper';
import {
  convertWeightToImperial,
  convertWeightToMetric,
  getWeightUnitByMeasure,
} from '../../../../../helpers/appSettingsHelper';
import { penExistsInPublishedVisit } from '../../../../../helpers/toolHelper';
import { logEvent } from '../../../../../helpers/logHelper';
import { convertInputNumbersToRegionalBasis } from '../../../../../helpers/genericHelper';

// actions
import {
  getPenByIdRequest,
  resetUpdatePenRequest,
  updatePenRequest,
} from '../../../../../store/actions/pen';
import {
  getSitePensRequest,
  setSelectedPenRequest,
} from '../../../../../store/actions/tool';

import {
  LOCOMOTION_CATEGORY_LIST,
  TOOL_TYPES,
  NEXT_FIELD_TEXT,
  UNIT_OF_MEASURE,
  TOOL_ANALYSIS_TYPES,
  VISIT_TABLE_FIELDS,
} from '../../../../../constants/AppConstants';
import { hideLocomotionToolToastRequest } from '../../../../../store/actions/userPreferences';
import { saveLocomotionPenAnalysisRequest } from '../../../../../store/actions/tools/locomotionScore';

const PenAnalysis = props => {
  const {
    currentStep,
    totalToolSteps,
    selectedVisits,
    openToolSheet,
    onDownloadPress,
    onSharePenAnalysisData,
    healthCurrentActivePen,
    isDirty,
    setIsDirty,
    setEnableResults,
    activePenAnalysis,
    setActivePenAnalysis,
  } = props;
  const dispatch = useDispatch();
  const isFocus = useIsFocused();
  const [pensList, setPensList] = useState([]);
  const [accordion, setAccordion] = useState(false);
  const [selectedPen, setSelectedPen] = useState(null);
  const [animalsInPen, setAnimalsInPen] = useState('');
  const [daysInMilk, setDaysInMilk] = useState('');
  const [milkProduction, setMilkProduction] = useState('');
  const [showPenBottomSheet, setShowPenBottomSheet] = useState(false);
  const [initialStateUpdate, setInitialStateUpdate] = useState(false);
  const [totalCowsCount, setTotalCowsCount] = useState(0);

  // const [activePenAnalysis, setActivePenAnalysis] = useState({});
  const [onFocusField, setonFocusField] = useState('');
  const [type, setType] = useState(CONTENT_TYPE.TEXT);
  const [action, setAction] = useState(() => {});

  const visitState = useSelector(state => state.visit);
  const toolState = useSelector(state => state.tool);
  const penState = useSelector(state => state.pen);
  const userPreferencesState = useSelector(state => state.userPreferences);
  const { isEditable = false } = visitState?.visit;
  const penAnalysisData = !stringIsEmpty(visitState?.visit?.locomotionScore)
    ? JSON.parse(visitState?.visit?.locomotionScore)
    : {};

  const unitOfMeasure = visitState.visit.unitOfMeasure;
  const weightUnit = getWeightUnitByMeasure(unitOfMeasure);

  //refs
  const inputReferences = useRef([]);

  //refs
  let totalAnimalsRef = useRef();
  let DIMRef = useRef();
  let milkProductionRef = useRef();

  //helpers
  const focusTotalAnimalsRef = () => {
    totalAnimalsRef?.focus?.();
  };
  const focusDIMRef = () => {
    DIMRef?.focus?.();
  };
  const focusMilkProductionRef = () => {
    milkProductionRef?.focus?.();
  };

  useEffect(() => {
    penHandling();
  }, [visitState?.visit]);

  // useEffect(() => {
  //   initialStateFormPenAnalysis(selectedPen);
  //   penHandling();
  // }, [toolState?.saveLocomotionPenAnalysisSuccess]);

  useEffect(() => {
    if (toolState?.pensList?.length > 0) {
      setPensList(toolState?.pensList);
      // very first time page load 1 pen already selected
      // setSelectedPen(toolState?.pensList[0]);
      if (healthCurrentActivePen) {
        pickPenInReducerFromPensList(
          toolState?.pensList,
          healthCurrentActivePen,
          setSelectedPen,
        );
      } else {
        setSelectedPen(toolState?.pensList[0]);
      }
    }
  }, [toolState.pensList]);

  //saves selected pen in reducer on pen change
  useEffect(() => {
    dispatch(setSelectedPenRequest(selectedPen));
  }, [selectedPen?.id, selectedPen?.sv_id]);

  useEffect(() => {
    const { siteId, localSiteId, locomotionScore } = visitState?.visit;
    const payload = {
      locomotionScore,
      siteId,
      localSiteId,
      toolType: VISIT_TABLE_FIELDS.LOCOMOTION_SCORE,
    };
    // Fetch site  data from database
    dispatch(getSitePensRequest(payload));
  }, []);

  useEffect(() => {
    if (
      !stringIsEmpty(penState?.pen) &&
      Object.keys(penState?.pen).length > 0 &&
      initialStateUpdate
    ) {
      saveLocomotionPenAnalysis(true);
    }
  }, [openToolSheet, currentStep]);

  useEffect(() => {
    if (selectedPen?.id) {
      // Fetch pen data from database
      dispatch(getPenByIdRequest({ id: selectedPen?.id }));
    }
  }, [selectedPen?.id]);

  useEffect(() => {
    debounce_fun(activePenAnalysis);
    setTotalCowsCount(getTotalCowsCountLocomotion(activePenAnalysis));

    return () => saveLocomotionPenAnalysis();
  }, [isFocus, activePenAnalysis, animalsInPen, daysInMilk, milkProduction]);

  //disables results button if pen's values sum to 0
  const debounce_fun = _.debounce(penAnalysisArray => {
    setEnableResults(
      shouldEnableResultsButton(
        TOOL_ANALYSIS_TYPES.PEN_ANALYSIS,
        penAnalysisArray,
      ),
    );
  }, 1000);

  useEffect(() => {
    initialStateFormPenAnalysis(selectedPen);
  }, [penState.pen]);

  useEffect(() => {
    if (penState.updateSuccess) {
      dispatch(resetUpdatePenRequest());
    }
  }, [penState.updateSuccess]);
  useEffect(() => {
    if (!stringIsEmpty(selectedPen)) {
      initialStateFormPenAnalysis(selectedPen);
    }
  }, [selectedPen]);

  const penHandling = () => {
    let animalAnalysis = '';
    if (!stringIsEmpty(visitState?.visit?.animalAnalysis)) {
      animalAnalysis =
        typeof visitState?.visit?.animalAnalysis == 'string'
          ? JSON.parse(visitState?.visit?.animalAnalysis)
          : visitState?.visit?.animalAnalysis;
      let penData = visitState?.visit?.locomotionScore;
      let allPensList = toolState.pensList;
      let savePenData = onSavePenData(allPensList, animalAnalysis, penData);
      if (savePenData) {
        const { id } = visitState?.visit;
        if (isEditable) {
          dispatch(
            saveLocomotionPenAnalysisRequest({
              locomotionScoreData: savePenData,
              localVisitId: id,
              updated_at: dateHelper.getUnixTimestamp(new Date()),
            }),
          );
        }
      }
    }
  };

  const initialStateFormPenAnalysis = async selectedPen => {
    if (
      (!stringIsEmpty(penState?.pen) &&
        Object.keys(penState?.pen).length > 0) ||
      (!stringIsEmpty(selectedPen) && selectedPen?.id)
    ) {
      const pen = penState?.pen || selectedPen;
      let visitData = visitState.visit?.locomotionScore;
      let selectedVisitPen = null;
      if (!stringIsEmpty(visitData)) {
        visitData = JSON.parse(visitData);

        selectedVisitPen = visitData?.pens?.find(
          x => x.penId == selectedPen?.id || x.penId == selectedPen?.sv_id,
        );
      }
      if (!isEditable && !stringIsEmpty(selectedVisitPen)) {
        setAnimalsInPen(selectedVisitPen?.totalAnimalsInPen);
        setDaysInMilk(
          stringIsEmpty(selectedVisitPen?.daysInMilk)
            ? ''
            : selectedVisitPen?.daysInMilk,
        );
        let milkVal = selectedVisitPen?.milkProductionInKg;
        if (unitOfMeasure === UNIT_OF_MEASURE.IMPERIAL) {
          milkVal = convertWeightToImperial(milkVal, 1);
        }
        setMilkProduction(getFormattedDecimalValues(milkVal));
      } else if (isEditable) {
        setAnimalsInPen(pen?.animals);
        setDaysInMilk(stringIsEmpty(pen?.daysInMilk) ? '' : pen?.daysInMilk);
        let milkVal = pen?.milk;

        if (unitOfMeasure === UNIT_OF_MEASURE.IMPERIAL) {
          milkVal = convertWeightToImperial(milkVal, 1);
        }
        setMilkProduction(getFormattedDecimalValues(milkVal));
      }
      let animalAnalysis = '';
      if (
        !stringIsEmpty(visitState?.visit?.animalAnalysis) &&
        stringIsEmpty(visitState?.visit?.locomotionScore)
      ) {
        //CDEA-2436: type of visitState?.visit?.animalAnalysis is object when filters are applied
        if (typeof visitState?.visit?.animalAnalysis === 'string') {
          animalAnalysis = JSON.parse(visitState?.visit?.animalAnalysis);
        } else {
          animalAnalysis = visitState?.visit?.animalAnalysis;
        }
        let savePensData = getPensWithAnimalObserved(animalAnalysis, pensList);
        onSavePenAnalysisDataWithAnimalObserved(savePensData);
      }
      let isPen = allPenAnalysis(visitState?.visit);
      setActivePenAnalysis(getSelectedPen(isPen, pen, animalAnalysis));
      setInitialStateUpdate(true);
    }
  };

  const saveLocomotionPenAnalysis = (isForce = false) => {
    if (isDirty) {
      setIsDirty(false);
      activePenAnalysis.totalAnimalsInPen = parseFloat(animalsInPen);
      activePenAnalysis.daysInMilk = parseFloat(daysInMilk);
      activePenAnalysis.milkProductionInKg =
        unitOfMeasure === UNIT_OF_MEASURE.IMPERIAL
          ? convertWeightToMetric(convertStringToNumber(milkProduction))
          : parseFloat(convertStringToNumber(milkProduction));
      const { id } = visitState?.visit;
      let obj = {
        visitData: visitState?.visit,
        data: activePenAnalysis,
        localVisitId: id,
      };
      if (isEditable) {
        let onSaveLocomotionData = onUpdatedLocomotionScoreObj(obj);
        dispatch(
          saveLocomotionPenAnalysisRequest({
            locomotionScoreData: onSaveLocomotionData,
            localVisitId: id,
            updated_at: dateHelper.getUnixTimestamp(new Date()),
            mobileLastUpdatedTime: dateHelper.getUnixTimestamp(new Date()),
          }),
        );
      }
    }
  };

  const onPenSetupUpdated = () => {
    let onUpdatedPenObj = onUpdatePen(
      animalsInPen,
      daysInMilk,
      unitOfMeasure === UNIT_OF_MEASURE.IMPERIAL
        ? convertWeightToMetric(convertStringToNumber(milkProduction))
        : convertStringToNumber(milkProduction),
      penState.pen,
    );

    dispatch(updatePenRequest(onUpdatedPenObj));
  };

  // Animal observed click decrement btn when pen%  animals in per pen and animal milk loss calculated at run time
  const onDecrementClick = index => {
    setIsDirty(true);
    let categoryListState = [...activePenAnalysis?.categories];
    categoryListState[index].animalsObserved -= 1;
    onCategoriesListStateUpdate(categoryListState, index);
    setTotalCowsCount(totalCowsCount - 1);
  };

  // Animal observed click increment btn when pen%  animals in per pen and animal milk loss calculated at run time
  const onIncrementClick = index => {
    setIsDirty(true);
    let categoryListState = [...activePenAnalysis?.categories];
    categoryListState[index].animalsObserved += 1;
    onCategoriesListStateUpdate(categoryListState, index);
    setTotalCowsCount(totalCowsCount + 1);
  };

  const onAnimalCountChange = (index, value) => {
    setIsDirty(true);
    let categoryListState = [...activePenAnalysis?.categories];
    let _categoryCount = categoryListState[index].animalsObserved; //used below
    categoryListState[index].animalsObserved = parseInt(value);
    onCategoriesListStateUpdate(categoryListState, index);

    let _totalCowsCount = totalCowsCount - _categoryCount;
    _totalCowsCount += parseInt(value);
    setTotalCowsCount(_totalCowsCount);
  };

  const onCategoriesListStateUpdate = (categoryListState, index) => {
    try {
      setActivePenAnalysis(prevState => ({
        ...prevState,
        categories: categoryListState,
      }));
    } catch (error) {
      logEvent(
        'components -> Locomotion -> PenAnalysis -> onCategoriesListStateUpdate Exception',
        error,
      );
      console.log('onCategoriesListStateUpdate Exception', error);
    }
  };

  const openPenBottomSheet = () => {
    setShowPenBottomSheet(true);
  };

  const closePenBottomSheet = () => {
    setShowPenBottomSheet(false);
  };

  const onPenChange = item => {
    saveSelectedPenInReducer(dispatch, item);
    saveLocomotionPenAnalysis(true);
    setIsDirty(false);
    setEnableResults(false);
    setSelectedPen(item);
    closePenBottomSheet();
    setAnimalsInPen('');
    setDaysInMilk('');
    setMilkProduction('');
    initialStateFormPenAnalysis(item);
  };

  // user manually change animal in per pen form pen setup calculate run time pens% and animal in per pen
  const onChangeAnimalInPerPen = value => {
    value = removeStringCommas(value);
    if (value?.length > 1 && value.indexOf('0') === 0) {
      value = value.replace('0', '');
    }
    if (onValueChange(value, 0, 9999, true, 0)) {
      // setAnimalsInPen(Number(value));
      setAnimalsInPen(value);
      setIsDirty(true);

      let stateCategoryList = activePenAnalysis?.categories;
      stateCategoryList.map(
        a => (a.animalsInPen = ((Number(a.pens) * value) / 100).toFixed(3)),
      );
      setActivePenAnalysis(prevState => ({
        ...prevState,
        categories: stateCategoryList,
      }));
    }
  };

  const onChangeMilkProduction = e => {
    if (onValueChange(e, 0, 999, false, 1)) {
      // setMilkProduction(Number(e));
      setMilkProduction(e);
      setIsDirty(true);
    }
  };

  const onChangeDaysInMilk = e => {
    if (onValueChange(e, -100, 999, true, 0, true)) {
      // setDaysInMilk(Number(e));
      setDaysInMilk(e);
      setIsDirty(true);
    }
  };

  const onBlurInput = (newValue, oldValue) => {
    if (newValue != oldValue) {
      onPenSetupUpdated();
    }
  };

  const onFocusFieldUpdate = field => {
    if (stringIsEmpty(onFocusField)) {
      setonFocusField(field);
    }
  };

  const onSavePenAnalysisDataWithAnimalObserved = savePensData => {
    let obj = {
      localVisitId: visitState?.visit?.id,
      visitId: visitState?.visit?.sv_id,
      pens: savePensData,
    };
    dispatch(
      saveLocomotionPenAnalysisRequest({
        locomotionScoreData: obj,
        localVisitId: visitState?.visit?.id,
        updated_at: dateHelper.getUnixTimestamp(new Date()),
        mobileLastUpdatedTime: dateHelper.getUnixTimestamp(new Date()),
      }),
    );
  };

  const renderPenAnalysisResults = () => {
    const { id, visitDate } = visitState.visit;
    return (
      <PenAnalysisGraph
        selectedPen={selectedPen}
        penData={{ ...activePenAnalysis, visitId: id, date: visitDate }}
        selectedVisits={selectedVisits}
        onDownloadPress={onDownloadPress}
        onSharePenAnalysisData={onSharePenAnalysisData}
      />
    );
  };

  const renderAnimalInPen = (index, categories, animalsInPen) => {
    let getAnimalsInPen = 0;
    getAnimalsInPen = animalInPerPen(index, categories, animalsInPen);
    if (getAnimalsInPen > 0) {
      getAnimalsInPen = Number(getAnimalsInPen).toFixed(2);
      return getAnimalsInPen;
    }
    return '-';
  };

  const onCloseToast = () => {
    dispatch(hideLocomotionToolToastRequest());
  };

  const getToolToast = () => {
    const {
      userPreferences: { defaultValues },
    } = userPreferencesState || {};
    const { [TOOL_TYPES.LOCOMOTION_SCORE]: locomotionPenToast } =
      defaultValues || false;
    return isEditable ? locomotionPenToast : false;
  };

  return (
    <>
      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        // behavior={Platform.OS === 'ios' ? 'position' : 'padding'}
        behavior="position"
        contentContainerStyle={{ flexGrow: 1 }}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 10 : -120}>
        {currentStep === totalToolSteps ? (
          renderPenAnalysisResults()
        ) : (
          <View style={styles.flexOne}>
            <CustomInputAccessoryView doneAction={action} type={type} />
            <ScrollView
              keyboardDismissMode="on-drag"
              keyboardShouldPersistTaps="handled"
              showsVerticalScrollIndicator={false}>
              <View style={styles.container}>
                <View style={styles.penNameRow}>
                  <TouchableOpacity
                    style={styles.dropdownTextContainer}
                    onPress={openPenBottomSheet}>
                    <Text style={styles.dropdownText}>
                      {selectedPen?.name || ''}
                    </Text>
                    <CHEVRON_DOWN_BLUE_ICON
                      width={normalize(12)}
                      height={normalize(8)}
                    />
                  </TouchableOpacity>
                  <View style={styles.totalCowsCountView}>
                    <Text style={styles.totalCowsCountText}>
                      {`${i18n.t('animalsObserved')}: ${totalCowsCount}`}
                    </Text>
                  </View>
                </View>
                {Object.keys(activePenAnalysis)?.length > 0 &&
                  activePenAnalysis?.categories?.map((item, index) => {
                    return (
                      <View key={index}>
                        <View style={styles.listView}>
                          <Text style={styles.catName}>
                            {i18n.t('cat')} {item.category.toFixed(1)}
                          </Text>
                        </View>
                        <View style={styles.listChildView}>
                          <View style={styles.animalsInPen}>
                            <View style={styles.listHeaderView}>
                              <Text style={styles.listHeaderText}>
                                {i18n.t('animalInPen')}
                              </Text>
                            </View>
                            <View style={styles.listValue}>
                              <Text style={styles.listViewText}>
                                {convertInputNumbersToRegionalBasis(
                                  renderAnimalInPen(
                                    index,
                                    activePenAnalysis?.categories,
                                    animalsInPen,
                                  ),
                                  2,
                                  true,
                                )}
                              </Text>
                            </View>
                          </View>
                          <View style={styles.animalsInPen}>
                            <View style={styles.listHeaderView}>
                              <Text style={styles.listHeaderText}>
                                {i18n.t('pen')} {i18n.t('%')}
                              </Text>
                            </View>
                            <View style={styles.listValue}>
                              <Text style={styles.listViewText}>
                                {convertInputNumbersToRegionalBasis(
                                  pensInPercent(
                                    index,
                                    activePenAnalysis?.categories,
                                  ),
                                )}
                              </Text>
                            </View>
                          </View>
                          <View style={styles.animalObservedView}>
                            <View style={styles.listHeaderView}>
                              <Text style={styles.listHeaderText}>
                                {i18n.t('animalObserved')}
                              </Text>
                            </View>
                            <View>
                              <CircularCounter
                                disabled={!isEditable}
                                count={
                                  penExistsInPublishedVisit(
                                    isEditable,
                                    penAnalysisData,
                                    activePenAnalysis,
                                    (forLocomotion = true),
                                  )
                                    ? activePenAnalysis?.categories[index]
                                        ?.animalsObserved || 0
                                    : '-'
                                }
                                onDecrementClick={() => onDecrementClick(index)}
                                onIncrementClick={() => onIncrementClick(index)}
                                onChangeText={count =>
                                  onAnimalCountChange(index, count)
                                }
                                blurOnSubmit={false}
                                showInput={true}
                                reference={input => {
                                  inputReferences.current[index] = input;
                                }}
                                onSubmitEditing={() => {
                                  if (
                                    index <
                                    inputReferences.current.length - 1
                                  ) {
                                    inputReferences?.current[
                                      index + 1
                                    ]?.focus();
                                  } else {
                                    focusTotalAnimalsRef();
                                  }
                                }}
                                inputAccessoryViewID="customInputAccessoryView"
                                returnKeyType={NEXT_FIELD_TEXT.NEXT}
                                onFocus={() => {
                                  setType(CONTENT_TYPE.NUMBER);
                                  setAction({
                                    currentRef:
                                      index < inputReferences.current.length - 1
                                        ? inputReferences?.current[index + 1]
                                        : totalAnimalsRef,
                                  });
                                }}
                              />
                            </View>
                          </View>
                        </View>
                      </View>
                    );
                  })}

                <View style={styles.penSetupContainer}>
                  <View style={styles.penSetupView}>
                    <Text style={styles.penSetupText}>
                      {i18n.t('fromPenSetup')}
                    </Text>
                  </View>

                  <View style={styles.penSetupChildContainer}>
                    <View style={styles.penViewChildContainer}>
                      <View style={styles.justifyCenter}>
                        <Text
                          style={[
                            styles.penChildText,
                            styles.penChildTextWidth,
                          ]}>
                          {i18n.t('totalAnimalsInPen')}
                        </Text>
                      </View>
                      <NumberFormInput
                        disabled={!isEditable}
                        placeholder="0"
                        onBlur={() =>
                          onBlurInput(animalsInPen, penState?.pen?.animals)
                        }
                        blurOnSubmit={false}
                        keyboardType={KEYBOARD_TYPE.NUMBER_PAD}
                        // value={getFormattedCommaNumber(
                        //   animalsInPen?.toString(),
                        // )}
                        value={animalsInPen?.toString()}
                        hasCommas={true}
                        isInteger={true}
                        onChange={onChangeAnimalInPerPen}
                        style={styles.input}
                        reference={input => (totalAnimalsRef = input)}
                        inputAccessoryViewID="customInputAccessoryView"
                        returnKeyType={NEXT_FIELD_TEXT.NEXT}
                        onFocus={() => {
                          setType(CONTENT_TYPE.NUMBER);
                          onFocusFieldUpdate(i18n.t('totalAnimalsInPen'));
                          setAction({
                            currentRef: DIMRef,
                          });
                        }}
                        onSubmitEditing={() => {
                          // Keyboard?.dismiss();
                          focusDIMRef();
                        }}
                      />
                    </View>

                    <View style={styles.penViewChildContainer}>
                      <View style={styles.justifyCenter}>
                        <Text style={styles.penChildText}>{i18n.t('DIM')}</Text>
                      </View>
                      <NumberFormInput
                        disabled={!isEditable}
                        keyboardType={
                          Platform.OS === 'ios'
                            ? KEYBOARD_TYPE.NUMBERS_AND_PUNCTUATION
                            : KEYBOARD_TYPE.NUMBER_PAD
                        }
                        onBlur={() =>
                          onBlurInput(daysInMilk, penState?.pen?.daysInMilk)
                        }
                        placeholder="0"
                        blurOnSubmit={false}
                        onChange={onChangeDaysInMilk}
                        minValue={-100}
                        maxValue={999}
                        isNegative={true}
                        isInteger={true}
                        // value={daysInMilk?.toString()}
                        value={daysInMilk?.toString()}
                        style={styles.input}
                        reference={input => {
                          DIMRef = input;
                        }}
                        onSubmitEditing={() => {
                          focusMilkProductionRef();
                        }}
                        inputAccessoryViewID="customInputAccessoryView"
                        returnKeyType={NEXT_FIELD_TEXT.NEXT}
                        onFocus={() => {
                          setType(CONTENT_TYPE.NUMBER);
                          onFocusFieldUpdate(i18n.t('DIM'));
                          setAction({
                            currentRef: milkProductionRef,
                          });
                        }}
                      />
                    </View>

                    <View style={styles.penViewChildContainer}>
                      <View style={styles.justifyCenter}>
                        <Text style={styles.penChildText}>
                          {`${i18n.t('milkProduction')} (${weightUnit})`}
                        </Text>
                      </View>
                      <NumberFormInput
                        keyboardType={KEYBOARD_TYPE.DECIMAL}
                        disabled={!isEditable}
                        placeholder={i18n.t('singleDecimalNumberPlaceholder')}
                        onBlur={() =>
                          onBlurInput(
                            milkProduction,
                            unitOfMeasure === UNIT_OF_MEASURE.IMPERIAL
                              ? convertWeightToImperial(penState?.pen?.milk, 1)
                              : penState?.pen?.milk,
                          )
                        }
                        // onFocus={() =>
                        //   onFocusFieldUpdate(i18n.t('milkProduction'))
                        // }
                        blurOnSubmit={false}
                        minValue={0}
                        maxValue={999}
                        onChange={onChangeMilkProduction}
                        // value={milkProduction.toString()}
                        value={milkProduction?.toString()}
                        decimalPoints={1}
                        style={styles.input}
                        reference={input => {
                          milkProductionRef = input;
                        }}
                        onSubmitEditing={() => {
                          Keyboard?.dismiss();
                        }}
                        inputAccessoryViewID="customInputAccessoryView"
                        returnKeyType={NEXT_FIELD_TEXT.DONE}
                        onFocus={() => {
                          setType(CONTENT_TYPE.NUMBER);
                          setAction({
                            dismiss: true,
                          });
                        }}
                      />
                    </View>
                    <View style={styles.penViewChildContainer}>
                      <View style={styles.milkLossViewHeader}>
                        <Text style={styles.penChildText}>
                          {i18n.t('milkLoss')}{' '}
                        </Text>
                        <Text style={styles.penChildText}>({weightUnit})</Text>
                      </View>
                      <View style={styles.milkLossView}>
                        <Text style={styles.milkLossText}>
                          {convertInputNumbersToRegionalBasis(
                            calculateMilkLoss(
                              activePenAnalysis?.categories,
                              milkProduction,
                            ),
                          )}
                        </Text>
                      </View>
                    </View>
                  </View>
                </View>

                <View style={styles.accordionView}>
                  <TouchableOpacity
                    onPress={() => setAccordion(!accordion)}
                    style={[
                      styles.accordionButton,
                      !accordion ? styles.accordionClose : styles.accordionOpen,
                    ]}>
                    {!accordion ? (
                      <AccordionInActiveIcon />
                    ) : (
                      <AccordionActiveIcon />
                    )}

                    <Text style={styles.accordionText}>
                      {i18n.t('referenceTable')}
                    </Text>
                  </TouchableOpacity>

                  <View
                    style={[
                      styles.marginHorizontal,
                      {
                        display: accordion ? 'flex' : 'none',
                      },
                    ]}>
                    <View style={styles.tableContainer}>
                      <View style={styles.tableHeaderViewContainer}>
                        <View style={styles.tableLeftView}>
                          <Text style={styles.tableHeaderText}>
                            {i18n.t('cat')}
                          </Text>
                        </View>
                        <View style={styles.tableRightView}>
                          <Text style={styles.tableHeaderRightText}>
                            {i18n.t('%')} {i18n.t('lossCow')}
                          </Text>
                        </View>
                      </View>
                      {Object.keys(activePenAnalysis).length > 0 &&
                        activePenAnalysis?.categories?.map((item, index) => (
                          <View
                            key={index}
                            style={styles.tableHeaderViewChildContainer}>
                            <View style={styles.tableLeftView}>
                              <Text style={styles.tableLeftRowText}>
                                {item.category.toFixed(1)}
                              </Text>
                            </View>
                            <View style={styles.tableRightView}>
                              <Text style={styles.tableRightRowText}>
                                {!stringIsEmpty(
                                  LOCOMOTION_CATEGORY_LIST[index].lossCow,
                                )
                                  ? `${convertInputNumbersToRegionalBasis(
                                      LOCOMOTION_CATEGORY_LIST[index].lossCow,
                                    )} ${i18n.t('%')}`
                                  : '-'}
                              </Text>
                            </View>
                          </View>
                        ))}
                    </View>
                  </View>
                </View>
              </View>
              {showPenBottomSheet && (
                <CustomBottomSheet
                  type={BOTTOM_SHEET_TYPE.SIMPLE}
                  selectLabel={i18n.t('selectPen')}
                  searchPlaceHolder={i18n.t('searchPen')}
                  data={pensList}
                  onChange={onPenChange}
                  onClose={closePenBottomSheet}
                />
              )}
            </ScrollView>
          </View>
        )}
      </KeyboardAvoidingView>
      {!!getToolToast() && (
        <View style={styles.alert}>
          <ToolAlert onCloseToast={onCloseToast} />
        </View>
      )}
    </>
  );
};
export default PenAnalysis;
