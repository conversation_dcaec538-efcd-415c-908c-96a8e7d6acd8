// modules
import { useState, useEffect, useRef } from 'react';
import { View, Platform, ScrollView, KeyboardAvoidingView } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

//lodash
import _ from 'lodash';

// Reusable Components
import FromPenSetup from './FromPenSetup';
import ToolAlert from '../../common/ToolAlert';
import PenCategoriesList from './PenCategories';
import PenAnalysisGraph from './ResultPenAnalysis';
import PenSelectionAndCount from './PenSelectionHeader';
import CategoriesReferenceTable from './ReferenceTable';

// styles
import styles from './styles';

// helpers
import {
  convertStringToNumber,
  getFormattedDecimalValues,
  stringIsEmpty,
} from '../../../../../helpers/alphaNumericHelper';
import {
  getSelectedPen,
  onUpdatedLocomotionScoreObj,
  allPenAnalysis,
  onSavePenData,
  shouldEnableResultsButton,
  getPensWithAnimalObserved,
  getTotalCowsCountLocomotion,
  replaceSelectedPenInLocomotionData,
} from '../../../../../helpers/locomotionHelper';
import { dateHelper } from '../../../../../helpers/dateHelper';
import { pickPenInReducerFromPensList } from '../../../../../helpers/visitHelper';
import {
  convertWeightToImperial,
  convertWeightToMetric,
} from '../../../../../helpers/appSettingsHelper';

// actions
import {
  getPenByIdRequest,
  resetUpdatePenRequest,
} from '../../../../../store/actions/pen';
import {
  getSitePensRequest,
  setSelectedPenRequest,
} from '../../../../../store/actions/tool';

// constants
import {
  TOOL_TYPES,
  UNIT_OF_MEASURE,
  TOOL_ANALYSIS_TYPES,
  VISIT_TABLE_FIELDS,
} from '../../../../../constants/AppConstants';
import { hideLocomotionToolToastRequest } from '../../../../../store/actions/userPreferences';
import {
  saveLocomotionPenAnalysisRequest,
  setSelectedLocomotionPenRequest,
} from '../../../../../store/actions/tools/locomotionScore';

const PenAnalysis = props => {
  const {
    currentStep,
    totalToolSteps,
    selectedVisits,
    openToolSheet,
    healthCurrentActivePen,
    isDirty,
    setIsDirty,
    setEnableResults,
    setActivePenAnalysis,
  } = props;
  const dispatch = useDispatch();

  const [animalsInPen, setAnimalsInPen] = useState('');
  const [daysInMilk, setDaysInMilk] = useState('');
  const [milkProduction, setMilkProduction] = useState('');
  const [initialStateUpdate, setInitialStateUpdate] = useState(false);

  const visitState = useSelector(state => state.visit.visit);
  const selectedPen = useSelector(state => state.locomotionScore.selectedPen);
  const pensList = useSelector(state => state.tool.pensList);
  const penState = useSelector(state => state.pen);
  const locomotionData = useSelector(
    state => state.locomotionScore.locomotionToolData,
  );
  const userPreferencesState = useSelector(state => state.userPreferences);
  const { isEditable = false, unitOfMeasure, id } = visitState;

  //refs
  const totalAnimalsRef = useRef();

  // useEffect(() => {
  //   penHandling();
  // }, [visitState?.visit]);

  useEffect(() => {
    if (pensList?.length > 0) {
      // setPensList(toolState?.pensList);
      // very first time page load 1 pen already selected
      // setSelectedPen(toolState?.pensList[0]);
      if (healthCurrentActivePen) {
        const pen = pickPenInReducerFromPensList(
          pensList,
          healthCurrentActivePen,
          null,
          null,
          true,
        );
        dispatch(setSelectedLocomotionPenRequest(pen));
      } else {
        // setSelectedPen(toolState?.pensList[0]);
      }
    }
  }, [pensList]);

  //saves selected pen in reducer on pen change
  useEffect(() => {
    dispatch(setSelectedPenRequest(selectedPen));
  }, [selectedPen?.id, selectedPen?.sv_id, selectedPen?.penId]);

  // useEffect(() => {
  //   if (
  //     !stringIsEmpty(penState?.pen) &&
  //     Object.keys(penState?.pen).length > 0 &&
  //     initialStateUpdate
  //   ) {
  //     saveLocomotionPenAnalysis(true);
  //   }
  // }, [openToolSheet, currentStep]);

  // useEffect(() => {
  //   if (selectedPen?.id) {
  //     // Fetch pen data from database
  //     dispatch(getPenByIdRequest({ id: selectedPen?.id }));
  //   }
  // }, [selectedPen?.id]);

  useEffect(() => {
    return () => {
      saveLocomotionPenAnalysis();
    };
  }, [isDirty]);

  useEffect(() => {
    debounce_fun(selectedPen);

    // return () => saveLocomotionPenAnalysis();
  }, [selectedPen]);

  // disables results button if pen's values sum to 0
  const debounce_fun = _.debounce(penAnalysisArray => {
    setEnableResults(
      shouldEnableResultsButton(
        TOOL_ANALYSIS_TYPES.PEN_ANALYSIS,
        penAnalysisArray,
      ),
    );
  }, 1000);

  // useEffect(() => {
  //   initialStateFormPenAnalysis(selectedPen);
  // }, [penState.pen]);

  useEffect(() => {
    if (penState.updateSuccess) {
      dispatch(resetUpdatePenRequest());
    }
  }, [penState.updateSuccess]);

  // useEffect(() => {
  //   if (!stringIsEmpty(selectedPen)) {
  //     initialStateFormPenAnalysis(selectedPen);
  //   }
  // }, [selectedPen]);

  const penHandling = () => {
    let animalAnalysis = '';
    if (!stringIsEmpty(visitState?.animalAnalysis)) {
      animalAnalysis =
        typeof visitState?.animalAnalysis == 'string'
          ? JSON.parse(visitState?.animalAnalysis)
          : visitState?.animalAnalysis;
      let penData = visitState?.locomotionScore;
      let allPensList = pensList;
      let savePenData = onSavePenData(allPensList, animalAnalysis, penData);
      if (savePenData) {
        const { id } = visitState;
        if (isEditable) {
          dispatch(
            saveLocomotionPenAnalysisRequest({
              locomotionScoreData: savePenData,
              localVisitId: id,
              updated_at: dateHelper.getUnixTimestamp(new Date()),
            }),
          );
        }
      }
    }
  };

  const initialStateFormPenAnalysis = async selectedPen => {
    if (
      (!stringIsEmpty(penState?.pen) &&
        Object.keys(penState?.pen).length > 0) ||
      (!stringIsEmpty(selectedPen) && selectedPen?.id)
    ) {
      const pen = penState?.pen || selectedPen;
      let visitData = visitState?.locomotionScore;
      let selectedVisitPen = null;
      if (!stringIsEmpty(visitData)) {
        visitData = JSON.parse(visitData);

        selectedVisitPen = visitData?.pens?.find(
          x => x.penId == selectedPen?.id || x.penId == selectedPen?.sv_id,
        );
      }
      if (!isEditable && !stringIsEmpty(selectedVisitPen)) {
        setAnimalsInPen(selectedVisitPen?.totalAnimalsInPen);
        setDaysInMilk(
          stringIsEmpty(selectedVisitPen?.daysInMilk)
            ? ''
            : selectedVisitPen?.daysInMilk,
        );
        let milkVal = selectedVisitPen?.milkProductionInKg;
        if (unitOfMeasure === UNIT_OF_MEASURE.IMPERIAL) {
          milkVal = convertWeightToImperial(milkVal, 1);
        }
        setMilkProduction(getFormattedDecimalValues(milkVal));
      } else if (isEditable) {
        setAnimalsInPen(pen?.animals);
        setDaysInMilk(stringIsEmpty(pen?.daysInMilk) ? '' : pen?.daysInMilk);
        let milkVal = pen?.milk;

        if (unitOfMeasure === UNIT_OF_MEASURE.IMPERIAL) {
          milkVal = convertWeightToImperial(milkVal, 1);
        }
        setMilkProduction(getFormattedDecimalValues(milkVal));
      }
      let animalAnalysis = '';
      if (
        !stringIsEmpty(visitState?.animalAnalysis) &&
        stringIsEmpty(visitState?.locomotionScore)
      ) {
        //CDEA-2436: type of visitState?.visit?.animalAnalysis is object when filters are applied
        if (typeof visitState?.animalAnalysis === 'string') {
          animalAnalysis = JSON.parse(visitState?.animalAnalysis);
        } else {
          animalAnalysis = visitState?.animalAnalysis;
        }
        let savePensData = getPensWithAnimalObserved(animalAnalysis, pensList);
        onSavePenAnalysisDataWithAnimalObserved(savePensData);
      }
      let isPen = allPenAnalysis(visitState);
      console.log(
        'getSelectedPen(isPen, pen, animalAnalysis)',
        getSelectedPen(isPen, pen, animalAnalysis),
      );
      setActivePenAnalysis(getSelectedPen(isPen, pen, animalAnalysis));
      setInitialStateUpdate(true);
    }
  };

  const saveLocomotionPenAnalysis = () => {
    console.log('is dirty pen analysis--', isDirty);
    if (isDirty) {
      const payload = {
        ...selectedPen,
        milkProductionInKg:
          unitOfMeasure === UNIT_OF_MEASURE.IMPERIAL
            ? convertWeightToMetric(
                convertStringToNumber(selectedPen?.milkProductionInKg),
              )
            : parseFloat(
                convertStringToNumber(selectedPen?.milkProductionInKg),
              ),
      };

      // let obj = {
      //   visitData: visitState,
      //   data: payload,
      //   localVisitId: id,
      // };
      // console.log('obj saveLocomotionPenAnalysis', obj);
      if (isEditable) {
        // let onSaveLocomotionData = onUpdatedLocomotionScoreObj(obj);
        const onSaveLocomotionData = replaceSelectedPenInLocomotionData(
          payload,
          locomotionData,
        );
        console.log('onSaveLocomotionData', onSaveLocomotionData);
        dispatch(
          saveLocomotionPenAnalysisRequest({
            locomotionScoreData: onSaveLocomotionData,
            localVisitId: id,
            updated_at: dateHelper.getUnixTimestamp(new Date()),
            mobileLastUpdatedTime: dateHelper.getUnixTimestamp(new Date()),
          }),
        );
      }
      setIsDirty(false);
    }
  };

  const onSavePenAnalysisDataWithAnimalObserved = savePensData => {
    let obj = {
      localVisitId: visitState?.id,
      visitId: visitState?.sv_id,
      pens: savePensData,
    };
    dispatch(
      saveLocomotionPenAnalysisRequest({
        locomotionScoreData: obj,
        localVisitId: visitState?.id,
        updated_at: dateHelper.getUnixTimestamp(new Date()),
        mobileLastUpdatedTime: dateHelper.getUnixTimestamp(new Date()),
      }),
    );
  };

  const renderPenAnalysisResults = () => {
    const { id, visitDate } = visitState;
    return (
      <PenAnalysisGraph
        selectedVisits={selectedVisits}
        penData={{ ...selectedPen, visitId: id, date: visitDate }}
      />
    );
  };

  const onCloseToast = () => {
    dispatch(hideLocomotionToolToastRequest());
  };

  const getToolToast = () => {
    const {
      userPreferences: { defaultValues },
    } = userPreferencesState || {};
    const { [TOOL_TYPES.LOCOMOTION_SCORE]: locomotionPenToast } =
      defaultValues || false;
    return isEditable ? locomotionPenToast : false;
  };

  return (
    <>
      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        contentContainerStyle={{ flexGrow: 1 }}
        behavior={Platform.OS === 'ios' ? 'position' : 'padding'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 10 : -120}>
        {currentStep === totalToolSteps ? (
          renderPenAnalysisResults()
        ) : (
          <View style={styles.flexOne}>
            <ScrollView
              keyboardDismissMode="on-drag"
              keyboardShouldPersistTaps="handled"
              showsVerticalScrollIndicator={false}>
              <View style={styles.container}>
                <PenSelectionAndCount setIsDirty={setIsDirty} />

                <PenCategoriesList
                  setIsDirty={setIsDirty}
                  totalAnimalsRef={totalAnimalsRef}
                />

                <FromPenSetup
                  setIsDirty={setIsDirty}
                  totalAnimalsRef={totalAnimalsRef}
                />

                <CategoriesReferenceTable />
              </View>
            </ScrollView>
          </View>
        )}
      </KeyboardAvoidingView>
      {!!getToolToast() && (
        <View style={styles.alert}>
          <ToolAlert onCloseToast={onCloseToast} />
        </View>
      )}
    </>
  );
};
export default PenAnalysis;
