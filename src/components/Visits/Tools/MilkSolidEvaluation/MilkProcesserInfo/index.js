import React from 'react';
import { View, TouchableOpacity, Text } from 'react-native';

// localization
import i18n from '../../../../../localization/i18n';

//styles
import styles from './styles';
import { TRASH_ICON } from '../../../../../constants/AssetSVGConstants';
import { normalize } from '../../../../../constants/theme/variables/customFont';

const MilkProcessorInformation = ({
  addPickup,
  pickups,
  onDeletePress,
  isEditable = false,
}) => {
  return (
    <View style={styles.parent}>
      <View style={styles.container}>
        <Text style={styles.formHeading}>{i18n.t('milkProcessorInfo')}</Text>
      </View>

      {/* View pickup here */}

      {pickups?.map((item, index) => (
        <View style={styles.pickupList}>
          <TouchableOpacity
            onPress={() => addPickup(item, true, index)}
            style={styles.flexOne}>
            <Text style={styles.textPick}>{`${i18n.t('pickup')} ${
              index + 1
            }`}</Text>
          </TouchableOpacity>
          <View style={styles.flexOne}>
            <TouchableOpacity
              onPress={() => onDeletePress(item, index)}
              disabled={!isEditable}
              style={styles.iconContainer}>
              <TRASH_ICON width={normalize(14)} height={normalize(16)} />
            </TouchableOpacity>
          </View>
        </View>
      ))}

      <TouchableOpacity disabled={!isEditable} onPress={addPickup}>
        <View
          style={[
            styles.buttonContainer,
            !isEditable && styles.disabledButton,
          ]}>
          <Text style={[styles.plusIcon, !isEditable && styles.disabledText]}>
            {i18n.t('plusSign')}
          </Text>
          <Text
            style={[styles.createPenText, !isEditable && styles.disabledText]}>
            {i18n.t('addPickup')}
          </Text>
        </View>
      </TouchableOpacity>
    </View>
  );
};

export default MilkProcessorInformation;
