import fonts, {
  normalize,
} from '../../../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../../../constants/theme/variables/customColor';

export default {
  customButton: {
    marginHorizontal: normalize(20),
    backgroundColor: colors.white,
    borderWidth: normalize(1),
    borderColor: colors.primaryMain,
    borderStyle: 'dashed',
    width: null,
    marginTop: normalize(8),
    marginBottom: normalize(20),
  },
  labelContainer: {
    flexDirection: 'row',
    paddingTop: Platform.select({ ios: normalize(5) }),
  },
  plusIcon: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(19),
    lineHeight: normalize(16),
    color: colors.primaryMain,
    paddingTop: normalize(2),
    marginRight: normalize(7),
  },
  createPenText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '400',
    fontSize: normalize(14),
    lineHeight: normalize(18),
    letterSpacing: 0.2,
    color: colors.primaryMain,
  },
  disabledButton: {
    borderColor: colors.grey2,
  },
  disabledText: {
    color: colors.grey2,
  },
};
