import fonts, {
  normalize,
} from '../../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../../constants/theme/variables/customColor';

export default {
  container: {
    flexDirection: 'column',
  },
  formInputStyle: {
    width: normalize(150),
  },
  categoryHeader: {
    marginTop: normalize(13),
  },
  headerText: {
    fontSize: normalize(16),
    fontWeight: '500',
    fontFamily: fonts.HelveticaNeueRegular,
    color: colors.primaryMain,
    lineHeight: normalize(16),
    letterSpacing: 0.2,
  },
  numberInputContainerStyles: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    // borderWidth: 2,
    marginBottom: normalize(20),
  },
  numberInputStyle: {
    marginRight: normalize(-10),
    width: normalize(150),
    borderRadius: normalize(4),
    borderWidth: normalize(1),
    borderColor: colors.grey4,
  },
  numberInputFieldLabel: {
    fontWeight: '400',
    fontSize: normalize(14),
    lineHeight: normalize(22),
    letterSpacing: 0.2,
    color: colors.grey1,
    paddingLeft: normalize(5),
    // textTransform: 'capitalize',
  },
};
