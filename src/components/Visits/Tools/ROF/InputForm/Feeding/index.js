// modules
import React from 'react';
import { Keyboard, Platform, View } from 'react-native';
import { useFormikContext } from 'formik';
import { useDispatch } from 'react-redux';

import NumberFormInput from '../../../../../common/NumberFormInput';
import FeedingIngredientsForm from './FeedingIngredientsForm';

import styles from './styles';

import i18n from '../../../../../../localization/i18n';

import {
  DAYS_IN_MILK_MAX_VALUE,
  DAYS_IN_MILK_MIN_VALUE,
  LACTATING_ANIMALS_MAX_VALUE,
  LACTATING_ANIMALS_MIN_VALUE,
  NEXT_FIELD_TEXT,
} from '../../../../../../constants/AppConstants';
import {
  ROF_FEEDING_INGREDIENTS_TYPES,
  ROF_FORM_TYPES,
} from '../../../../../../constants/toolsConstants/ROFConstants';
import {
  KEYBOARD_TYPE,
  ROF_FIELDS,
} from '../../../../../../constants/FormConstants';

import { logEvent } from '../../../../../../helpers/logHelper';
import {
  calculateAverageMilkProductionPerCowPerDay,
  handlePriceKgPerCowInAllMilkingIngredients,
  recalculateTotalDryMatterInFeeding,
} from '../../../../../../helpers/rofHelper';

import { setIsSiteDataUpdated } from '../../../../../../store/actions/tools/rof';

const Feeding = props => {
  let { isEditable, formType } = props;
  let { setFieldValue, values, handleChange } = useFormikContext();
  const dispatch = useDispatch();

  const handleLactatingCowsChange = v => {
    try {
      //set set lactating cows
      setFieldValue(`${ROF_FIELDS.FEEDING}.${ROF_FIELDS.LACTATING_COWS}`, v);
      //set avg milk production cow/day
      let milkProduction = calculateAverageMilkProductionPerCowPerDay(
        values[ROF_FIELDS.MILK_PRODUCTION][
          ROF_FIELDS.AVERAGE_MILK_PRODUCTION_KG
        ],
        v,
        true,
      );
      setFieldValue(
        `${ROF_FIELDS.MILK_PRODUCTION}.${ROF_FIELDS.MILK_PRODUCTION_KG}`,
        milkProduction,
      );
      //set price per kg cow in all milking ingredients
      handlePriceKgPerCowInAllMilkingIngredients(
        setFieldValue,
        milkProduction,
        values[ROF_FIELDS.MILK_PRODUCTION],
      );
      if (formType === ROF_FORM_TYPES.INDIVIDUAL_COWS) {
        //recalculate all the total dry matter in feedings.
        recalculateTotalDryMatterInFeeding(values, v, setFieldValue);
      }
      dispatch(setIsSiteDataUpdated(true));
    } catch (e) {
      logEvent('handleLactatingCowsChange fail', e);
    }
  };

  const handleDaysInMilkChange = v => {
    try {
      //set set days in milk
      setFieldValue(`${ROF_FIELDS.FEEDING}.${ROF_FIELDS.DAYS_IN_MILK}`, v);

      dispatch(setIsSiteDataUpdated(true));
    } catch (e) {
      logEvent('handleDaysInMilkChange fail', e);
    }
  };

  return (
    <View style={styles.container}>
      {/* from site setup */}
      <NumberFormInput
        label={i18n.t('lactatingCows')}
        disabled={!isEditable}
        placeholder={i18n.t('numberPlaceholder')}
        isInteger
        hasCommas
        required
        minValue={LACTATING_ANIMALS_MIN_VALUE}
        maxValue={LACTATING_ANIMALS_MAX_VALUE}
        value={values[ROF_FIELDS.FEEDING][ROF_FIELDS.LACTATING_COWS]}
        onChange={handleLactatingCowsChange}
        // reference={input => {
        //   inputReferences.current[
        //     selectedScorer?.key === SCORER_ENUMS.THREE_SCREEN
        //       ? index + FORM_INPUT_REFERENCE.FIELD_THREE
        //       : index + FORM_INPUT_REFERENCE.FIELD_FOUR
        //   ] = input;
        // }}
        onSubmitEditing={() => {
          Keyboard.dismiss();
        }}
        blurOnSubmit={false}
        customLabelStyle={styles.numberInputFieldLabel}
        customInputContainerStyle={styles.numberInputStyle}
        customContainerStyle={styles.numberInputContainerStyles}
        returnKeyType={NEXT_FIELD_TEXT.NEXT}
        inputAccessoryViewID="customInputAccessoryView"
        // onFocus={() => {
        //   setType(CONTENT_TYPE.NUMBER);
        //   setAction({
        //     dismiss: true,
        //   });
        // }}
      />
      {/* from site setup */}
      <NumberFormInput
        label={i18n.t('daysInMilk')}
        disabled={!isEditable}
        placeholder={i18n.t('numberPlaceholder')}
        minValue={DAYS_IN_MILK_MIN_VALUE}
        maxValue={DAYS_IN_MILK_MAX_VALUE}
        isNegative={true}
        value={values[ROF_FIELDS.FEEDING][ROF_FIELDS.DAYS_IN_MILK]}
        onChange={handleDaysInMilkChange}
        keyboardType={
          Platform.OS === 'ios'
            ? KEYBOARD_TYPE.NUMBERS_AND_PUNCTUATION
            : KEYBOARD_TYPE.NUMBER_PAD
        }
        // reference={input => {
        //   inputReferences.current[
        //     selectedScorer?.key === SCORER_ENUMS.THREE_SCREEN
        //       ? index + FORM_INPUT_REFERENCE.FIELD_THREE
        //       : index + FORM_INPUT_REFERENCE.FIELD_FOUR
        //   ] = input;
        // }}
        onSubmitEditing={() => {
          Keyboard.dismiss();
        }}
        blurOnSubmit={false}
        customLabelStyle={styles.numberInputFieldLabel}
        customInputContainerStyle={styles.numberInputStyle}
        customContainerStyle={styles.numberInputContainerStyles}
        returnKeyType={NEXT_FIELD_TEXT.DONE}
        inputAccessoryViewID="customInputAccessoryView"
        // onFocus={() => {
        //   setType(CONTENT_TYPE.NUMBER);
        //   setAction({
        //     dismiss: true,
        //   });
        // }}
      />
      <FeedingIngredientsForm
        {...props}
        title={i18n.t('homeGrownForages')}
        buttonText={i18n.t('addHomeGrownForages')}
        type={ROF_FEEDING_INGREDIENTS_TYPES.HOME_GROWN_FORAGES}
        atleastOneRequired
        ingredientNameTitle={i18n.t('forageName')}
        formType={formType}
      />
      <FeedingIngredientsForm
        {...props}
        title={i18n.t('homeGrownGrains')}
        buttonText={i18n.t('addHomeGrownGrains')}
        type={ROF_FEEDING_INGREDIENTS_TYPES.HOME_GROWN_GRAINS}
        atleastOneRequired
        ingredientNameTitle={i18n.t('grainName')}
        formType={formType}
      />
      <FeedingIngredientsForm
        {...props}
        title={i18n.t('purchaseBulkFeed')}
        buttonText={i18n.t('addPurchaseBulkFeed')}
        type={ROF_FEEDING_INGREDIENTS_TYPES.PURCHASE_BULK_FEED}
        ingredientNameTitle={i18n.t('feedName')}
        formType={formType}
      />
      <FeedingIngredientsForm
        {...props}
        title={i18n.t('purchaseBagsFeed')}
        buttonText={i18n.t('addPurchaseBagsFeed')}
        type={ROF_FEEDING_INGREDIENTS_TYPES.PURCHASE_BAG_FEED}
        ingredientNameTitle={i18n.t('feedName')}
        formType={formType}
      />
    </View>
  );
};

export default Feeding;
