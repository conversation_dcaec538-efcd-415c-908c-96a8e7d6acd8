// modules
import React from 'react';
import { View, Text, Keyboard } from 'react-native';
import { useFormikContext } from 'formik';
import { useSelector } from 'react-redux';

import NumberFormInput from '../../../../../../common/NumberFormInput';

import styles from './styles';

import { ROF_FIELDS } from '../../../../../../../constants/FormConstants';
import { NEXT_FIELD_TEXT } from '../../../../../../../constants/AppConstants';
import {
  ROF_DECIMAL_PLACES,
  ROF_INTEGER_MAX_VALUE,
  ROF_INTEGER_MIN_VALUE,
  ROF_MILKING_INGREDIENTS_TYPES,
} from '../../../../../../../constants/toolsConstants/ROFConstants';

import i18n from '../../../../../../../localization/i18n';

import {
  getCurrencyForTools,
  getWeightUnitByMeasure,
} from '../../../../../../../helpers/appSettingsHelper';
import {
  calculateCurrentQuotaUtilizationKgPerDay,
  calculatePriceKgPerCow,
} from '../../../../../../../helpers/rofHelper';

const MilkingIngredients = props => {
  let { isEditable, milkingType } = props;
  let { setFieldValue, values } = useFormikContext();

  const unitOfMeasure = useSelector(state => state.visit.visit?.unitOfMeasure);
  const currencies = useSelector(state => state.enums.enum?.currencies);
  const selectedCurrency = useSelector(
    state => state.visit.visit?.selectedCurrency,
  );

  const weightUnit = getWeightUnitByMeasure(unitOfMeasure);
  const currencySymbol = getCurrencyForTools(currencies, selectedCurrency);

  const handlePriceKgPerHLChange = v => {
    //set price kg per hl
    setFieldValue(
      `${ROF_FIELDS.MILK_PRODUCTION}.${milkingType}.${ROF_FIELDS.PRICE_PER_HL}`,
      v,
    );
    if (milkingType === ROF_FIELDS.BUTTERFAT) {
      //set currentQuotaUtilizationKgPerDay
      setFieldValue(
        `${ROF_FIELDS.MILK_PRODUCTION}.${ROF_FIELDS.CURRENT_QUOTA_UTILIZATION_KG_PER_DAY}`,
        calculateCurrentQuotaUtilizationKgPerDay(
          values[ROF_FIELDS.MILK_PRODUCTION][
            ROF_FIELDS.AVERAGE_MILK_PRODUCTION_KG
          ],
          v,
          true,
        ),
      );
    }
    //set price per kg cow
    setFieldValue(
      `${ROF_FIELDS.MILK_PRODUCTION}.${milkingType}.${ROF_FIELDS.PRICE_PER_KG_PER_COW}`,
      calculatePriceKgPerCow(
        values[ROF_FIELDS.MILK_PRODUCTION][ROF_FIELDS.MILK_PRODUCTION_KG],
        v,
        true,
      ),
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.categoryHeader}>
        <Text style={styles.headerText}>{i18n.t(milkingType)}</Text>
      </View>
      <NumberFormInput
        label={`${i18n.t('price')} (${currencySymbol}/${weightUnit})`}
        disabled={!isEditable}
        placeholder={i18n.t('numberPlaceholder')}
        decimalPoints={ROF_DECIMAL_PLACES}
        minValue={ROF_INTEGER_MIN_VALUE}
        value={
          values[ROF_FIELDS.MILK_PRODUCTION][milkingType][
            ROF_FIELDS.PRICE_PER_KG
          ]
        }
        onChange={v => {
          setFieldValue(
            `${ROF_FIELDS.MILK_PRODUCTION}.${milkingType}.${ROF_FIELDS.PRICE_PER_KG}`,
            v,
          );
        }}
        onSubmitEditing={() => Keyboard.dismiss()}
        blurOnSubmit={true}
        customLabelStyle={styles.numberInputFieldLabel}
        customInputContainerStyle={styles.numberInputStyle}
        customContainerStyle={styles.numberInputContainerStyles}
        returnKeyType={NEXT_FIELD_TEXT.NEXT}
        inputAccessoryViewID="customInputAccessoryView"
        // reference={input => {
        //   inputReferences.current[
        //     selectedScorer?.key === SCORER_ENUMS.THREE_SCREEN
        //       ? index + FORM_INPUT_REFERENCE.FIELD_THREE
        //       : index + FORM_INPUT_REFERENCE.FIELD_FOUR
        //   ] = input;
        // }}
        // onFocus={() => {
        //   setType(CONTENT_TYPE.NUMBER);
        //   setAction({
        //     dismiss: true,
        //   });
        // }}
      />

      {milkingType != ROF_MILKING_INGREDIENTS_TYPES.CLASS2_PROTEIN &&
        milkingType !=
          ROF_MILKING_INGREDIENTS_TYPES.CLASS2_LACTOSE_AND_OTHER_SOLIDS &&
        milkingType != ROF_MILKING_INGREDIENTS_TYPES.DEDUCTIONS && (
          <>
            {/* TODO: handle one min */}
            <NumberFormInput
              label={`${i18n.t('price')} (${weightUnit}${i18n.t('perHL')})`}
              placeholder={i18n.t('numberPlaceholder')}
              disabled={!isEditable}
              hasCommas
              required
              decimalPoints={ROF_DECIMAL_PLACES}
              minValue={ROF_INTEGER_MIN_VALUE}
              maxValue={ROF_INTEGER_MAX_VALUE}
              value={
                values[ROF_FIELDS.MILK_PRODUCTION][milkingType][
                  ROF_FIELDS.PRICE_PER_HL
                ]
              }
              onChange={handlePriceKgPerHLChange}
              onSubmitEditing={() => Keyboard.dismiss()}
              blurOnSubmit={true}
              customLabelStyle={styles.numberInputFieldLabel}
              customInputContainerStyle={styles.numberInputStyle}
              customContainerStyle={styles.numberInputContainerStyles}
              returnKeyType={NEXT_FIELD_TEXT.NEXT}
              inputAccessoryViewID="customInputAccessoryView"
              // reference={input => {
              //   inputReferences.current[
              //     selectedScorer?.key === SCORER_ENUMS.THREE_SCREEN
              //       ? index + FORM_INPUT_REFERENCE.FIELD_THREE
              //       : index + FORM_INPUT_REFERENCE.FIELD_FOUR
              //   ] = input;
              // }}
              // onFocus={() => {
              //   setType(CONTENT_TYPE.NUMBER);
              //   setAction({
              //     dismiss: true,
              //   });
              // }}
            />
            <NumberFormInput
              label={`${i18n.t('price')} (${weightUnit}${i18n.t('perCow')})`}
              disabled={true}
              placeholder={i18n.t('numberPlaceholder')}
              hasCommas
              decimalPoints={ROF_DECIMAL_PLACES}
              value={
                values[ROF_FIELDS.MILK_PRODUCTION][milkingType][
                  ROF_FIELDS.PRICE_PER_KG_PER_COW
                ]
              }
              onSubmitEditing={() => Keyboard.dismiss()}
              blurOnSubmit={true}
              customLabelStyle={styles.numberInputFieldLabel}
              customInputContainerStyle={styles.numberInputStyle}
              customContainerStyle={styles.numberInputContainerStyles}
              returnKeyType={NEXT_FIELD_TEXT.NEXT}
              inputAccessoryViewID="customInputAccessoryView"
              // reference={input => {
              //   inputReferences.current[
              //     selectedScorer?.key === SCORER_ENUMS.THREE_SCREEN
              //       ? index + FORM_INPUT_REFERENCE.FIELD_THREE
              //       : index + FORM_INPUT_REFERENCE.FIELD_FOUR
              //   ] = input;
              // }}
              // onFocus={() => {
              //   setType(CONTENT_TYPE.NUMBER);
              //   setAction({
              //     dismiss: true,
              //   });
              // }}
            />
          </>
        )}
    </View>
  );
};

export default MilkingIngredients;
