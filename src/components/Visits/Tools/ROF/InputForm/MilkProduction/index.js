// modules
import React, { useRef, useEffect } from 'react';
import { View } from 'react-native';
import { useFormikContext } from 'formik';
import { useSelector } from 'react-redux';

import NumberFormInput from '../../../../../common/NumberFormInput';
import MilkingIngredients from './MilkingIngredients';

import styles from './styles';

import { ROF_FIELDS } from '../../../../../../constants/FormConstants';
import {
  ANIMALS_IN_TANK_MAX_VALUE,
  NEXT_FIELD_TEXT,
} from '../../../../../../constants/AppConstants';
import {
  ROF_DECIMAL_PLACES,
  ROF_INTEGER_MAX_VALUE,
  ROF_INTEGER_MIN_VALUE,
  ROF_ONE_DECIMAL_PLACES,
} from '../../../../../../constants/toolsConstants/ROFConstants';
import { ROF_MILKING_INGREDIENTS_TYPES } from '../../../../../../constants/toolsConstants/ROFConstants';

import i18n from '../../../../../../localization/i18n';

import { getWeightUnitByMeasure } from '../../../../../../helpers/appSettingsHelper';
import { logEvent } from '../../../../../../helpers/logHelper';
import {
  calculateAverageMilkProductionPerCowPerDay,
  calculateCurrentQuotaUtilizationKgPerDay,
  handlePriceKgPerCowInAllMilkingIngredients,
} from '../../../../../../helpers/rofHelper';

const MilkProduction = props => {
  const inputRefs = useRef([]);

  let { isEditable, openMilkProductionOutputsAccordion } = props;
  let { values, setFieldValue } = useFormikContext();
  const unitOfMeasure = useSelector(state => state.visit.visit?.unitOfMeasure);

  const weightUnit = getWeightUnitByMeasure(unitOfMeasure);

  useEffect(() => {
    inputRefs.current[0].focus();
  }, []);

  const handleAverageMilkProductionAnimalsInTankChange = v => {
    try {
      //set avg milk production per kg
      setFieldValue(
        `${ROF_FIELDS.MILK_PRODUCTION}.${ROF_FIELDS.AVERAGE_MILK_PRODUCTION_KG}`,
        v,
      );

      //set currentQuotaUtilizationKgPerDay
      setFieldValue(
        `${ROF_FIELDS.MILK_PRODUCTION}.${ROF_FIELDS.CURRENT_QUOTA_UTILIZATION_KG_PER_DAY}`,
        calculateCurrentQuotaUtilizationKgPerDay(
          v,
          values[ROF_FIELDS.MILK_PRODUCTION][ROF_FIELDS.BUTTERFAT][
            ROF_FIELDS.PRICE_PER_HL
          ],
          true,
        ),
      );
      //set avg milk production cow/day
      setFieldValue(
        `${ROF_FIELDS.MILK_PRODUCTION}.${ROF_FIELDS.MILK_PRODUCTION_KG}`,
        calculateAverageMilkProductionPerCowPerDay(
          v,
          values[ROF_FIELDS.FEEDING][ROF_FIELDS.LACTATING_COWS],
          true,
        ),
      );
    } catch (e) {
      console.log('handleAverageMilkProductionAnimalsInTankChange fail', e);
      logEvent('handleAverageMilkProductionAnimalsInTankChange fail', e);
    }
  };

  const handleMilkProductionLitersPerCowPerDayChange = v => {
    try {
      //set milk production per kg
      setFieldValue(
        `${ROF_FIELDS.MILK_PRODUCTION}.${ROF_FIELDS.MILK_PRODUCTION_KG}`,
        v,
      );

      //set price per kg cow in all milking ingredients
      handlePriceKgPerCowInAllMilkingIngredients(
        setFieldValue,
        v,
        values[ROF_FIELDS.MILK_PRODUCTION],
      );
    } catch (e) {
      console.log('handleMilkProductionLitersPerCowPerDayChange fail', e);
      logEvent('handleMilkProductionLitersPerCowPerDayChange fail', e);
    }
  };

  const setInputRef = (parentIndex, fieldIndex, node) => {
    try {
      if (!inputRefs.current[parentIndex]) {
        inputRefs.current[parentIndex] = [];
      }

      inputRefs.current[parentIndex][fieldIndex] = node;
    } catch (e) {
      logEvent('ROF>milk production>setInputRef', {
        parentIndex,
        fieldIndex,
        node,
        e,
      });
    }
  };

  const focusNextField = (parentIndex, fieldIndex) => {
    try {
      inputRefs.current[parentIndex]?.[fieldIndex]
        ? inputRefs.current[parentIndex]?.[fieldIndex]?.focus()
        : openMilkProductionOutputsAccordion(true);
    } catch (e) {
      logEvent('ROF>milk production>focusNextField', {
        parentIndex,
        fieldIndex,
        e,
      });
    }
  };

  return (
    <View style={styles.container}>
      {/* from milk production else number input, TODO: handle one min value */}
      <NumberFormInput
        label={`${i18n.t(
          'averageMilkProductionAnimalsInTank',
        )} (${weightUnit})`}
        placeholder={i18n.t('numberPlaceholder')}
        disabled={!isEditable}
        hasCommas
        required
        decimalPoints={ROF_ONE_DECIMAL_PLACES}
        minValue={ROF_INTEGER_MIN_VALUE}
        maxValue={ANIMALS_IN_TANK_MAX_VALUE}
        value={
          values[ROF_FIELDS.MILK_PRODUCTION][
            ROF_FIELDS.AVERAGE_MILK_PRODUCTION_KG
          ]
        }
        onChange={handleAverageMilkProductionAnimalsInTankChange}
        blurOnSubmit={true}
        customLabelStyle={styles.numberInputFieldLabel}
        customInputContainerStyle={styles.numberInputStyle}
        customContainerStyle={styles.numberInputContainerStyles}
        returnKeyType={NEXT_FIELD_TEXT.NEXT}
        reference={reference => (inputRefs.current[0] = reference)}
        onSubmitEditing={() => inputRefs.current[1].focus()}
      />
      {/* from site setup */}

      <NumberFormInput
        label={`${i18n.t('averageMilkProductionLitresPerCowPerDay')}`}
        placeholder={i18n.t('numberPlaceholder')}
        disabled={true}
        decimalPoints={ROF_DECIMAL_PLACES}
        minValue={ROF_INTEGER_MIN_VALUE}
        maxValue={ROF_INTEGER_MAX_VALUE}
        value={
          values[ROF_FIELDS.MILK_PRODUCTION][ROF_FIELDS.MILK_PRODUCTION_KG]
        }
        onChange={handleMilkProductionLitersPerCowPerDayChange}
        blurOnSubmit={true}
        customLabelStyle={styles.numberInputFieldLabel}
        customInputContainerStyle={styles.numberInputStyle}
        customContainerStyle={styles.numberInputContainerStyles}
      />
      <NumberFormInput
        label={`${i18n.t('kgOfQuotaPerDay').replaceAll('kg', weightUnit)}`}
        placeholder={i18n.t('numberPlaceholder')}
        disabled={!isEditable}
        decimalPoints={ROF_DECIMAL_PLACES}
        minValue={ROF_INTEGER_MIN_VALUE}
        maxValue={ROF_INTEGER_MAX_VALUE}
        value={
          values[ROF_FIELDS.MILK_PRODUCTION][ROF_FIELDS.KG_OF_QUOTA_PER_DAY]
        }
        onChange={v => {
          setFieldValue(
            `${ROF_FIELDS.MILK_PRODUCTION}.${ROF_FIELDS.KG_OF_QUOTA_PER_DAY}`,
            v,
          );
        }}
        blurOnSubmit={true}
        customLabelStyle={styles.numberInputFieldLabel}
        customInputContainerStyle={styles.numberInputStyle}
        customContainerStyle={styles.numberInputContainerStyles}
        returnKeyType={NEXT_FIELD_TEXT.NEXT}
        reference={reference => (inputRefs.current[1] = reference)}
        onSubmitEditing={() => inputRefs.current[2].focus()}
      />
      <NumberFormInput
        label={`${i18n
          .t('incentiveDaysKgPerDay')
          .replaceAll('kg', weightUnit)}`}
        placeholder={i18n.t('numberPlaceholder')}
        disabled={!isEditable}
        isInteger
        minValue={ROF_INTEGER_MIN_VALUE}
        maxValue={ROF_INTEGER_MAX_VALUE}
        value={
          values[ROF_FIELDS.MILK_PRODUCTION][
            ROF_FIELDS.INCENTIVE_DAYS_KG_PER_DAY
          ]
        }
        onChange={v => {
          setFieldValue(
            `${ROF_FIELDS.MILK_PRODUCTION}.${ROF_FIELDS.INCENTIVE_DAYS_KG_PER_DAY}`,
            v,
          );
        }}
        blurOnSubmit={true}
        customLabelStyle={styles.numberInputFieldLabel}
        customInputContainerStyle={styles.numberInputStyle}
        customContainerStyle={styles.numberInputContainerStyles}
        returnKeyType={NEXT_FIELD_TEXT.NEXT}
        reference={reference => (inputRefs.current[2] = reference)}
        onSubmitEditing={() => inputRefs.current[3].focus()}
      />
      <NumberFormInput
        label={`${i18n.t('totalQuota')} (${weightUnit}${i18n.t('perDay')})`}
        placeholder={i18n.t('numberPlaceholder')}
        disabled={!isEditable}
        decimalPoints={ROF_DECIMAL_PLACES}
        minValue={ROF_INTEGER_MIN_VALUE}
        maxValue={ROF_INTEGER_MAX_VALUE}
        value={
          values[ROF_FIELDS.MILK_PRODUCTION][ROF_FIELDS.TOTAL_QUOTA_KG_PER_DAY]
        }
        onChange={v => {
          setFieldValue(
            `${ROF_FIELDS.MILK_PRODUCTION}.${ROF_FIELDS.TOTAL_QUOTA_KG_PER_DAY}`,
            v,
          );
        }}
        blurOnSubmit={true}
        customLabelStyle={styles.numberInputFieldLabel}
        customInputContainerStyle={styles.numberInputStyle}
        customContainerStyle={styles.numberInputContainerStyles}
        returnKeyType={NEXT_FIELD_TEXT.NEXT}
        reference={reference => (inputRefs.current[3] = reference)}
        onSubmitEditing={() => inputRefs.current[0][0].focus()}
      />
      <NumberFormInput
        label={`${i18n
          .t('currentQuotaUtilizationKgPerDay')
          .replace('kg', weightUnit)}`}
        placeholder={i18n.t('numberPlaceholder')}
        disabled={true}
        decimalPoints={ROF_DECIMAL_PLACES}
        minValue={ROF_INTEGER_MIN_VALUE}
        maxValue={ROF_INTEGER_MAX_VALUE}
        value={
          values[ROF_FIELDS.MILK_PRODUCTION][
            ROF_FIELDS.CURRENT_QUOTA_UTILIZATION_KG_PER_DAY
          ]
        }
        blurOnSubmit={true}
        customLabelStyle={styles.numberInputFieldLabel}
        customInputContainerStyle={styles.numberInputStyle}
        customContainerStyle={styles.numberInputContainerStyles}
        returnKeyType={NEXT_FIELD_TEXT.NEXT}
      />
      {Object.values(ROF_MILKING_INGREDIENTS_TYPES).map(
        (milkingType, index) => {
          return (
            <MilkingIngredients
              milkingType={milkingType}
              isEditable={isEditable}
              setInputRef={setInputRef}
              focusNextField={focusNextField}
              index={index}
            />
          );
        },
      )}
    </View>
  );
};

export default MilkProduction;
