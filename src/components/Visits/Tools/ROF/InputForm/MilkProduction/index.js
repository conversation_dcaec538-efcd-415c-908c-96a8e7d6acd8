// modules
import React from 'react';
import { Keyboard, View } from 'react-native';
import { useFormikContext } from 'formik';
import { useSelector } from 'react-redux';

import NumberFormInput from '../../../../../common/NumberFormInput';
import MilkingIngredients from './MilkingIngredients';

import styles from './styles';

import { ROF_FIELDS } from '../../../../../../constants/FormConstants';
import {
  ANIMALS_IN_TANK_MAX_VALUE,
  NEXT_FIELD_TEXT,
} from '../../../../../../constants/AppConstants';
import {
  ROF_DECIMAL_PLACES,
  ROF_INTEGER_MAX_VALUE,
  ROF_INTEGER_MIN_VALUE,
  ROF_ONE_DECIMAL_PLACES,
} from '../../../../../../constants/toolsConstants/ROFConstants';
import { ROF_MILKING_INGREDIENTS_TYPES } from '../../../../../../constants/toolsConstants/ROFConstants';

import i18n from '../../../../../../localization/i18n';

import { getWeightUnitByMeasure } from '../../../../../../helpers/appSettingsHelper';
import { logEvent } from '../../../../../../helpers/logHelper';
import {
  calculateAverageMilkProductionPerCowPerDay,
  calculateCurrentQuotaUtilizationKgPerDay,
  handlePriceKgPerCowInAllMilkingIngredients,
} from '../../../../../../helpers/rofHelper';

const MilkProduction = props => {
  let { isEditable } = props;
  let { values, setFieldValue } = useFormikContext();
  const unitOfMeasure = useSelector(state => state.visit.visit?.unitOfMeasure);

  const weightUnit = getWeightUnitByMeasure(unitOfMeasure);

  const handleAverageMilkProductionAnimalsInTankChange = v => {
    try {
      //set avg milk production per kg
      setFieldValue(
        `${ROF_FIELDS.MILK_PRODUCTION}.${ROF_FIELDS.AVERAGE_MILK_PRODUCTION_KG}`,
        v,
      );

      //set currentQuotaUtilizationKgPerDay
      setFieldValue(
        `${ROF_FIELDS.MILK_PRODUCTION}.${ROF_FIELDS.CURRENT_QUOTA_UTILIZATION_KG_PER_DAY}`,
        calculateCurrentQuotaUtilizationKgPerDay(
          v,
          values[ROF_FIELDS.MILK_PRODUCTION][ROF_FIELDS.BUTTERFAT][
            ROF_FIELDS.PRICE_PER_HL
          ],
          true,
        ),
      );
      //set avg milk production cow/day
      setFieldValue(
        `${ROF_FIELDS.MILK_PRODUCTION}.${ROF_FIELDS.MILK_PRODUCTION_KG}`,
        calculateAverageMilkProductionPerCowPerDay(
          v,
          values[ROF_FIELDS.FEEDING][ROF_FIELDS.LACTATING_COWS],
          true,
        ),
      );
    } catch (e) {
      console.log('handleAverageMilkProductionAnimalsInTankChange fail', e);
      logEvent('handleAverageMilkProductionAnimalsInTankChange fail', e);
    }
  };

  const handleMilkProductionLitersPerCowPerDayChange = v => {
    try {
      //set milk production per kg
      setFieldValue(
        `${ROF_FIELDS.MILK_PRODUCTION}.${ROF_FIELDS.MILK_PRODUCTION_KG}`,
        v,
      );

      //set price per kg cow in all milking ingredients
      handlePriceKgPerCowInAllMilkingIngredients(
        setFieldValue,
        v,
        values[ROF_FIELDS.MILK_PRODUCTION],
      );
    } catch (e) {
      console.log('handleMilkProductionLitersPerCowPerDayChange fail', e);
      logEvent('handleMilkProductionLitersPerCowPerDayChange fail', e);
    }
  };

  return (
    <View style={styles.container}>
      {/* from milk production else number input, TODO: handle one min value */}
      <NumberFormInput
        label={`${i18n.t(
          'averageMilkProductionAnimalsInTank',
        )} (${weightUnit})`}
        placeholder={i18n.t('numberPlaceholder')}
        disabled={!isEditable}
        hasCommas
        required
        decimalPoints={ROF_ONE_DECIMAL_PLACES}
        minValue={ROF_INTEGER_MIN_VALUE}
        maxValue={ANIMALS_IN_TANK_MAX_VALUE}
        value={
          values[ROF_FIELDS.MILK_PRODUCTION][
            ROF_FIELDS.AVERAGE_MILK_PRODUCTION_KG
          ]
        }
        onChange={handleAverageMilkProductionAnimalsInTankChange}
        onSubmitEditing={() => Keyboard.dismiss()}
        blurOnSubmit={true}
        customLabelStyle={styles.numberInputFieldLabel}
        customInputContainerStyle={styles.numberInputStyle}
        customContainerStyle={styles.numberInputContainerStyles}
        returnKeyType={NEXT_FIELD_TEXT.NEXT}
        inputAccessoryViewID="customInputAccessoryView"
        // reference={input => {
        //   inputReferences.current[
        //     selectedScorer?.key === SCORER_ENUMS.THREE_SCREEN
        //       ? index + FORM_INPUT_REFERENCE.FIELD_THREE
        //       : index + FORM_INPUT_REFERENCE.FIELD_FOUR
        //   ] = input;
        // }}
        // onFocus={() => {
        //   setType(CONTENT_TYPE.NUMBER);
        //   setAction({
        //     dismiss: true,
        //   });
        // }}
      />
      {/* from site setup */}

      <NumberFormInput
        label={`${i18n.t('averageMilkProductionLitresPerCowPerDay')}`}
        placeholder={i18n.t('numberPlaceholder')}
        disabled={true}
        decimalPoints={ROF_DECIMAL_PLACES}
        minValue={ROF_INTEGER_MIN_VALUE}
        maxValue={ROF_INTEGER_MAX_VALUE}
        value={
          values[ROF_FIELDS.MILK_PRODUCTION][ROF_FIELDS.MILK_PRODUCTION_KG]
        }
        onChange={handleMilkProductionLitersPerCowPerDayChange}
        onSubmitEditing={() => Keyboard.dismiss()}
        blurOnSubmit={true}
        customLabelStyle={styles.numberInputFieldLabel}
        customInputContainerStyle={styles.numberInputStyle}
        customContainerStyle={styles.numberInputContainerStyles}
        returnKeyType={NEXT_FIELD_TEXT.NEXT}
        inputAccessoryViewID="customInputAccessoryView"
        // reference={input => {
        //   inputReferences.current[
        //     selectedScorer?.key === SCORER_ENUMS.THREE_SCREEN
        //       ? index + FORM_INPUT_REFERENCE.FIELD_THREE
        //       : index + FORM_INPUT_REFERENCE.FIELD_FOUR
        //   ] = input;
        // }}
        // onFocus={() => {
        //   setType(CONTENT_TYPE.NUMBER);
        //   setAction({
        //     dismiss: true,
        //   });
        // }}
      />
      <NumberFormInput
        label={`${weightUnit} ${i18n.t('ofQuota')} (${weightUnit}${i18n.t(
          'perDay',
        )})`}
        placeholder={i18n.t('numberPlaceholder')}
        disabled={!isEditable}
        decimalPoints={ROF_DECIMAL_PLACES}
        minValue={ROF_INTEGER_MIN_VALUE}
        maxValue={ROF_INTEGER_MAX_VALUE}
        value={
          values[ROF_FIELDS.MILK_PRODUCTION][ROF_FIELDS.KG_OF_QUOTA_PER_DAY]
        }
        onChange={v => {
          setFieldValue(
            `${ROF_FIELDS.MILK_PRODUCTION}.${ROF_FIELDS.KG_OF_QUOTA_PER_DAY}`,
            v,
          );
        }}
        onSubmitEditing={() => Keyboard.dismiss()}
        blurOnSubmit={true}
        customLabelStyle={styles.numberInputFieldLabel}
        customInputContainerStyle={styles.numberInputStyle}
        customContainerStyle={styles.numberInputContainerStyles}
        returnKeyType={NEXT_FIELD_TEXT.NEXT}
        inputAccessoryViewID="customInputAccessoryView"
        // reference={input => {
        //   inputReferences.current[
        //     selectedScorer?.key === SCORER_ENUMS.THREE_SCREEN
        //       ? index + FORM_INPUT_REFERENCE.FIELD_THREE
        //       : index + FORM_INPUT_REFERENCE.FIELD_FOUR
        //   ] = input;
        // }}
        // onFocus={() => {
        //   setType(CONTENT_TYPE.NUMBER);
        //   setAction({
        //     dismiss: true,
        //   });
        // }}
      />
      <NumberFormInput
        label={`${i18n.t('incentiveDays')} (${weightUnit}${i18n.t('perDay')})`}
        placeholder={i18n.t('numberPlaceholder')}
        disabled={!isEditable}
        isInteger
        minValue={ROF_INTEGER_MIN_VALUE}
        maxValue={ROF_INTEGER_MAX_VALUE}
        value={
          values[ROF_FIELDS.MILK_PRODUCTION][
            ROF_FIELDS.INCENTIVE_DAYS_KG_PER_DAY
          ]
        }
        onChange={v => {
          setFieldValue(
            `${ROF_FIELDS.MILK_PRODUCTION}.${ROF_FIELDS.INCENTIVE_DAYS_KG_PER_DAY}`,
            v,
          );
        }}
        onSubmitEditing={() => Keyboard.dismiss()}
        blurOnSubmit={true}
        customLabelStyle={styles.numberInputFieldLabel}
        customInputContainerStyle={styles.numberInputStyle}
        customContainerStyle={styles.numberInputContainerStyles}
        returnKeyType={NEXT_FIELD_TEXT.NEXT}
        inputAccessoryViewID="customInputAccessoryView"
        // reference={input => {
        //   inputReferences.current[
        //     selectedScorer?.key === SCORER_ENUMS.THREE_SCREEN
        //       ? index + FORM_INPUT_REFERENCE.FIELD_THREE
        //       : index + FORM_INPUT_REFERENCE.FIELD_FOUR
        //   ] = input;
        // }}
        // onFocus={() => {
        //   setType(CONTENT_TYPE.NUMBER);
        //   setAction({
        //     dismiss: true,
        //   });
        // }}
      />
      <NumberFormInput
        label={`${i18n.t('totalQuota')} (${weightUnit}${i18n.t('perDay')})`}
        placeholder={i18n.t('numberPlaceholder')}
        disabled={!isEditable}
        decimalPoints={ROF_DECIMAL_PLACES}
        minValue={ROF_INTEGER_MIN_VALUE}
        maxValue={ROF_INTEGER_MAX_VALUE}
        value={
          values[ROF_FIELDS.MILK_PRODUCTION][ROF_FIELDS.TOTAL_QUOTA_KG_PER_DAY]
        }
        onChange={v => {
          setFieldValue(
            `${ROF_FIELDS.MILK_PRODUCTION}.${ROF_FIELDS.TOTAL_QUOTA_KG_PER_DAY}`,
            v,
          );
        }}
        onSubmitEditing={() => Keyboard.dismiss()}
        blurOnSubmit={true}
        customLabelStyle={styles.numberInputFieldLabel}
        customInputContainerStyle={styles.numberInputStyle}
        customContainerStyle={styles.numberInputContainerStyles}
        returnKeyType={NEXT_FIELD_TEXT.NEXT}
        inputAccessoryViewID="customInputAccessoryView"
        // reference={input => {
        //   inputReferences.current[
        //     selectedScorer?.key === SCORER_ENUMS.THREE_SCREEN
        //       ? index + FORM_INPUT_REFERENCE.FIELD_THREE
        //       : index + FORM_INPUT_REFERENCE.FIELD_FOUR
        //   ] = input;
        // }}
        // onFocus={() => {
        //   setType(CONTENT_TYPE.NUMBER);
        //   setAction({
        //     dismiss: true,
        //   });
        // }}
      />
      <NumberFormInput
        label={`${i18n.t('currentQuotaUtilization')} (${weightUnit}${i18n.t(
          'perDay',
        )})`}
        placeholder={i18n.t('numberPlaceholder')}
        disabled={true}
        decimalPoints={ROF_DECIMAL_PLACES}
        minValue={ROF_INTEGER_MIN_VALUE}
        maxValue={ROF_INTEGER_MAX_VALUE}
        value={
          values[ROF_FIELDS.MILK_PRODUCTION][
            ROF_FIELDS.CURRENT_QUOTA_UTILIZATION_KG_PER_DAY
          ]
        }
        onSubmitEditing={() => Keyboard.dismiss()}
        blurOnSubmit={true}
        customLabelStyle={styles.numberInputFieldLabel}
        customInputContainerStyle={styles.numberInputStyle}
        customContainerStyle={styles.numberInputContainerStyles}
        returnKeyType={NEXT_FIELD_TEXT.NEXT}
        inputAccessoryViewID="customInputAccessoryView"
        // reference={input => {
        //   inputReferences.current[
        //     selectedScorer?.key === SCORER_ENUMS.THREE_SCREEN
        //       ? index + FORM_INPUT_REFERENCE.FIELD_THREE
        //       : index + FORM_INPUT_REFERENCE.FIELD_FOUR
        //   ] = input;
        // }}
        // onFocus={() => {
        //   setType(CONTENT_TYPE.NUMBER);
        //   setAction({
        //     dismiss: true,
        //   });
        // }}
      />
      {Object.values(ROF_MILKING_INGREDIENTS_TYPES).map(milkingType => {
        return (
          <MilkingIngredients
            milkingType={milkingType}
            isEditable={isEditable}
          />
        );
      })}
    </View>
  );
};

export default MilkProduction;
