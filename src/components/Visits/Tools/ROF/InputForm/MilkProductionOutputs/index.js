// modules
import React from 'react';
import { View, Keyboard } from 'react-native';
import { useFormikContext } from 'formik';
import { useSelector } from 'react-redux';

// styles
import styles from './styles';

// components
import NumberFormInput from '../../../../../common/NumberFormInput';

// localization
import i18n from '../../../../../../localization/i18n';

import {
  getCurrencyForTools,
  getWeightUnitByMeasure,
} from '../../../../../../helpers/appSettingsHelper';

// constants
import { ROF_FIELDS } from '../../../../../../constants/FormConstants';
import { NEXT_FIELD_TEXT } from '../../../../../../constants/AppConstants';
import { getMilkProductionOutputsInitialFormValues } from '../../../../../../helpers/rofHelper';
import {
  ROF_INTEGER_MAX_VALUE,
  ROF_INTEGER_MIN_VALUE,
} from '../../../../../../constants/toolsConstants/ROFConstants';

const MilkProductionOutputs = props => {
  let { isEditable, formType } = props;
  let { setFieldValue, values } = useFormikContext();
  const unitOfMeasure = useSelector(state => state.visit.visit?.unitOfMeasure);
  const currencies = useSelector(state => state.enums.enum?.currencies);
  const selectedCurrency = useSelector(
    state => state.visit.visit?.selectedCurrency,
  );

  const weightUnit = getWeightUnitByMeasure(unitOfMeasure);
  const currencySymbol = getCurrencyForTools(currencies, selectedCurrency);

  const milkProductionOutputsKeys = Object.keys(
    values[ROF_FIELDS.MILK_PRODUCTION_OUTPUTS],
  );

  return (
    <View style={styles.container}>
      {milkProductionOutputsKeys?.map(outputKey => {
        return (
          <NumberFormInput
            label={i18n
              .t(outputKey)
              .replace('$', currencySymbol)
              .replace('kg', weightUnit)}
            disabled={
              outputKey === ROF_FIELDS.MAX_ALLOWED && isEditable ? false : true
            }
            hasCommas
            value={values[ROF_FIELDS.MILK_PRODUCTION_OUTPUTS][outputKey]}
            onChange={v => {
              if (outputKey === ROF_FIELDS.MAX_ALLOWED) {
                setFieldValue(
                  `${ROF_FIELDS.MILK_PRODUCTION_OUTPUTS}.${ROF_FIELDS.MAX_ALLOWED}`,
                  v,
                );
              }
            }}
            onBlur={() => {
              if (outputKey === ROF_FIELDS.MAX_ALLOWED) {
                getMilkProductionOutputsInitialFormValues(
                  values,
                  null,
                  formType,
                  setFieldValue,
                  true,
                );
              }
            }}
            minValue={
              outputKey === ROF_FIELDS.MAX_ALLOWED
                ? ROF_INTEGER_MIN_VALUE
                : null
            }
            maxValue={
              outputKey === ROF_FIELDS.MAX_ALLOWED
                ? ROF_INTEGER_MAX_VALUE
                : null
            }
            // reference={input => {
            //   inputReferences.current[
            //     selectedScorer?.key === SCORER_ENUMS.THREE_SCREEN
            //       ? index + FORM_INPUT_REFERENCE.FIELD_THREE
            //       : index + FORM_INPUT_REFERENCE.FIELD_FOUR
            //   ] = input;
            // }}
            onSubmitEditing={() => Keyboard.dismiss()}
            blurOnSubmit={true}
            customLabelStyle={styles.numberInputFieldLabel}
            customInputContainerStyle={styles.numberInputStyle}
            customContainerStyle={styles.numberInputContainerStyles}
            returnKeyType={NEXT_FIELD_TEXT.DONE}
            inputAccessoryViewID="customInputAccessoryView"
            // onFocus={() => {
            //   setType(CONTENT_TYPE.NUMBER);
            //   setAction({
            //     dismiss: true,
            //   });
            // }}
          />
        );
      })}
    </View>
  );
};

export default MilkProductionOutputs;
