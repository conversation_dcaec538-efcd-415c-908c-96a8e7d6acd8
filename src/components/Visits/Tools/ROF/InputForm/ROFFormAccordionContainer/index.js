// modules
import React, { useState } from 'react';
import { View } from 'react-native';
import { useFormikContext } from 'formik';

//components
import ROFAccordions from './formAccordions';
import HerdProfile from '../HerdProfile';
import Feeding from '../Feeding';
import MilkProduction from '../MilkProduction';
import MilkProductionOutputs from '../MilkProductionOutputs';

// styles
import styles from './styles';

import { ROF_FORM_ACCORDIONS } from '../../../../../../constants/toolsConstants/ROFConstants';

import i18n from '../../../../../../localization/i18n';
import { getMilkProductionOutputsInitialFormValues } from '../../../../../../helpers/rofHelper';

const ROFFormAccordionContainer = props => {
  let { scrollViewRef, isEditable, formType, unitOfMeasure } = props;
  let { values, setFieldValue } = useFormikContext();

  const [selectedAccordion, setSelectedAccordion] = useState(
    ROF_FORM_ACCORDIONS.HERD_PROFILE,
  );

  return (
    <View style={styles.formContainer}>
      <ROFAccordions
        isSelected={selectedAccordion === ROF_FORM_ACCORDIONS.HERD_PROFILE}
        onOpenAccordion={() => {
          setSelectedAccordion(ROF_FORM_ACCORDIONS.HERD_PROFILE);
        }}
        title={i18n.t('herdProfile')}
        content={<HerdProfile isEditable={isEditable} />}
      />
      <ROFAccordions
        isSelected={selectedAccordion === ROF_FORM_ACCORDIONS.FEEDING}
        onOpenAccordion={() => {
          scrollViewRef?.current?.scrollTo({
            x: 0,
            y: 70,
            animated: true,
          });
          setSelectedAccordion(ROF_FORM_ACCORDIONS.FEEDING);
        }}
        title={i18n.t('feeding')}
        content={<Feeding isEditable={isEditable} formType={formType} />}
      />
      <ROFAccordions
        isSelected={selectedAccordion === ROF_FORM_ACCORDIONS.MILK_PRODUCTION}
        onOpenAccordion={() => {
          scrollViewRef?.current?.scrollTo({
            x: 0,
            y: 120,
            animated: true,
          });

          setSelectedAccordion(ROF_FORM_ACCORDIONS.MILK_PRODUCTION);
        }}
        title={i18n.t('milkProduction')}
        content={<MilkProduction isEditable={isEditable} />}
      />
      <ROFAccordions
        isSelected={
          selectedAccordion === ROF_FORM_ACCORDIONS.MILK_PRODUCTION_OUTPUTS
        }
        onOpenAccordion={() => {
          scrollViewRef?.current?.scrollTo({
            x: 0,
            y: 180,
            animated: true,
          });

          setSelectedAccordion(ROF_FORM_ACCORDIONS.MILK_PRODUCTION_OUTPUTS);
          //calculate milk output formulae
          getMilkProductionOutputsInitialFormValues(
            values,
            unitOfMeasure,
            formType,
            setFieldValue,
            true,
          );
        }}
        title={i18n.t('milkProductionOutputs')}
        content={
          <MilkProductionOutputs isEditable={isEditable} formType={formType} />
        }
      />
    </View>
  );
};

export default ROFFormAccordionContainer;
