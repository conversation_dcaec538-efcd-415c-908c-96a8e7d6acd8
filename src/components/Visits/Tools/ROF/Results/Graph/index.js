// modules
import React, { useEffect, useMemo, useState } from 'react';
import { View, Text, SafeAreaView, ScrollView } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// styles
import styles from './styles';

// components
import ToolGraph from '../../../common/ToolGraph';
import ManureScreenerBarGraph from '../../../../../common/ManureScreenerGraph';

// localization
import i18n from '../../../../../../localization/i18n';

// constants
import {
  EXPORT_REPORT_TYPES,
  VISIT_TABLE_FIELDS,
} from '../../../../../../constants/AppConstants';
import { ROF_GRAPH_LEGENDS } from '../../../../../../constants/toolsConstants/ROFConstants';

// helpers
import { getGraphLegends } from '../../../../../../helpers/manureScreenerHelper';
import { createModelDataForROFGraphs } from '../../../../../../helpers/rofHelper';
import { getSiteAndAccountIdFromVisit } from '../../../../../../helpers/visitHelper';
import {
  getCurrencyForTools,
  getWeightUnitByMeasure,
} from '../../../../../../helpers/appSettingsHelper';

// actions
import {
  shareRofGraphsRequest,
  downloadRofGraphsRequest,
} from '../../../../../../store/actions/tools/rof';
import { getRecentVisitsForToolRequest } from '../../../../../../store/actions/tool';

const ROFGraph = props => {
  const dispatch = useDispatch();

  const visitState = useSelector(state => state.visit.visit);
  const rofToolData = useSelector(state => state.rof.rofToolData);
  const recentVisits = useSelector(state => state.tool.recentVisits);
  const comparingRofVisits = useSelector(state => state.rof.comparingRofVisits);
  const unitOfMeasure = useSelector(state => state.visit.visit?.unitOfMeasure);
  const currencies = useSelector(state => state.enums.enum?.currencies);
  const selectedCurrency = useSelector(
    state => state.visit.visit?.selectedCurrency,
  );

  const weightUnit = getWeightUnitByMeasure(unitOfMeasure);
  const currencySymbol = getCurrencyForTools(currencies, selectedCurrency);

  const labels = useMemo(() => getGraphLegends([visitState]), []);

  const [penGraphData, setPenGraphData] = useState([]);
  const [landscapeModalVisible, setLandscapeModalVisible] = useState(false);

  // get recent visits with rof tool data
  useEffect(() => {
    const { siteId = '', accountId = '' } =
      getSiteAndAccountIdFromVisit(visitState);

    dispatch(
      getRecentVisitsForToolRequest({
        siteId,
        accountId,
        localVisitId: visitState.id,
        tool: VISIT_TABLE_FIELDS.ROF,
      }),
    );
  }, []);
  console.log('This is rofToolData', rofToolData);
  console.log('recentVisits', recentVisits);
  console.log('comparingRofVisits', comparingRofVisits);

  useEffect(() => {
    const mappedData = createModelDataForROFGraphs(
      rofToolData,
      recentVisits,
      comparingRofVisits,
      props?.formType,
    );
    setPenGraphData(mappedData);
  }, [recentVisits, comparingRofVisits]);

  const onExpandIconPress = () => {
    setLandscapeModalVisible(!landscapeModalVisible);
  };

  const _handlePressDownload = option => {
    const payload = {
      exportType: option,
      selectedGraph: EXPORT_REPORT_TYPES.RETURN_OVER_FEED,
      recentVisits,
      comparingRofVisits,
      formType: props?.formType,
    };

    dispatch(downloadRofGraphsRequest(payload));
  };

  const _handlePressShare = async (option, exportMethod) => {
    const payload = {
      exportType: option,
      exportMethod,
      recentVisits,
      comparingRofVisits,
      formType: props?.formType,
    };

    dispatch(shareRofGraphsRequest(payload));
  };

  const customGraphTitleComponent = (
    <View style={styles.infoColumn}>
      <Text style={styles.penAnalysisTitle}>{i18n.t('ReturnOverFeed')}</Text>
    </View>
  );

  const graphComponent = (
    <View>
      <ScrollView horizontal={true} showsHorizontalScrollIndicator={false}>
        <ManureScreenerBarGraph
          useTiltedLabels
          hasYOffset
          barWidth={12}
          barOffset={18}
          verticalTickCount={5}
          xAxisFormatter={t => {
            return !!t?.length && t?.slice(0, 5);
          }}
          valuesFormatter={t => {
            return !!t > 0 ? parseFloat(t) : '';
          }}
          yAxisFormatter={t => {
            if (Math.round(t) == 0 || Math.round(t) == '0') {
              return Math.round(t).toFixed(2);
            } else {
              return Math.round(t).toFixed(2);
            }
          }}
          labels={labels?.length ? labels : []}
          data={penGraphData || []}
          width={
            landscapeModalVisible
              ? styles.graphWidthLandscape(
                  penGraphData?.[0]?.dataPoints?.length,
                ).width
              : styles.graphWidth(penGraphData?.[0]?.dataPoints?.length).width
          }
          xDomainPadding={{
            x: [
              penGraphData?.[0]?.dataPoints?.length * 17,
              penGraphData?.[0]?.dataPoints?.length * 15,
            ],
          }}
          height={
            landscapeModalVisible
              ? styles.graphHeightLandscape.height
              : styles.forageGraphHeight.height
          }
        />
      </ScrollView>

      {/* Legends */}
      <View style={styles.legendContainer(landscapeModalVisible)}>
        {ROF_GRAPH_LEGENDS.map((legend, index) => {
          return (
            <View style={styles.legendItem} key={`${legend.title}-${index}`}>
              <View style={styles.legendCircle(legend.color)}></View>
              <Text style={styles.legendText}>
                {legend.title
                  .replace('$', currencySymbol)
                  .replace('kg', weightUnit)}
              </Text>
            </View>
          );
        })}
      </View>
    </View>
  );

  return (
    <ToolGraph
      showExpandIcon
      handleExpandIconPress={onExpandIconPress}
      showDownloadIcon={!landscapeModalVisible}
      onDownloadPress={_handlePressDownload}
      onSharePress={_handlePressShare}
      showShareIcon={!landscapeModalVisible}
      landscapeModalVisible={landscapeModalVisible}
      customGraphTitleComponent={customGraphTitleComponent}
      graphComponent={
        <SafeAreaView>
          <View style={styles.leftPadding}>
            {penGraphData.length > 0 && graphComponent}
          </View>
        </SafeAreaView>
      }
    />
  );
};

export default ROFGraph;
