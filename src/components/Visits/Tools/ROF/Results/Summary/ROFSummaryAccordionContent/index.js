// modules
import React from 'react';
import { View, Text } from 'react-native';
import { useSelector } from 'react-redux';

import styles from './styles';

import i18n from '../../../../../../../localization/i18n';

import { convertInputNumbersToRegionalBasis } from '../../../../../../../helpers/genericHelper';
import { truncateString } from '../../../../../../../helpers/alphaNumericHelper';
import { getCurrencyForTools, getWeightUnitByMeasure } from '../../../../../../../helpers/appSettingsHelper';

import { ROF_DECIMAL_PLACES } from '../../../../../../../constants/toolsConstants/ROFConstants';


const ROFSummaryAccordionContent = props => {
  let { data } = props;

  const unitOfMeasure = useSelector(state => state.visit.visit?.unitOfMeasure);
  const currencies = useSelector(state => state.enums.enum?.currencies);
  const selectedCurrency = useSelector(
    state => state.visit.visit?.selectedCurrency,
  );

  const weightUnit = getWeightUnitByMeasure(unitOfMeasure);
  const currencySymbol = getCurrencyForTools(currencies, selectedCurrency);

  return (
    <View style={styles.container}>
      {data &&
        Object.entries(data)?.map(([key, value]) => (
          <View
            style={styles.textContainer}
            key={'_ROFSummaryAccordionContent-' + key}>
            <Text style={styles.accordionText}>
              {truncateString(i18n.t(key), 40)
                .replace('$', currencySymbol)
                .replace('kg', weightUnit)}
            </Text>
            <Text style={styles.accordionText}>
              {convertInputNumbersToRegionalBasis(
                value,
                ROF_DECIMAL_PLACES,
                true,
              )}
            </Text>
          </View>
        ))}
    </View>
  );
};

export default ROFSummaryAccordionContent;
