import fonts, {
  normalize,
} from '../../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../../constants/theme/variables/customColor';

export default {
  container: {
    flex: 1,
    flexDirection: 'column',
  },
  textContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    height: normalize(30),
    alignItems: 'center',
    marginHorizontal: normalize(16.5),
    marginTop: normalize(8),
  },
  accordionText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(12),
    lineHeight: normalize(16),
    letterSpacing: 0.15,
    color: colors.grey1,
  },
};
