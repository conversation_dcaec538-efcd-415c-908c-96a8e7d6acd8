// modules
import React from 'react';
import { StyleSheet } from 'react-native';
import { Cell, TableWrapper } from 'react-native-reanimated-table';

// components
import TableRow from '../TableRow';

// localization
import i18n from '../../../../../localization/i18n';

// styles
import customFont, {
  normalize,
} from '../../../../../constants/theme/variables/customFont';
import colors from '../../../../../constants/theme/variables/customColor';

const TableRowWrapper = ({
  cellLabelKey = '',
  customLabel = null,
  rowData = [],
  isRowEven = false,
  cellWidth = null,
  cellTextStyle = {},
  textStyles = {},
  customRowStyles,
  showCell = true,
  customCellStyles = {},
  widthArr = [],
  numberOfLines = 1,
}) => {
  console.log('customLabel', customLabel);

  return (
    <TableWrapper style={styles.flexRow}>
      {showCell ? (
        <Cell
          data={customLabel ? customLabel : i18n.t(cellLabelKey)}
          width={cellWidth ? cellWidth : styles.cellWidth}
          style={[
            styles.dataRow,
            isRowEven ? styles.dataRowDarkColor : null,
            customCellStyles,
          ]}
          textStyle={[styles.cellTextStyle, cellTextStyle]}
        />
      ) : (
        <></>
      )}
      <TableRow
        rowData={rowData}
        rowStyles={[
          styles.dataRow,
          isRowEven ? styles.dataRowDarkColor : null,
          customRowStyles,
        ]}
        textStyles={[styles.rowText, textStyles]}
        widthArr={widthArr}
        numberOfLines={numberOfLines}
      />
    </TableWrapper>
  );
};

const styles = StyleSheet.create({
  flexRow: {
    flexDirection: 'row',
  },
  dataRow: {
    height: normalize(35),
    backgroundColor: colors.grey10,
  },
  cellWidth: normalize(220),
  rowText: {
    fontSize: normalize(13),
    marginHorizontal: normalize(0),
    fontFamily: customFont.HelveticaNeueRegular,
    fontWeight: '500',
    color: colors.grey1,
    width: normalize(120),
    textAlign: 'center',
  },
  cellTextStyle: {
    color: colors.grey1,
    marginHorizontal: normalize(30),
    fontSize: normalize(14),
    fontWeight: '500',
    fontFamily: customFont.HelveticaNeueRegular,
  },
  dataRowDarkColor: {
    backgroundColor: colors.grey24,
  },
});

export default TableRowWrapper;
