import React, { useRef } from 'react';
import { View } from 'react-native';
import { useSelector } from 'react-redux';

import TableRow from '../../../Components/TableRow';
import TableRowHeader from '../../../Components/TableRowHeader';
import TableRowWrapper from '../../../Components/TableRowWrapper';

import styles from './styles';

import i18n from '../../../../../../localization/i18n';

import { ROF_FIELDS } from '../../../../../../constants/FormConstants';
import {
  getCurrencyForTools,
  getWeightUnitByMeasure,
} from '../../../../../../helpers/appSettingsHelper';

const FeedingSectionRenderer = ({
  sections = [],
  currencySymbol,
  weightUnit,
}) => {
  if (sections.length === 0) return null;

  return (
    <>
      {sections.map((section, sectionIndex) => (
        <View key={sectionIndex}>
          {section.data.map(([title, value], index) => (
            <View key={index}>
              {index === 0 ? (
                <TableRow
                  rowData={[value]}
                  customCellStyles={styles.flexOne}
                  customRowStyles={{
                    flex: 0.5,
                  }}
                  textStyles={styles.rowHeadingText}
                  rowStyles={{ backgroundColor: 'white' }}
                />
              ) : (
                <TableRowWrapper
                  key={index}
                  customLabel={i18n
                    .t(title)
                    .replace('$', currencySymbol)
                    .replace('kg', weightUnit)}
                  customCellStyles={styles.flexOne}
                  customRowStyles={{
                    flex: 0.5,
                  }}
                  rowData={[value]}
                  isRowEven={index % 2 === 0 ? false : true}
                  numberOfLines={2}
                />
              )}
            </View>
          ))}
        </View>
      ))}
    </>
  );
};

const FeedingAnalysis = ({
  homeGrownForage = [],
  homeGrownGrains = [],
  purchasedBagFeed = [],
  purchasedBulkFeed = [],
  feedingPages = [], 
  showHeader = true, 
}) => {
  const pageRefs = useRef([]);

  const unitOfMeasure = useSelector(
    state => state.visitReport.visitDetails?.unitOfMeasure,
  );
  const currencies = useSelector(state => state.enums.enum?.currencies);
  const selectedCurrency = useSelector(
    state => state.visitReport.visitDetails?.selectedCurrency,
  );

  const weightUnit = getWeightUnitByMeasure(unitOfMeasure);
  const currencySymbol = getCurrencyForTools(currencies, selectedCurrency);

  // If we have paginated data, use that; otherwise fall back to legacy props
  if (feedingPages && feedingPages.length > 0) {
    // Render only ONE feeding table (the first page in the array)
    const page = feedingPages[0];
    return (
      <View ref={viewRef => (pageRefs[0] = viewRef)} collapsable={false}>
        {showHeader && (
          <TableRowHeader headerLabel={i18n.t(ROF_FIELDS.FEEDING)} />
        )}

        <View style={styles.section}>
          <View style={styles.flexOne}>
            <FeedingSectionRenderer
              sections={page.leftColumn}
              currencySymbol={currencySymbol}
              weightUnit={weightUnit}
            />
          </View>

          <View style={styles.spacer} />

          <View style={styles.flexOne}>
            <FeedingSectionRenderer
              sections={page.rightColumn}
              currencySymbol={currencySymbol}
              weightUnit={weightUnit}
            />
          </View>
        </View>
      </View>
    );
  }

  // Legacy rendering for backward compatibility
  return (
    <View ref={viewRef => (pageRefs[0] = viewRef)} collapsable={false}>
      {showHeader && (
        <TableRowHeader headerLabel={i18n.t(ROF_FIELDS.FEEDING)} />
      )}

      <View style={styles.section}>
        <View style={styles.flexOne}>
          {homeGrownForage.length > 0 ? (
            homeGrownForage.map(([title, value], index) => (
              <View key={index}>
                {index == 0 ? (
                  <TableRow
                    rowData={[value]}
                    customCellStyles={styles.flexOne}
                    customRowStyles={{
                      flex: 0.5,
                    }}
                    textStyles={styles.rowHeadingText}
                    rowStyles={{ backgroundColor: 'white' }}
                  />
                ) : (
                  <TableRowWrapper
                    key={index}
                    customLabel={i18n
                      .t(title)
                      .replace('$', currencySymbol)
                      .replace('kg', weightUnit)}
                    customCellStyles={styles.flexOne}
                    customRowStyles={{
                      flex: 0.5,
                    }}
                    rowData={[value]}
                    isRowEven={index % 2 === 0 ? false : true}
                    numberOfLines={2}
                  />
                )}
              </View>
            ))
          ) : (
            <></>
          )}
          {purchasedBulkFeed.length > 0 ? (
            purchasedBulkFeed.map(([title, value], index) => (
              <View key={index}>
                {index == 0 ? (
                  <TableRow
                    rowData={[value]}
                    customCellStyles={styles.flexOne}
                    customRowStyles={{
                      flex: 0.5,
                    }}
                    textStyles={styles.rowHeadingText}
                    rowStyles={{ backgroundColor: 'white' }}
                  />
                ) : (
                  <TableRowWrapper
                    key={index}
                    customLabel={i18n
                      .t(title)
                      .replace('$', currencySymbol)
                      .replace('kg', weightUnit)}
                    customCellStyles={styles.flexOne}
                    customRowStyles={{
                      flex: 0.5,
                    }}
                    rowData={[value]}
                    isRowEven={index % 2 === 0 ? false : true}
                    numberOfLines={2}
                  />
                )}
              </View>
            ))
          ) : (
            <></>
          )}
        </View>

        <View style={styles.spacer} />

        <View style={styles.flexOne}>
          {homeGrownGrains.length > 0 ? (
            homeGrownGrains.map(([title, value], index) => (
              <View key={index}>
                {index == 0 ? (
                  <TableRow
                    rowData={[value]}
                    customCellStyles={styles.flexOne}
                    customRowStyles={{
                      flex: 0.5,
                    }}
                    textStyles={styles.rowHeadingText}
                    rowStyles={{ backgroundColor: 'white' }}
                  />
                ) : (
                  <TableRowWrapper
                    key={index}
                    customLabel={i18n
                      .t(title)
                      .replace('$', currencySymbol)
                      .replace('kg', weightUnit)}
                    customCellStyles={styles.flexOne}
                    customRowStyles={{
                      flex: 0.5,
                    }}
                    rowData={[value]}
                    isRowEven={index % 2 === 0 ? false : true}
                    numberOfLines={2}
                  />
                )}
              </View>
            ))
          ) : (
            <></>
          )}
          {purchasedBagFeed.length > 0 ? (
            purchasedBagFeed.map(([title, value], index) => (
              <View key={index}>
                {index == 0 ? (
                  <TableRow
                    rowData={[value]}
                    customCellStyles={styles.flexOne}
                    customRowStyles={{
                      flex: 0.5,
                    }}
                    textStyles={styles.rowHeadingText}
                    rowStyles={{ backgroundColor: 'white' }}
                  />
                ) : (
                  <TableRowWrapper
                    key={index}
                    customLabel={i18n
                      .t(title)
                      .replace('$', currencySymbol)
                      .replace('kg', weightUnit)}
                    customCellStyles={styles.flexOne}
                    customRowStyles={{
                      flex: 0.5,
                    }}
                    rowData={[value]}
                    isRowEven={index % 2 === 0 ? false : true}
                    numberOfLines={2}
                  />
                )}
              </View>
            ))
          ) : (
            <></>
          )}
        </View>
      </View>
    </View>
  );
};

export default FeedingAnalysis;
