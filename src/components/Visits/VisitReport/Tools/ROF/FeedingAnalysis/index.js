import React, { useRef } from 'react';
import { View } from 'react-native';
import { useSelector } from 'react-redux';

import TableRow from '../../../Components/TableRow';
import TableRowHeader from '../../../Components/TableRowHeader';
import TableRowWrapper from '../../../Components/TableRowWrapper';

import styles from './styles';

import i18n from '../../../../../../localization/i18n';

import { ROF_FIELDS } from '../../../../../../constants/FormConstants';
import {
  getCurrencyForTools,
  getWeightUnitByMeasure,
} from '../../../../../../helpers/appSettingsHelper';

const FeedingAnalysis = ({
  homeGrownForage = [],
  homeGrownFGrains = [],
  purchasedBagFeed = [],
  purchasedBulkFeed = [],
}) => {
  const pageRefs = useRef([]);

  const unitOfMeasure = useSelector(
    state => state.visitReport.visitDetails?.unitOfMeasure,
  );
  const currencies = useSelector(state => state.enums.enum?.currencies);
  const selectedCurrency = useSelector(
    state => state.visitReport.visitDetails?.selectedCurrency,
  );

  const weightUnit = getWeightUnitByMeasure(unitOfMeasure);
  const currencySymbol = getCurrencyForTools(currencies, selectedCurrency);

  const getFormattedLabel = title => {
    return i18n.t(title).replace('$', currencySymbol).replace('kg', weightUnit);
  };

  return (
    <View ref={viewRef => (pageRefs[0] = viewRef)} collapsable={false}>
      <TableRowHeader headerLabel={i18n.t(ROF_FIELDS.FEEDING)} />

      <View style={styles.section}>
        <View style={styles.flexOne}>
          {homeGrownForage.length > 0 ? (
            homeGrownForage.map(([title, value], index) => (
              <View>
                {index == 0 ? (
                  <TableRow
                    rowData={[value]}
                    customCellStyles={styles.flexOne}
                    customRowStyles={{
                      flex: 0.5,
                    }}
                    textStyles={styles.rowHeadingText}
                    rowStyles={{ backgroundColor: 'white' }}
                  />
                ) : (
                  <TableRowWrapper
                    key={index}
                    customLabel={getFormattedLabel(title)}
                    customCellStyles={styles.flexOne}
                    customRowStyles={{
                      flex: 0.5,
                    }}
                    rowData={[value]}
                    isRowEven={index % 2 === 0 ? false : true}
                    numberOfLines={2}
                  />
                )}
              </View>
            ))
          ) : (
            <></>
          )}
          {purchasedBulkFeed.length > 0 ? (
            purchasedBulkFeed.map(([title, value], index) => (
              <View>
                {index == 0 ? (
                  <TableRow
                    rowData={[value]}
                    customCellStyles={styles.flexOne}
                    customRowStyles={{
                      flex: 0.5,
                    }}
                    textStyles={styles.rowHeadingText}
                    rowStyles={{ backgroundColor: 'white' }}
                  />
                ) : (
                  <TableRowWrapper
                    key={index}
                    customLabel={getFormattedLabel(title)}
                    customCellStyles={styles.flexOne}
                    customRowStyles={{
                      flex: 0.5,
                    }}
                    rowData={[value]}
                    isRowEven={index % 2 === 0 ? false : true}
                    numberOfLines={2}
                  />
                )}
              </View>
            ))
          ) : (
            <></>
          )}
        </View>

        <View style={styles.spacer} />

        <View style={styles.flexOne}>
          {homeGrownFGrains.length > 0 ? (
            homeGrownFGrains.map(([title, value], index) => (
              <View>
                {index == 0 ? (
                  <TableRow
                    rowData={[value]}
                    customCellStyles={styles.flexOne}
                    customRowStyles={{
                      flex: 0.5,
                    }}
                    textStyles={styles.rowHeadingText}
                    rowStyles={{ backgroundColor: 'white' }}
                  />
                ) : (
                  <TableRowWrapper
                    key={index}
                    customLabel={getFormattedLabel(title)}
                    customCellStyles={styles.flexOne}
                    customRowStyles={{
                      flex: 0.5,
                    }}
                    rowData={[value]}
                    isRowEven={index % 2 === 0 ? false : true}
                    numberOfLines={2}
                  />
                )}
              </View>
            ))
          ) : (
            <></>
          )}
          {purchasedBagFeed.length > 0 ? (
            purchasedBagFeed.map(([title, value], index) => (
              <View>
                {index == 0 ? (
                  <TableRow
                    rowData={[value]}
                    customCellStyles={styles.flexOne}
                    customRowStyles={{
                      flex: 0.5,
                    }}
                    textStyles={styles.rowHeadingText}
                    rowStyles={{ backgroundColor: 'white' }}
                  />
                ) : (
                  <TableRowWrapper
                    key={index}
                    customLabel={getFormattedLabel(title)}
                    customCellStyles={styles.flexOne}
                    customRowStyles={{
                      flex: 0.5,
                    }}
                    rowData={[value]}
                    isRowEven={index % 2 === 0 ? false : true}
                    numberOfLines={2}
                  />
                )}
              </View>
            ))
          ) : (
            <></>
          )}
        </View>
      </View>
    </View>
  );
};

export default FeedingAnalysis;
