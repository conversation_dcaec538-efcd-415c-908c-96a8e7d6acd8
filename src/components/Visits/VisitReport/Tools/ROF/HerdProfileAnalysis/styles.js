import { StyleSheet } from 'react-native';
import customFont, {
  normalize,
} from '../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../constants/theme/variables/customColor';
import { Platform as RNPlatform } from 'react-native';
import Platform from '../../../../../../constants/theme/variables/platform';
export default StyleSheet.create({
  container: {
    marginTop: normalize(20),
    width: normalize(1000),
    borderWidth: 1,
    overflow: 'hidden',
    borderColor: colors.todayBackGroundColor,
    borderRadius: 4,
    paddingVertical: normalize(20),
  },
  scrollViewStyles: {
    paddingVertical: normalize(20),
  },
  scrollViewContainer: {
    flexGrow: 1,
  },
  mainContainer: {
    paddingHorizontal: normalize(20),
    flexDirection: 'row',
  },
  graphWidth: normalize(1000),
  graphHeight: normalize(300),
  graphPadding: {
    bottom: 40,
    right: 10,
    left: 55,
    top: 30,
  },
  bodyContainer: {
    width: normalize(1000),
  },
  yAxisLabel: {
    color: colors.alphabetIndex,
    fontFamily: customFont.HelveticaNeueRegular,
    fontSize: normalize(11),
    textAlign: 'center',
  },
  graphText: {
    marginBottom: normalize(10),
    fontFamily: customFont.HelveticaNeueBold,
    fontWeight: '400',
    fontSize: normalize(12),
    color: colors.alphabetIndex,
    letterSpacing: 0.15,
    alignSelf: 'center',
  },
  legendsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: normalize(20),
  },
  bullet: {
    marginRight: normalize(10),
    borderRadius: normalize(5),
    width: normalize(10),
    height: normalize(10),
    backgroundColor: colors.secondary2,
  },
  goalsBulletColor: {
    backgroundColor: colors.transparent,
    borderWidth: 1,
    borderStyle: 'dashed',
    marginLeft: normalize(20),
    borderColor: colors.graphHeaderBullet3,
  },
  legendsText: {
    fontFamily: customFont.HelveticaNeueBold,
    fontWeight: '500',
    fontSize: normalize(12),
    color: colors.grey1,
    letterSpacing: 0.15,
  },
  flexRow: {
    flexDirection: 'row',
  },
  section: {
    flexDirection: 'row',
  },
  spacer: {
    width: normalize(20),
    height: normalize(20),
  },
  flexOne: {
    flex: 1,
  },
  flex4: { flex: 4 },
  flex2: { flex: 2, marginLeft: normalize(20) },
  cellWidth: '50%',

  totalLost: {
    backgroundColor: colors.grey25,
  },
  totalLostText: {
    fontSize: normalize(14),
    fontFamily: customFont.HelveticaNeueBold,
    color: colors.grey1,
    fontWeight: 'bold',
  },

  rowText: {
    color: colors.grey1,
    width: null, //normalize(150),
    marginLeft: normalize(20),
    textAlign: 'left',
  },
  headerRow: {
    color: colors.white,
    width: null, //normalize(150),
    marginLeft: normalize(20),
    textAlign: 'left',
  },
  dataRowDarkColor: {
    backgroundColor: colors.grey24,
  },
  dataRow: dataLength => ({
    height: normalize(40),
    backgroundColor: colors.grey10,
    // width: normalize(220 * dataLength),
  }),
  graphHeading: {
    marginTop: normalize(10),
  },
  textHeading: {
    fontSize: normalize(16),
    color: colors.grey1,
    fontFamily: customFont.HelveticaNeueMedium,
    marginTop: normalize(10),
  },
  graphContainer: {
    borderWidth: 1,
    borderColor: colors.todayBackGroundColor,
    borderRadius: normalize(8),
    marginTop: normalize(10),
  },
  legendContainer: {
    flexDirection: 'row',
    marginBottom: normalize(15),
    marginLeft: normalize(20),
    flexWrap: 'wrap',
  },
  legendItem: {
    flexDirection: 'row',
    marginRight: normalize(30),
    alignItems: 'center',
  },
  legendCircle: backgroundColor => ({
    width: normalize(8),
    height: normalize(8),
    borderRadius: normalize(50),
    backgroundColor: backgroundColor,
    marginRight: normalize(10),
  }),
  legendText: {
    fontFamily: customFont.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(12),
    lineHeight: normalize(15),
    color: colors.grey1,
    letterSpacing: 0.15,
  },

  incidenceGraphYAxisLabel: {
    marginLeft:
      RNPlatform.OS === 'ios'
        ? Platform.deviceHeight < 700
          ? normalize(-50)
          : normalize(-50)
        : normalize(-40),
    marginRight:
      RNPlatform.OS === 'ios'
        ? Platform.deviceHeight < 700
          ? normalize(-60)
          : normalize(-60)
        : normalize(-70),
  },
  visitComparisonView: {
    marginTop: normalize(20),
  },
  visitComparisonHeader: {
    color: colors.grey1,
    fontSize: normalize(16),
    fontFamily: customFont.HelveticaNeueMedium,
    paddingBottom: normalize(5),
    textTransform: 'capitalize',
  },
  toolmainHeader: {
    color: colors.primaryMain,
    fontSize: normalize(16),
    fontFamily: customFont.HelveticaNeueMedium,
    paddingBottom: normalize(5),
    textTransform: 'capitalize',
  },
  rowStyle: {
    textTransform: 'capitalize',
  },
  marginTop: {
    marginTop: normalize(10),
  },
  subHeaderText: {
    fontSize: normalize(14),
    color: colors.primaryMain,
    marginLeft: normalize(10),
    // marginTop: normalize(10),
    marginBottom: normalize(10),
    lineHeight: normalize(18),
    fontWeight: '700',
  },
  dynamicHeightAdjustment: {
    height: normalize(150),
  },
  noDataContainer: {
    height: normalize(100),
    justifyContent: 'center',
    alignItems: 'center',
  },
});
