// modules
import React, { useEffect, useRef } from 'react';
import { View, ScrollView } from 'react-native';
import { Table } from 'react-native-reanimated-table';
import { useDispatch } from 'react-redux';

// styles
import styles from './styles';

// components
import ToolHeader from '../../../Components/ToolHeader';
import HerdProfileAnalysis from '../HerdProfileAnalysis';
import FeedingAnalysis from '../FeedingAnalysis';
import NotesListing from '../../../Components/NotesListing';
import ToolFooter from '../../../Components/ToolFooter';

// constants
import {
  TOOL_ANALYSIS_TYPES,
  TOOL_TYPES,
} from '../../../../../../constants/AppConstants';
import { ROF_FIELDS } from '../../../../../../constants/FormConstants';

// actions
import { addNewPagePdfPageReference } from '../../../../../../store/actions/visitReport';

// localization
import i18n from '../../../../../../localization/i18n';

const HerdProfileAndFeeding = ({
  tool,
  formType,
  herdProfileData,
  feedingData,
}) => {
  const pageRef = useRef([]);
  const dispatch = useDispatch();

  useEffect(() => {
    setTimeout(() => {
      const pageInfo = {
        pageRef: pageRef,
        toolType: TOOL_TYPES.ROF,
        analysisType: TOOL_ANALYSIS_TYPES.HERD_ANALYSIS,
        pageNumber: 1,
      };

      dispatch(addNewPagePdfPageReference(pageInfo));
    }, 100);
  }, []);

  // Use prepared data passed as props instead of calling helper functions
  const herdProfileBody = herdProfileData || {};
  const feedingBody = feedingData || {};

  console.log('feedingBody.homeGrownGrains', feedingBody.homeGrownGrains);

  return (
    <ScrollView
      horizontal
      style={styles.scrollViewStyles}
      contentContainerStyle={styles.scrollViewContainer}>
      <View ref={viewRef => (pageRef[0] = viewRef)} collapsable={false}>
        <ToolHeader
          basicInfo={tool?.basicInfo}
          analysisType={
            formType === ROF_FIELDS.TMR
              ? i18n.t('tmr')
              : i18n.t('individualCow')
          }
          isNotesAvailable={tool?.notes?.length}
        />

        <View style={styles.mainContainer}>
          <View style={styles.bodyContainer}>
            <Table>
              <HerdProfileAnalysis tool={tool} herdAnalysis={herdProfileBody} />
              <View style={styles.spacer} />
              <FeedingAnalysis
                // homeGrownFGrains={feedingBody.homeGrownGrains}
                // homeGrownForage={feedingBody.homeGrownForages}
                purchaseBulkFeed={feedingBody.purchaseBulkFeed}
                purchaseBagFeed={feedingBody.purchaseBagsFeed}
              />
            </Table>
          </View>

          <NotesListing toolId={tool?.basicInfo?.toolId} />
        </View>

        <ToolFooter isNotesAvailable={tool?.notes?.length} />
      </View>
    </ScrollView>
  );
};

export default HerdProfileAndFeeding;
