// modules
import React, { useEffect, useRef } from 'react';
import { View, ScrollView } from 'react-native';
import { Table } from 'react-native-reanimated-table';
import { useDispatch } from 'react-redux';

// styles
import styles from './styles';

// components
import ToolHeader from '../../../Components/ToolHeader';
import HerdProfileAnalysis from '../HerdProfileAnalysis';
import FeedingAnalysis from '../FeedingAnalysis';
import NotesListing from '../../../Components/NotesListing';
import ToolFooter from '../../../Components/ToolFooter';

// constants
import {
  TOOL_ANALYSIS_TYPES,
  TOOL_TYPES,
} from '../../../../../../constants/AppConstants';
import { ROF_FIELDS } from '../../../../../../constants/FormConstants';

// actions
import { addNewPagePdfPageReference } from '../../../../../../store/actions/visitReport';

// localization
import i18n from '../../../../../../localization/i18n';

const HerdProfileAndFeeding = ({
  tool,
  formType,
  herdProfileData,
  feedingData,
  feedingPages = [], // New prop for paginated feeding data
}) => {
  const pageRef = useRef([]);
  const dispatch = useDispatch();

  useEffect(() => {
    setTimeout(() => {
      const pageInfo = {
        pageRef: pageRef,
        toolType: TOOL_TYPES.ROF,
        analysisType: TOOL_ANALYSIS_TYPES.HERD_ANALYSIS,
        pageNumber: 1,
      };

      dispatch(addNewPagePdfPageReference(pageInfo));
    }, 100);
  }, []);

  // Use prepared data passed as props instead of calling helper functions
  const herdProfileBody = herdProfileData || {};
  const feedingBody = feedingData || {};

  console.log('feedingBody', feedingBody);
  console.log('feedingPages', feedingPages);

  // Determine if we should use paginated feeding data
  const usePaginatedFeeding = feedingPages && feedingPages.length > 0;
  const totalPages = usePaginatedFeeding ? feedingPages.length : 1;

  return (
    <ScrollView
      horizontal
      style={styles.scrollViewStyles}
      contentContainerStyle={styles.scrollViewContainer}>
      {/* First Page: Herd Profile + First Feeding Page */}
      <View ref={viewRef => (pageRef[0] = viewRef)} collapsable={false}>
        <ToolHeader
          basicInfo={tool?.basicInfo}
          analysisType={
            formType === ROF_FIELDS.TMR
              ? i18n.t('tmr')
              : i18n.t('individualCow')
          }
          isNotesAvailable={tool?.notes?.length}
        />

        <View style={styles.mainContainer}>
          <View style={styles.bodyContainer}>
            <Table>
              <HerdProfileAnalysis tool={tool} herdAnalysis={herdProfileBody} />
              <View style={styles.spacer} />
              {usePaginatedFeeding ? (
                <FeedingAnalysis
                  feedingPages={[feedingPages[0]]} // Only first page
                  showHeader={true}
                />
              ) : (
                <FeedingAnalysis
                  homeGrownGrains={feedingBody.homeGrownGrains}
                  homeGrownForage={feedingBody.homeGrownForages}
                  purchasedBagFeed={feedingBody.purchasedBagFeed}
                  purchasedBulkFeed={feedingBody.purchasedBulkFeed}
                  showHeader={true}
                />
              )}
            </Table>
          </View>

          <NotesListing toolId={tool?.basicInfo?.toolId} />
        </View>

        <ToolFooter isNotesAvailable={tool?.notes?.length} />
      </View>

      {/* Additional Pages: Remaining Feeding Sections */}
      {usePaginatedFeeding &&
        feedingPages.length > 1 &&
        feedingPages.slice(1).map((page, index) => (
          <View
            key={index + 1}
            ref={viewRef => (pageRef[index + 1] = viewRef)}
            collapsable={false}>
            <ToolHeader
              basicInfo={tool?.basicInfo}
              analysisType={
                formType === ROF_FIELDS.TMR
                  ? i18n.t('tmr')
                  : i18n.t('individualCow')
              }
              isNotesAvailable={tool?.notes?.length}
            />

            <View style={styles.mainContainer}>
              <View style={styles.bodyContainer}>
                <Table>
                  <FeedingAnalysis
                    feedingPages={[page]} // Single page
                    showHeader={true}
                  />
                </Table>
              </View>

              <NotesListing toolId={tool?.basicInfo?.toolId} />
            </View>

            <ToolFooter isNotesAvailable={tool?.notes?.length} />
          </View>
        ))}
    </ScrollView>
  );
};

export default HerdProfileAndFeeding;
