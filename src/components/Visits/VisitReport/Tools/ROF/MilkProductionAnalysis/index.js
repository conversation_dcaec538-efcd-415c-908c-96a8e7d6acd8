import React from 'react';
import { View } from 'react-native';
import { useSelector } from 'react-redux';

import styles from './styles';

import TableRow from '../../../Components/TableRow';
import TableRowWrapper from '../../../Components/TableRowWrapper';
import TableRowHeader from '../../../Components/TableRowHeader';

import i18n from '../../../../../../localization/i18n';

import { ROF_FIELDS } from '../../../../../../constants/FormConstants';

import {
  getCurrencyForTools,
  getWeightUnitByMeasure,
} from '../../../../../../helpers/appSettingsHelper';
import { convertInputNumbersToRegionalBasis } from '../../../../../../helpers/genericHelper';

const renderSection = (section = [], hasHeader = true) => {
  if (!section || section.length === 0) return null;
  const unitOfMeasure = useSelector(
    state => state.visitReport.visitDetails?.unitOfMeasure,
  );
  const currencies = useSelector(state => state.enums.enum?.currencies);
  const selectedCurrency = useSelector(
    state => state.visitReport.visitDetails?.selectedCurrency,
  );

  const weightUnit = getWeightUnitByMeasure(unitOfMeasure);
  const currencySymbol = getCurrencyForTools(currencies, selectedCurrency);

  return section.map(([label, value], index) => (
    <View key={index}>
      {hasHeader && index === 0 && label === 'title' ? (
        <TableRow
          rowData={[
            i18n
              .t(value)
              .replaceAll('$', currencySymbol)
              .replaceAll('kg', weightUnit),
          ]}
          customCellStyles={styles.flexOne}
          customRowStyles={{ flex: 0.5 }}
          textStyles={styles.rowHeadingText}
          rowStyles={{ backgroundColor: 'white' }}
        />
      ) : (
        <TableRowWrapper
          customLabel={i18n
            .t(label)
            .replace('$', currencySymbol)
            .replace(/\bkg\b/g, weightUnit)}
          rowData={[convertInputNumbersToRegionalBasis(value, 2, true)]}
          customCellStyles={styles.flexOne}
          customRowStyles={{ flex: 0.5 }}
          isRowEven={index % 2 !== 0}
          numberOfLines={2}
        />
      )}
    </View>
  ));
};

const MilkProductionAnalysis = ({ milkTableData = {} }) => {
  const {
    milkProductionSection = [],
    butterfatSection = [],
    secondColumnDataFirst = [],
    lactoseAndOtherSolidsSection = [],
    thirdColumnDataFirst = [],
    proteinSection = [],
    class2LactoseAndOtherSolidsSection = [],
    class2ProteinSection = [],
    deductionsSection = [],
  } = milkTableData;

  return (
    <View>
      <TableRowHeader headerLabel={i18n.t(ROF_FIELDS.MILK_PRODUCTION)} />

      <View style={styles.section}>
        <View style={styles.flexOne}>
          {renderSection(milkProductionSection, false)}
          {renderSection(butterfatSection, true)}
          {renderSection(class2LactoseAndOtherSolidsSection, true)}
        </View>

        <View style={styles.spacer} />

        <View style={styles.flexOne}>
          {renderSection(secondColumnDataFirst, false)}
          {renderSection(lactoseAndOtherSolidsSection, true)}
          {renderSection(class2ProteinSection, true)}
        </View>

        <View style={styles.spacer} />

        <View style={styles.flexOne}>
          {renderSection(thirdColumnDataFirst, false)}
          {renderSection(proteinSection, true)}
          {renderSection(deductionsSection, true)}
        </View>
      </View>
    </View>
  );
};

export default MilkProductionAnalysis;
