import React from 'react';
import { View } from 'react-native';
import { useSelector } from 'react-redux';

import styles from './styles';

import TableRowWrapper from '../../../Components/TableRowWrapper';
import TableRowHeader from '../../../Components/TableRowHeader';

import i18n from '../../../../../../localization/i18n';

import { ROF_FIELDS } from '../../../../../../constants/FormConstants';

import { convertInputNumbersToRegionalBasis } from '../../../../../../helpers/genericHelper';
import {
  getCurrencyForTools,
  getWeightUnitByMeasure,
} from '../../../../../../helpers/appSettingsHelper';

const renderSection = (section = []) => {
  if (!section || section.length === 0) return null;

  const unitOfMeasure = useSelector(
    state => state.visitReport.visitDetails?.unitOfMeasure,
  );
  const currencies = useSelector(state => state.enums.enum?.currencies);
  const selectedCurrency = useSelector(
    state => state.visitReport.visitDetails?.selectedCurrency,
  );

  const weightUnit = getWeightUnitByMeasure(unitOfMeasure);
  const currencySymbol = getCurrencyForTools(currencies, selectedCurrency);

  return section.map(([label, value], index) => (
    <View key={index}>
      <TableRowWrapper
        customLabel={i18n
          .t(label)
          .replace('$', currencySymbol)
          .replace('kg', weightUnit)}
        rowData={[convertInputNumbersToRegionalBasis(value, 2, true)]}
        customCellStyles={styles.flexOne}
        customRowStyles={{ flex: 0.5 }}
        isRowEven={index % 2 !== 0}
        numberOfLines={2}
      />
    </View>
  ));
};

const MilkProductionOutputsAnalysis = ({ milkOutputsTableData = {} }) => {
  const {
    firstColumnSection = [],
    secondColumnSection = [],
    thirdColumnSection = [],
  } = milkOutputsTableData;

  return (
    <View>
      <TableRowHeader
        headerLabel={i18n.t(ROF_FIELDS.MILK_PRODUCTION_OUTPUTS)}
      />

      <View style={styles.section}>
        <View style={styles.flexOne}>{renderSection(firstColumnSection)}</View>

        <View style={styles.spacer} />

        <View style={styles.flexOne}>{renderSection(secondColumnSection)}</View>

        <View style={styles.spacer} />

        <View style={styles.flexOne}>{renderSection(thirdColumnSection)}</View>
      </View>
    </View>
  );
};

export default MilkProductionOutputsAnalysis;
