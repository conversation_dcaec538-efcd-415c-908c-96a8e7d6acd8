// modules
import React, { useEffect, useRef } from 'react';
import { View, ScrollView } from 'react-native';
import { Table } from 'react-native-reanimated-table';
import { useDispatch } from 'react-redux';

// styles
import styles from './styles';

// components
import ToolHeader from '../../../Components/ToolHeader';
import MilkProductionAnalysis from '../MilkProductionAnalysis';
import MilkProductionOutputsAnalysis from '../MilkProductionOutputsAnalysis';
import NotesListing from '../../../Components/NotesListing';
import ToolFooter from '../../../Components/ToolFooter';

// constants
import {
  TOOL_ANALYSIS_TYPES,
  TOOL_TYPES,
} from '../../../../../../constants/AppConstants';

// actions
import { addNewPagePdfPageReference } from '../../../../../../store/actions/visitReport';

// localization
import i18n from '../../../../../../localization/i18n';
import { ROF_FIELDS } from '../../../../../../constants/FormConstants';

const MilkProductionPages = ({
  tool,
  formType,
  milkProductionData,
  milkProductionOutputsData,
  startingPageNumber = 2,
}) => {
  const pageRef = useRef([]);
  const dispatch = useDispatch();

  useEffect(() => {
    setTimeout(() => {
      const pageInfo = {
        pageRef: pageRef,
        toolType: TOOL_TYPES.ROF,
        analysisType: TOOL_ANALYSIS_TYPES.HERD_ANALYSIS,
        pageNumber: startingPageNumber,
      };

      dispatch(addNewPagePdfPageReference(pageInfo));
    }, 100);
  }, [startingPageNumber]);

  const milkProduction = milkProductionData || {};
  const milkProductionOutputs = milkProductionOutputsData || {};

  return (
    <ScrollView
      horizontal
      style={styles.scrollViewStyles}
      contentContainerStyle={styles.scrollViewContainer}>
      <View ref={viewRef => (pageRef[0] = viewRef)} collapsable={false}>
        <ToolHeader
          basicInfo={tool?.basicInfo}
          analysisType={
            formType === ROF_FIELDS.TMR
              ? i18n.t('tmr')
              : i18n.t('individualCow')
          }
          isNotesAvailable={tool?.notes?.length}
        />

        <View style={styles.mainContainer}>
          <View style={styles.bodyContainer}>
            <Table>
              <MilkProductionAnalysis milkTableData={milkProduction} />
              <View style={styles.spacer} />
              <MilkProductionOutputsAnalysis
                milkOutputsTableData={milkProductionOutputs}
              />
            </Table>
          </View>
        </View>
        <ToolFooter isNotesAvailable={false} />
      </View>
    </ScrollView>
  );
};

export default MilkProductionPages;
