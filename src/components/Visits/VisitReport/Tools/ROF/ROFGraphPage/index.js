// modules
import React, { useEffect, useRef } from 'react';
import { View, ScrollView, Text } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';

// styles
import styles from './styles';

// components
import ToolHeader from '../../../Components/ToolHeader';
import RO<PERSON>arGraph from './ROFBarGraph';
import Too<PERSON><PERSON>ooter from '../../../Components/ToolFooter';

// constants
import {
  TOOL_ANALYSIS_TYPES,
  TOOL_TYPES,
} from '../../../../../../constants/AppConstants';
import { ROF_GRAPH_LEGENDS } from '../../../../../../constants/toolsConstants/ROFConstants';

// localization
import i18n from '../../../../../../localization/i18n';

// helpers
import {
  getCurrencyForTools,
  getWeightUnitByMeasure,
} from '../../../../../../helpers/appSettingsHelper';

// actions
import { addNewPagePdfPageReference } from '../../../../../../store/actions/visitReport';

const ROFGraphPage = ({ tool, formType, graphData, graphLabels }) => {
  const pageRef = useRef([]);
  const dispatch = useDispatch();

  const unitOfMeasure = useSelector(
    state => state.visitReport.visitDetails?.unitOfMeasure,
  );
  const currencies = useSelector(state => state.enums.enum?.currencies);
  const selectedCurrency = useSelector(
    state => state.visitReport.visitDetails?.selectedCurrency,
  );

  const weightUnit = getWeightUnitByMeasure(unitOfMeasure);
  const currencySymbol = getCurrencyForTools(currencies, selectedCurrency);

  useEffect(() => {
    setTimeout(() => {
      const pageInfo = {
        pageRef: pageRef,
        toolType: TOOL_TYPES.ROF,
        analysisType: TOOL_ANALYSIS_TYPES.HERD_ANALYSIS,
        pageNumber: 4,
      };

      dispatch(addNewPagePdfPageReference(pageInfo));
    }, 100);
  }, []);

  // Use prepared data passed as props instead of calling helper functions
  const labels = graphLabels || [];

  console.log('This is graph data', graphData);

  const renderGraph = () => {
    if (!graphData || graphData.length === 0) {
      return (
        <View style={styles.noDataContainer}>
          <Text style={styles.noDataText}>
            {i18n.t('No graph data available')}
          </Text>
        </View>
      );
    }

    return (
      <View style={styles.graphContainer}>
        <View style={styles.graphTitleContainer}>
          <Text style={styles.graphTitle}>{i18n.t('ReturnOverFeed')}</Text>
        </View>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <ROFBarGraph
            barWidth={20}
            barOffset={25}
            verticalTickCount={6}
            domainPadding={styles.domainPadding}
            xAxisFormatter={t => {
              return !!t?.length && t?.slice(0, 5);
            }}
            valuesFormatter={t => {
              return !!t > 0 ? parseFloat(t).toFixed(2) : '';
            }}
            yAxisFormatter={t => {
              return parseFloat(t).toFixed(2);
            }}
            verticalAxisLabel={i18n
              .t('returnOverFeedCostPerCowPerDay')
              .replace('$', currencySymbol)
              .replace('kg', weightUnit)}
            data={graphData}
            width={styles.graphWidth}
            labels={labels}
            height={styles.graphHeight}
            customYAxisLabelStyle={styles.yAxisLabel}
            xDomainPadding={{ x: [30, 30] }}
          />
        </ScrollView>
        {/* Legends */}
        <View style={styles.legendContainer}>
          {ROF_GRAPH_LEGENDS.map((legend, index) => (
            <View style={styles.legendItem} key={`${legend.title}-${index}`}>
              <View
                style={[styles.legendCircle, { backgroundColor: legend.color }]}
              />
              <Text style={styles.legendText}>
                {legend.title
                  .replace('$', currencySymbol)
                  .replace('kg', weightUnit)}
              </Text>
            </View>
          ))}
        </View>
        <View style={{ height: styles.extraHeight }} />
      </View>
    );
  };

  return (
    <ScrollView
      horizontal
      style={styles.scrollViewStyles}
      contentContainerStyle={styles.scrollViewContainer}>
      <View ref={viewRef => (pageRef[0] = viewRef)} collapsable={false}>
        <ToolHeader
          basicInfo={tool?.basicInfo}
          analysisType={
            formType === 'tmr' ? i18n.t('tmr') : i18n.t('individualCow')
          }
          isNotesAvailable={tool?.notes?.length}
        />

        <View style={styles.mainContainer}>
          <View style={styles.bodyContainer}>{renderGraph()}</View>
        </View>

        <ToolFooter isNotesAvailable={false} />
      </View>
    </ScrollView>
  );
};

export default ROFGraphPage;
