// modules
import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';

// components
import HerdProfileAndFeeding from './HerdProfileAndFeeding';
import MilkProductionPages from './MilkProductionPages';
import SummaryTablesPage from './SummaryTablesPage';
import ROFGraphPage from './ROFGraphPage';

// helpers
import {
  getAvailableFormTypes,
  prepareROFVisitReportData,
} from '../../../../../helpers/visitReport/rofHelper';

// actions
import { getRecentVisitsForToolRequest } from '../../../../../store/actions/tool';

// constants
import { VISIT_TABLE_FIELDS } from '../../../../../constants/AppConstants';

const ReturnOverFeedPageComponent = ({ tool = {} }) => {
  const dispatch = useDispatch();
  const visitDetails = useSelector(state => state.visitReport.visitDetails);
  const sitePens = useSelector(state => state.visitReport.pens);

  const recentVisits = useSelector(state => state.tool.recentVisits);
  const enumState = useSelector(state => state.enums.enum);

  const comparingRofVisits =
    recentVisits?.map(visit => visit.id || visit.visitId) || [];

  useEffect(() => {
    if (visitDetails) {
      const siteId = visitDetails.siteId || visitDetails.localSiteId;
      const accountId = visitDetails.customerId || visitDetails.localCustomerId;

      dispatch(
        getRecentVisitsForToolRequest({
          siteId,
          accountId,
          localVisitId: visitDetails.id || visitDetails.localId,
          tool: VISIT_TABLE_FIELDS.ROF,
        }),
      );
    }
  }, [visitDetails, dispatch]);

  const formTypes = getAvailableFormTypes({ tool, visitDetails });

  const renderFormTypePages = formType => {
    const data = prepareROFVisitReportData(
      tool,
      visitDetails,
      sitePens,
      formType,
      recentVisits,
      comparingRofVisits,
      enumState,
    );
    const feedingPagesCount = data.feedingPages ? data.feedingPages.length : 1;

    console.log('feedingPagesCount', feedingPagesCount);

    return [
      ...(data.feedingPages && data.feedingPages.length > 0
        ? data.feedingPages.map((feedingPageData, index) => (
            <HerdProfileAndFeeding
              key={`feeding-page-${index}`}
              tool={tool}
              formType={formType}
              herdProfileData={data.herdProfile}
              feedingData={data.feeding}
              feedingPageData={feedingPageData}
              showHerdProfile={index === 0}
              pageNumber={index + 1}
            />
          ))
        : [
            <HerdProfileAndFeeding
              key="feeding-page-legacy"
              tool={tool}
              formType={formType}
              herdProfileData={data.herdProfile}
              feedingData={data.feeding}
              feedingPageData={null}
              showHerdProfile={true}
              pageNumber={1}
            />,
          ]),

      <MilkProductionPages
        key="milk-production"
        tool={tool}
        formType={formType}
        milkProductionData={data.milkProduction}
        milkProductionOutputsData={data.milkProductionOutputs}
        startingPageNumber={feedingPagesCount + 1}
      />,
      <SummaryTablesPage
        key="summary-tables"
        tool={tool}
        formType={formType}
        summaryTablesData={data.summaryTables}
        startingPageNumber={feedingPagesCount + 2}
      />,
      <ROFGraphPage
        key="rof-graph"
        tool={tool}
        formType={formType}
        graphData={data.graph.data}
        graphLabels={data.graph.labels}
        startingPageNumber={feedingPagesCount + 3}
      />,
    ];
  };

  return (
    <>
      {formTypes.length > 0 ? (
        formTypes.flatMap(({ key }) => renderFormTypePages(key))
      ) : (
        <></>
      )}
    </>
  );
};

export default ReturnOverFeedPageComponent;
