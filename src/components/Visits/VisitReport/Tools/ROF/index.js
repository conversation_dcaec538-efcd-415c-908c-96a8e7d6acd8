// modules
import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';

// components
import HerdProfileAndFeeding from './HerdProfileAndFeeding';
import MilkProductionPages from './MilkProductionPages';
import SummaryTablesPage from './SummaryTablesPage';
import ROFGraphPage from './ROFGraphPage';

// helpers
import {
  getAvailableFormTypes,
  prepareROFVisitReportData,
} from '../../../../../helpers/visitReport/rofHelper';

// actions
import { getRecentVisitsForToolRequest } from '../../../../../store/actions/tool';

// constants
import { VISIT_TABLE_FIELDS } from '../../../../../constants/AppConstants';

const ReturnOverFeedPageComponent = ({ tool = {} }) => {
  const dispatch = useDispatch();
  const visitDetails = useSelector(state => state.visitReport.visitDetails);
  const sitePens = useSelector(state => state.visitReport.pens);

  // Use the exact same Redux selectors as the working ROF graph
  const recentVisits = useSelector(state => state.tool.recentVisits);

  // For visit report, we want to show all recent visits, so create comparing visits from all recent visits
  const comparingRofVisits =
    recentVisits?.map(visit => visit.id || visit.visitId) || [];

  // Use the exact same fetch logic as the working ROF graph
  useEffect(() => {
    if (visitDetails) {
      const siteId = visitDetails.siteId || visitDetails.localSiteId;
      const accountId = visitDetails.customerId || visitDetails.localCustomerId;

      dispatch(
        getRecentVisitsForToolRequest({
          siteId,
          accountId,
          localVisitId: visitDetails.id || visitDetails.localId,
          tool: VISIT_TABLE_FIELDS.ROF,
        }),
      );
    }
  }, [visitDetails, dispatch]);

  const formTypes = getAvailableFormTypes({ tool, visitDetails });

  const renderFormTypePages = formType => {
    const data = prepareROFVisitReportData(
      tool,
      visitDetails,
      sitePens,
      formType,
      recentVisits,
      comparingRofVisits,
    );

    return [
      <HerdProfileAndFeeding
        tool={tool}
        formType={formType}
        herdProfileData={data.herdProfile}
        feedingData={data.feeding}
      />,
      <MilkProductionPages
        tool={tool}
        formType={formType}
        milkProductionData={data.milkProduction}
        milkProductionOutputsData={data.milkProductionOutputs}
      />,
      <SummaryTablesPage
        tool={tool}
        formType={formType}
        summaryTablesData={data.summaryTables}
      />,
      <ROFGraphPage
        tool={tool}
        formType={formType}
        graphData={data.graph.data}
        graphLabels={data.graph.labels}
      />,
    ];
  };

  return (
    <>
      {formTypes.length > 0 ? (
        formTypes.map(({ key }) => renderFormTypePages(key))
      ) : (
        <></>
      )}
    </>
  );
};

export default ReturnOverFeedPageComponent;
