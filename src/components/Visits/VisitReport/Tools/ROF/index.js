// modules
import React from 'react';
import { useSelector } from 'react-redux';

// components
import HerdProfileAndFeeding from './HerdProfileAndFeeding';
import MilkProductionPages from './MilkProductionPages';
import SummaryTablesPage from './SummaryTablesPage';
import ROFGraphPage from './ROFGraphPage';

// helpers
import {
  getAvailableFormTypes,
  prepareROFVisitReportData,
} from '../../../../../helpers/visitReport/rofHelper';

const ReturnOverFeedPageComponent = ({ tool = {} }) => {
  const visitDetails = useSelector(state => state.visitReport.visitDetails);
  const sitePens = useSelector(state => state.visitReport.pens);
  // Visit report stores recent visits by tool type in recentVisitsByTools
  const recentVisitsByTools = useSelector(
    state => state.visitReport.recentVisitsByTools,
  );
  const recentVisits =
    recentVisitsByTools?.ReturnOverFeed ||
    recentVisitsByTools?.returnOverFeed ||
    [];

  // Also try selectedTool as fallback
  const selectedToolRecentVisits = useSelector(
    state => state.visitReport.selectedTool.recentVisits,
  );

  // Use whichever has data
  const finalRecentVisits =
    recentVisits.length > 0 ? recentVisits : selectedToolRecentVisits;

  console.log('Debug recent visits sources:');
  console.log('recentVisitsByTools:', recentVisitsByTools);
  console.log('selectedTool.recentVisits:', selectedToolRecentVisits);
  console.log('finalRecentVisits:', finalRecentVisits);

  const formTypes = getAvailableFormTypes({ tool, visitDetails });

  const renderFormTypePages = formType => {
    const data = prepareROFVisitReportData(
      tool,
      visitDetails,
      sitePens,
      formType,
      finalRecentVisits,
      finalRecentVisits, // Use same data for comparing visits
    );

    return [
      <HerdProfileAndFeeding
        tool={tool}
        formType={formType}
        herdProfileData={data.herdProfile}
        feedingData={data.feeding}
      />,
      <MilkProductionPages
        tool={tool}
        formType={formType}
        milkProductionData={data.milkProduction}
        milkProductionOutputsData={data.milkProductionOutputs}
      />,
      <SummaryTablesPage
        tool={tool}
        formType={formType}
        summaryTablesData={data.summaryTables}
      />,
      <ROFGraphPage
        tool={tool}
        formType={formType}
        graphData={data.graph.data}
        graphLabels={data.graph.labels}
      />,
    ];
  };

  return (
    <>
      {formTypes.length > 0 ? (
        formTypes.map(({ key }) => renderFormTypePages(key))
      ) : (
        <></>
      )}
    </>
  );
};

export default ReturnOverFeedPageComponent;
