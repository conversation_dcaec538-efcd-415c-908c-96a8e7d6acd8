// modules
import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';

// components
import HerdProfileAndFeeding from './HerdProfileAndFeeding';
import MilkProductionPages from './MilkProductionPages';
import SummaryTablesPage from './SummaryTablesPage';
import ROFGraphPage from './ROFGraphPage';

// helpers
import {
  getAvailableFormTypes,
  prepareROFVisitReportData,
} from '../../../../../helpers/visitReport/rofHelper';

// actions
import { getRecentVisitsForToolRequest } from '../../../../../store/actions/tool';

// constants
import { VISIT_TABLE_FIELDS } from '../../../../../constants/AppConstants';

const ReturnOverFeedPageComponent = ({ tool = {} }) => {
  const dispatch = useDispatch();
  const visitDetails = useSelector(state => state.visitReport.visitDetails);
  const sitePens = useSelector(state => state.visitReport.pens);

  const recentVisits = useSelector(state => state.tool.recentVisits);
  const enumState = useSelector(state => state.enums.enum);

  const comparingRofVisits =
    recentVisits?.map(visit => visit.id || visit.visitId) || [];

  useEffect(() => {
    if (visitDetails) {
      const siteId = visitDetails.siteId || visitDetails.localSiteId;
      const accountId = visitDetails.customerId || visitDetails.localCustomerId;

      dispatch(
        getRecentVisitsForToolRequest({
          siteId,
          accountId,
          localVisitId: visitDetails.id || visitDetails.localId,
          tool: VISIT_TABLE_FIELDS.ROF,
        }),
      );
    }
  }, [visitDetails, dispatch]);

  const formTypes = getAvailableFormTypes({ tool, visitDetails });

  const renderFormTypePages = formType => {
    const data = prepareROFVisitReportData(
      tool,
      visitDetails,
      sitePens,
      formType,
      recentVisits,
      comparingRofVisits,
      enumState,
    );

    // Calculate the number of feeding pages for dynamic page numbering
    const feedingPagesCount = data.feedingPages ? data.feedingPages.length : 1;

    console.log('ROF Page Numbering:', {
      feedingPagesCount,
      'Pages 1 to': feedingPagesCount + ' (Herd Profile + Feeding)',
      milkProductionPageNumber: feedingPagesCount + 1,
      summaryTablesPageNumber: feedingPagesCount + 2,
      graphPageNumber: feedingPagesCount + 3,
    });

    return [
      <HerdProfileAndFeeding
        tool={tool}
        formType={formType}
        herdProfileData={data.herdProfile}
        feedingData={data.feeding}
        feedingPages={data.feedingPages}
      />,
      <MilkProductionPages
        tool={tool}
        formType={formType}
        milkProductionData={data.milkProduction}
        milkProductionOutputsData={data.milkProductionOutputs}
        startingPageNumber={feedingPagesCount + 1} // Dynamic page number
      />,
      <SummaryTablesPage
        tool={tool}
        formType={formType}
        summaryTablesData={data.summaryTables}
        startingPageNumber={feedingPagesCount + 2} // Dynamic page number
      />,
      <ROFGraphPage
        tool={tool}
        formType={formType}
        graphData={data.graph.data}
        graphLabels={data.graph.labels}
        startingPageNumber={feedingPagesCount + 3} // Dynamic page number
      />,
    ];
  };

  return (
    <>
      {formTypes.length > 0 ? (
        formTypes.map(({ key }) => renderFormTypePages(key))
      ) : (
        <></>
      )}
    </>
  );
};

export default ReturnOverFeedPageComponent;
