// modules
import React from 'react';
import { useSelector } from 'react-redux';

// components
import HerdProfileAndFeeding from './HerdProfileAndFeeding';
import MilkProductionPages from './MilkProductionPages';
import SummaryTablesPage from './SummaryTablesPage';
import ROFGraphPage from './ROFGraphPage';

// helpers
import {
  getAvailableFormTypes,
  prepareROFVisitReportData,
} from '../../../../../helpers/visitReport/rofHelper';

const ReturnOverFeedPageComponent = ({ tool = {} }) => {
  const visitDetails = useSelector(state => state.visitReport.visitDetails);
  const sitePens = useSelector(state => state.visitReport.pens);
  const recentVisits = useSelector(state => state.tool.recentVisits);
  const comparingRofVisits = useSelector(state => state.rof.comparingRofVisits);

  const formTypes = getAvailableFormTypes({ tool, visitDetails });

  const renderFormTypePages = formType => {
    const data = prepareROFVisitReportData(
      tool,
      visitDetails,
      sitePens,
      formType,
      recentVisits,
      comparingRofVisits,
    );

    return [
      <HerdProfileAndFeeding
        tool={tool}
        formType={formType}
        herdProfileData={data.herdProfile}
        feedingData={data.feeding}
      />,
      <MilkProductionPages
        tool={tool}
        formType={formType}
        milkProductionData={data.milkProduction}
        milkProductionOutputsData={data.milkProductionOutputs}
      />,
      <SummaryTablesPage
        tool={tool}
        formType={formType}
        summaryTablesData={data.summaryTables}
      />,
      <ROFGraphPage
        tool={tool}
        formType={formType}
        graphData={data.graph.data}
        graphLabels={data.graph.labels}
      />,
    ];
  };

  return (
    <>
      {formTypes.length > 0 ? (
        formTypes.map(({ key }) => renderFormTypePages(key))
      ) : (
        <></>
      )}
    </>
  );
};

export default ReturnOverFeedPageComponent;
