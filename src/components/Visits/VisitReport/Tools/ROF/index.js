// modules
import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';

// components
import HerdProfileAndFeeding from './HerdProfileAndFeeding';
import MilkProductionPages from './MilkProductionPages';
import SummaryTablesPage from './SummaryTablesPage';
import ROFGraphPage from './ROFGraphPage';

// helpers
import {
  getAvailableFormTypes,
  prepareROFVisitReportData,
} from '../../../../../helpers/visitReport/rofHelper';

// actions
import { getRecentVisitsForToolRequest } from '../../../../../store/actions/tool';

// constants
import { VISIT_TABLE_FIELDS } from '../../../../../constants/AppConstants';

const ReturnOverFeedPageComponent = ({ tool = {} }) => {
  const dispatch = useDispatch();
  const visitDetails = useSelector(state => state.visitReport.visitDetails);
  const sitePens = useSelector(state => state.visitReport.pens);

  // Use the exact same Redux selectors as the working ROF graph
  const recentVisits = useSelector(state => state.tool.recentVisits);
  const comparingRofVisitsFromState = useSelector(
    state => state.rof.comparingRofVisits,
  );

  // For visit report, we want to show all recent visits, so create comparing visits from all recent visits
  const comparingRofVisits =
    recentVisits?.map(visit => visit.id || visit.visitId) || [];

  console.log('Visit Report ROF - Using same selectors as working graph:');
  console.log('recentVisits:', recentVisits);
  console.log('comparingRofVisits:', comparingRofVisits);

  // Check if recent visits have ROF data
  console.log('Checking recent visits for ROF data:');
  recentVisits?.forEach((visit, index) => {
    console.log(`Visit ${index}:`, {
      id: visit.id,
      visitName: visit.visitName,
      hasReturnOverFeed: !!visit.returnOverFeed,
      returnOverFeedData: visit.returnOverFeed ? 'Present' : 'Missing',
      availableFields: Object.keys(visit),
    });
  });

  // Use the exact same fetch logic as the working ROF graph
  useEffect(() => {
    if (visitDetails) {
      console.log('Fetching recent visits using working ROF pattern...');
      console.log('Fetch parameters:');
      console.log('siteId:', visitDetails.siteId || visitDetails.localSiteId);
      console.log(
        'accountId:',
        visitDetails.customerId || visitDetails.localCustomerId,
      );
      console.log('localVisitId:', visitDetails.id || visitDetails.localId);
      console.log('tool:', VISIT_TABLE_FIELDS.ROF);

      const siteId = visitDetails.siteId || visitDetails.localSiteId;
      const accountId = visitDetails.customerId || visitDetails.localCustomerId;

      dispatch(
        getRecentVisitsForToolRequest({
          siteId,
          accountId,
          localVisitId: visitDetails.id || visitDetails.localId,
          tool: VISIT_TABLE_FIELDS.ROF,
        }),
      );
    }
  }, [visitDetails, dispatch]);

  const formTypes = getAvailableFormTypes({ tool, visitDetails });

  const renderFormTypePages = formType => {
    const data = prepareROFVisitReportData(
      tool,
      visitDetails,
      sitePens,
      formType,
      recentVisits,
      comparingRofVisits,
    );

    return [
      <HerdProfileAndFeeding
        tool={tool}
        formType={formType}
        herdProfileData={data.herdProfile}
        feedingData={data.feeding}
      />,
      <MilkProductionPages
        tool={tool}
        formType={formType}
        milkProductionData={data.milkProduction}
        milkProductionOutputsData={data.milkProductionOutputs}
      />,
      <SummaryTablesPage
        tool={tool}
        formType={formType}
        summaryTablesData={data.summaryTables}
      />,
      <ROFGraphPage
        tool={tool}
        formType={formType}
        graphData={data.graph.data}
        graphLabels={data.graph.labels}
      />,
    ];
  };

  return (
    <>
      {formTypes.length > 0 ? (
        formTypes.map(({ key }) => renderFormTypePages(key))
      ) : (
        <></>
      )}
    </>
  );
};

export default ReturnOverFeedPageComponent;
