// modules
import React from 'react';
import { View, Text } from 'react-native';
import {
  VictoryChart,
  VictoryGroup,
  VictoryAxis,
  VictoryBar,
  VictoryLabel,
  VictoryStack,
} from 'victory-native';

// styles
import styles from './styles';
import { convertInputNumbersToRegionalBasis } from '../../../helpers/genericHelper';

const ManureScreenerBarGraph = ({
  data,
  labels,
  width,
  height,
  barWidth,
  barOffset,
  verticalTickCount,
  verticalAxisLabel,
  customXAxisStyle,
  customYAxisStyle,
  customYAxisLabelStyle,
  customHorizontalLabelStyle,
  customVerticalLabelStyle,
  customBarLabelStyle,
  xAxisFormatter,
  yAxisFormatter,
  valuesFormatter,
  xDomainPadding,
  useTiltedLabels,
  hasYOffset,
  domainPadding,
  domain,
}) => {
  // render y-axis
  const renderVerticalAxis = graphData => {
    if (graphData?.length > 0) {
      return (
        <VictoryAxis
          dependentAxis
          style={{
            axis: { ...styles.axisStyles, ...customYAxisStyle },
            // axisLabel: { ...styles.axisLabelStyles, ...customYAxisLabelStyle },
            tickLabels: {
              ...styles.verticalLabels,
              ...customVerticalLabelStyle,
            },
          }}
          domain={domain || { y: [0, 100] }}
          tickCount={verticalTickCount || 10}
          tickFormat={
            yAxisFormatter
              ? t =>
                  convertInputNumbersToRegionalBasis(yAxisFormatter(t), 1, true)
              : t => convertInputNumbersToRegionalBasis(t, 1, true)
          }
        />
      );
    }
  };

  // render x-axis
  const renderHorizontalAxis = data => {
    if (data?.length > 0) {
      return (
        <VictoryAxis
          orientation="bottom"
          offsetY={50}
          style={{
            axis: {
              ...styles.axisStyles,
              ...customXAxisStyle,
              ...{
                strokeWidth: 0,
              },
            },
            tickLabels: {
              ...styles.horizontalLabels,
              ...customHorizontalLabelStyle,
            },
          }}
          domainPadding={xDomainPadding ? xDomainPadding : null}
          tickFormat={xAxisFormatter ? t => xAxisFormatter(t) : t => t}
        />
      );
    }
  };

  const renderHorizontalZeroAxis = () => {
    return (
      <VictoryAxis
        tickFormat={t => ''}
        axisValue={0}
        style={{
          axis: { ...styles.axisStyles, ...customXAxisStyle },
          tickLabels: {
            ...styles.horizontalLabels,
            ...customHorizontalLabelStyle,
          },
        }}
      />
    );
  };

  return (
    <View style={styles.container}>
      <Text style={{ ...styles.axisLabelStyles, ...customYAxisLabelStyle }}>
        {verticalAxisLabel}
      </Text>
      <VictoryChart
        height={height && height}
        width={width && width}
        // domain={{ y: [0, 100] }}
        domainPadding={domainPadding ? domainPadding : { x: [20, 25] }}>
        {renderHorizontalAxis(labels)}
        {renderVerticalAxis(data)}
        {renderHorizontalZeroAxis()}
        <VictoryGroup offset={barOffset || 22}>
          {data?.length > 0
            ? data.map((item, index) => {
                return useTiltedLabels ? (
                  <VictoryBar
                    key={index}
                    barRatio={0.7}
                    data={item.dataPoints}
                    style={{
                      data: { fill: item.onScreeColor },
                      labels: { ...styles.barLabels, ...customBarLabelStyle },
                    }}
                    barWidth={barWidth || 12}
                    labels={
                      valuesFormatter
                        ? ({ datum }) =>
                            convertInputNumbersToRegionalBasis(
                              valuesFormatter(datum?.onScreen),
                              1,
                              true,
                            )
                        : ({ datum }) =>
                            convertInputNumbersToRegionalBasis(
                              datum?.onScreen || '',
                              1,
                              true,
                            )
                    }
                    labelComponent={
                      hasYOffset ? (
                        <VictoryLabel
                          angle={-75}
                          dx={5}
                          dy={0}
                          textAnchor="start"
                        />
                      ) : (
                        <VictoryLabel angle={-75} dx={5} textAnchor="start" />
                      )
                    }
                  />
                ) : (
                  <VictoryBar
                    data={item.dataPoints}
                    key={index}
                    style={{
                      data: { fill: item.barColor },
                      labels: { ...styles.barLabels, ...customBarLabelStyle },
                    }}
                    barWidth={barWidth || 12}
                    labels={
                      valuesFormatter
                        ? ({ datum }) =>
                            convertInputNumbersToRegionalBasis(
                              valuesFormatter(datum.y),
                              1,
                              true,
                            )
                        : ({ datum }) =>
                            convertInputNumbersToRegionalBasis(datum.y, 1, true)
                    }
                  />
                );
              })
            : null}
        </VictoryGroup>
      </VictoryChart>
    </View>
  );
};

export default ManureScreenerBarGraph;
