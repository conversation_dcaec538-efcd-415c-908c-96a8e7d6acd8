import {
  DATE_FORMATS,
  DECIMAL_PLACES,
  ENUM_CONSTANTS,
  VISIT_TABLE_FIELDS,
} from '../constants/AppConstants';
import {
  MILK_SOLID_EVALUATION_FIELDS,
  ROF_FIELDS,
} from '../constants/FormConstants';
import customColor from '../constants/theme/variables/customColor';
import {
  ROF_MILKING_INGREDIENTS_TYPES,
  ROF_ONE_DECIMAL_PLACES,
  ROF_PRICE_LIST_TYPES,
  ROF_DECIMAL_PLACES,
  ROF_FORM_TYPES,
  ROF_FEEDING_INGREDIENTS_TYPES,
} from '../constants/toolsConstants/ROFConstants';
import i18n from '../localization/i18n';

import {
  convertNumberToString,
  convertStringToNumber,
  stringIsEmpty,
} from './alphaNumericHelper';
import { dateHelper, getFormattedDate } from './dateHelper';
import { getParsedToolData } from './genericHelper';
import { logEvent } from './logHelper';

//#region initialize ROF data
export const initializeROFFormData = (
  formType,
  siteData = {},
  visitData = {},
  enumState,
  unit,
  isEditable,
  rofPriceList,
  previousROFVisitData = {},
) => {
  try {
    let toolData = visitData?.[VISIT_TABLE_FIELDS.ROF];

    if (typeof toolData == 'string' && !stringIsEmpty(toolData)) {
      toolData = getParsedToolData(toolData);
      toolData = toolData[formType] || {};
    } else {
      toolData = toolData ? toolData[formType] : {};
    }

    if (previousROFVisitData) {
      previousROFVisitData = previousROFVisitData?.[VISIT_TABLE_FIELDS.ROF];
      if (
        previousROFVisitData &&
        typeof previousROFVisitData == 'string' &&
        !stringIsEmpty(previousROFVisitData)
      ) {
        previousROFVisitData = getParsedToolData(previousROFVisitData);
        previousROFVisitData = previousROFVisitData[formType] || {};
      } else {
        previousROFVisitData = previousROFVisitData
          ? previousROFVisitData[formType]
          : {};
      }
    }

    let formObject = {
      [ROF_FIELDS.HERD_PROFILE]: getHerdProfileInitialFormValues(
        toolData,
        previousROFVisitData,
      ),

      //feeding
      [ROF_FIELDS.FEEDING]: getFeedingInitialFormValues(
        toolData,
        unit,
        siteData,
        enumState,
        isEditable,
        rofPriceList,
        previousROFVisitData,
      ),
      [ROF_FIELDS.MILK_PRODUCTION]: getMilkProductionInitialFormValues(
        toolData,
        unit,
        visitData,
        rofPriceList,
        previousROFVisitData,
      ),
      [ROF_FIELDS.MILK_PRODUCTION_OUTPUTS]: {
        [ROF_FIELDS.MAX_ALLOWED]: toolData[
          ROF_FIELDS.MILK_PRODUCTION_OUTPUTS
        ]?.[ROF_FIELDS.MAX_ALLOWED]
          ? convertNumberToString(
              toolData[ROF_FIELDS.MILK_PRODUCTION_OUTPUTS][
                ROF_FIELDS.MAX_ALLOWED
              ],
            )
          : previousROFVisitData[ROF_FIELDS.MILK_PRODUCTION_OUTPUTS]?.[
              ROF_FIELDS.MAX_ALLOWED
            ]
          ? convertNumberToString(
              previousROFVisitData[ROF_FIELDS.MILK_PRODUCTION_OUTPUTS][
                ROF_FIELDS.MAX_ALLOWED
              ],
            )
          : '',
      },
      [ROF_FIELDS.SUMMARY]: getSummaryPreviousVisitData(
        toolData,
        unit,
        previousROFVisitData,
      ),
    };

    return formObject;
  } catch (e) {
    console.log('initializeROFFormData fail', e);
    logEvent('initializeROFFormData fail', e);
  }
};

const getHerdProfileInitialFormValues = (
  toolData = null,
  previousROFVisitData = null,
) => {
  try {
    let toolHerdProfileData = toolData[ROF_FIELDS.HERD_PROFILE] || {};
    let previousVisitHerdProfileData =
      previousROFVisitData?.[ROF_FIELDS.HERD_PROFILE] || {};

    return {
      [ROF_FIELDS.BREED]:
        toolHerdProfileData[ROF_FIELDS.BREED] ||
        previousVisitHerdProfileData?.[ROF_FIELDS.BREED] ||
        '',
      [ROF_FIELDS.OTHER_BREED_TYPE]:
        toolHerdProfileData[ROF_FIELDS.OTHER_BREED_TYPE] ||
        previousVisitHerdProfileData?.[ROF_FIELDS.OTHER_BREED_TYPE] ||
        '',
      [ROF_FIELDS.FEEDING_TYPE]:
        toolHerdProfileData[ROF_FIELDS.FEEDING_TYPE] ||
        previousVisitHerdProfileData?.[ROF_FIELDS.FEEDING_TYPE] ||
        '',
      [ROF_FIELDS.NUMBER_OF_TMR_GROUPS]: toolHerdProfileData[
        ROF_FIELDS.NUMBER_OF_TMR_GROUPS
      ]
        ? convertNumberToString(
            toolHerdProfileData[ROF_FIELDS.NUMBER_OF_TMR_GROUPS],
          )
        : previousVisitHerdProfileData?.[ROF_FIELDS.NUMBER_OF_TMR_GROUPS]
        ? convertNumberToString(
            previousVisitHerdProfileData?.[ROF_FIELDS.NUMBER_OF_TMR_GROUPS],
          )
        : '',
      [ROF_FIELDS.TYPE_OF_SUPPLEMENT]:
        toolHerdProfileData[ROF_FIELDS.TYPE_OF_SUPPLEMENT] ||
        previousVisitHerdProfileData?.[ROF_FIELDS.TYPE_OF_SUPPLEMENT] ||
        '',
      [ROF_FIELDS.COOL_AID]:
        toolHerdProfileData[ROF_FIELDS.COOL_AID] ||
        previousVisitHerdProfileData?.[ROF_FIELDS.COOL_AID] ||
        false,
      [ROF_FIELDS.FORTISSA_FIT]:
        toolHerdProfileData[ROF_FIELDS.FORTISSA_FIT] ||
        previousVisitHerdProfileData?.[ROF_FIELDS.FORTISSA_FIT] ||
        false,
      [ROF_FIELDS.MUN]: toolHerdProfileData[ROF_FIELDS.MUN]
        ? convertNumberToString(toolHerdProfileData[ROF_FIELDS.MUN])
        : previousVisitHerdProfileData?.[ROF_FIELDS.MUN]
        ? convertNumberToString(previousVisitHerdProfileData?.[ROF_FIELDS.MUN])
        : '',
      [ROF_FIELDS.MILKING_PER_DAY]: toolHerdProfileData[
        ROF_FIELDS.MILKING_PER_DAY
      ]
        ? convertNumberToString(toolHerdProfileData[ROF_FIELDS.MILKING_PER_DAY])
        : previousVisitHerdProfileData?.[ROF_FIELDS.MILKING_PER_DAY]
        ? convertNumberToString(
            previousVisitHerdProfileData?.[ROF_FIELDS.MILKING_PER_DAY],
          )
        : '',
    };
  } catch (error) {
    console.log('getHerdProfileInitialFormValues fail', error);
    logEvent('getHerdProfileInitialFormValues fail', error);
  }
};

const getFeedingInitialFormValues = (
  toolData = null,
  unit,
  siteData,
  enumState,
  isEditable,
  rofPriceList,
  previousROFVisitData = null,
) => {
  try {
    let toolFeedingData = toolData?.[ROF_FIELDS.FEEDING] || {};
    let previousVisitFeedingData =
      previousROFVisitData?.[ROF_FIELDS.FEEDING] || {};

    return {
      [ROF_FIELDS.LACTATING_COWS]:
        toolFeedingData[ROF_FIELDS.LACTATING_COWS] || siteData?.lactatingAnimal
          ? convertNumberToString(
              toolFeedingData[ROF_FIELDS.LACTATING_COWS] ||
                siteData?.lactatingAnimal,
            )
          : previousVisitFeedingData?.[ROF_FIELDS.LACTATING_COWS]
          ? convertNumberToString(
              previousVisitFeedingData?.[ROF_FIELDS.LACTATING_COWS],
            )
          : '',
      [ROF_FIELDS.DAYS_IN_MILK]:
        toolFeedingData[ROF_FIELDS.DAYS_IN_MILK] || siteData?.daysInMilk
          ? convertNumberToString(
              toolFeedingData[ROF_FIELDS.DAYS_IN_MILK] || siteData?.daysInMilk,
            )
          : previousVisitFeedingData?.[ROF_FIELDS.DAYS_IN_MILK]
          ? convertNumberToString(
              previousVisitFeedingData?.[ROF_FIELDS.DAYS_IN_MILK],
            )
          : '',

      //feeding ingredients
      [ROF_FIELDS.HOME_GROWN_FORAGES]: getFeedingIngredientsByType(
        ROF_FIELDS.HOME_GROWN_FORAGES,
        toolData,
        enumState,
        isEditable,
        rofPriceList[ROF_PRICE_LIST_TYPES.HOME_GROWN_FORAGES],
        previousVisitFeedingData,
      ),
      [ROF_FIELDS.HOME_GROWN_GRAINS]: getFeedingIngredientsByType(
        ROF_FIELDS.HOME_GROWN_GRAINS,
        toolData,
        enumState,
        isEditable,
        rofPriceList[ROF_PRICE_LIST_TYPES.HOME_GROWN_GRAINS],
        previousVisitFeedingData,
      ),
      [ROF_FIELDS.PURCHASE_BULK_FEED]: getFeedingIngredientsByType(
        ROF_FIELDS.PURCHASE_BULK_FEED,
        toolData,
        enumState,
        isEditable,
        [],
        previousVisitFeedingData,
      ),
      [ROF_FIELDS.PURCHASE_BAG_FEED]: getFeedingIngredientsByType(
        ROF_FIELDS.PURCHASE_BAG_FEED,
        toolData,
        enumState,
        isEditable,
        [],
        previousVisitFeedingData,
      ),
    };
  } catch (error) {
    console.log('getFeedingInitialFormValues fail', error);
    logEvent('getFeedingInitialFormValues fail', error);
  }
};

const getFeedingIngredientsByType = (
  type,
  toolData = {},
  enumState,
  isEditable = false,
  rofPriceList = [],
  previousVisitFeedingData = {},
) => {
  let data = [];

  try {
    toolData[ROF_FIELDS.FEEDING]?.[type]?.length > 0 &&
      toolData[ROF_FIELDS.FEEDING]?.[type]?.map(item => {
        data.push(
          getFeedingIngredientsFormValues(type, enumState, item, rofPriceList),
        );
      });

    if (isEditable) {
      if (data.length == 0) {
        // fetch feeding from previous visit if there's none from current
        previousVisitFeedingData?.[type]?.length > 0 &&
          previousVisitFeedingData?.[type]?.map(item => {
            data.push(
              getFeedingIngredientsFormValues(
                type,
                enumState,
                item,
                rofPriceList,
              ),
            );
          });

        //create default ones for following if nothing comes from previous visit too
        if (
          data.length == 0 &&
          (type == ROF_FIELDS.HOME_GROWN_FORAGES ||
            type == ROF_FIELDS.HOME_GROWN_GRAINS)
        ) {
          //add atleast one form for these two if form is editable
          data.push(
            getFeedingIngredientsFormValues(type, enumState, {}, rofPriceList),
          );
        }
      }
    }
  } catch (e) {
    console.log('getFeedingIngredientsByType fail', e);
    logEvent('getFeedingIngredientsByType fail', e);
  } finally {
    return data;
  }
};

const getFeedingIngredientsFormValues = (
  type,
  enumState,
  item = {},
  rofPriceList = [],
) => {
  try {
    let formObject = {};
    let pricePerTon = 0;
    switch (type) {
      case ROF_FIELDS.HOME_GROWN_FORAGES:
        formObject[ROF_FIELDS.HOME_GROWN_FORAGE_TYPE] =
          item[ROF_FIELDS.HOME_GROWN_FORAGE_TYPE] ||
          enumState?.[ENUM_CONSTANTS.HOME_GROWN_FORAGE_TYPES][0].key;
        formObject[ROF_FIELDS.FORAGE_NAME] = item[ROF_FIELDS.FORAGE_NAME] || '';
        pricePerTon = rofPriceList?.filter(p => {
          return p.name == formObject[ROF_FIELDS.HOME_GROWN_FORAGE_TYPE];
        })[0]?.price;

        break;
      case ROF_FIELDS.HOME_GROWN_GRAINS:
        formObject[ROF_FIELDS.HOME_GROWN_GRAINS_TYPE] =
          item[ROF_FIELDS.HOME_GROWN_GRAINS_TYPE] ||
          enumState?.[ENUM_CONSTANTS.HOME_GROWN_GRAIN_TYPES][0].key;
        formObject[ROF_FIELDS.GRAINS_NAME] = item[ROF_FIELDS.GRAINS_NAME] || '';
        pricePerTon = rofPriceList?.filter(p => {
          return p.name == formObject[ROF_FIELDS.HOME_GROWN_GRAINS_TYPE];
        })[0]?.price;
        break;
      case ROF_FIELDS.PURCHASE_BULK_FEED:
      case ROF_FIELDS.PURCHASE_BAG_FEED:
        formObject[ROF_FIELDS.FEED_NAME] = item[ROF_FIELDS.FEED_NAME] || '';
        break;
    }

    formObject[ROF_FIELDS.TOTAL_HERD_PER_DAY] =
      convertNumberToString(item[ROF_FIELDS.TOTAL_HERD_PER_DAY]) || '';
    formObject[ROF_FIELDS.DRY_MATTER] =
      convertNumberToString(item[ROF_FIELDS.DRY_MATTER]) || '';
    formObject[ROF_FIELDS.TOTAL_DRY_MATTER] =
      convertNumberToString(item[ROF_FIELDS.TOTAL_DRY_MATTER]) || '';
    formObject[ROF_FIELDS.PRICE_PER_TON] =
      (typeof item[ROF_FIELDS.PRICE_PER_TON] == 'string' &&
      !stringIsEmpty(item[ROF_FIELDS.PRICE_PER_TON])
        ? item[ROF_FIELDS.PRICE_PER_TON]
        : convertNumberToString(item[ROF_FIELDS.PRICE_PER_TON]) ||
          convertNumberToString(pricePerTon)) || '';

    return formObject;
  } catch (e) {
    console.log('getFeedingIngredientsFormValues fail', e);
    logEvent('getFeedingIngredientsFormValues fail', e);
  }
};

const getMilkPricePerTonValueByType = (rofPriceList = [], type) => {
  try {
    let item = rofPriceList?.[ROF_PRICE_LIST_TYPES.MILK_PRICE_PER_TON]?.find(
      i => i.name === type,
    );

    return item
      ? convertNumberToString(parseFloat(item.price.toFixed(DECIMAL_PLACES)))
      : '';
  } catch (e) {
    console.log('getMilkPricePerTonValueByType fail', e);
    logEvent('getMilkPricePerTonValueByType fail', e);
  }
};

const getMilkProductionInitialFormValues = (
  toolData = {},
  unit,
  visitData,
  rofPriceList = [],
  previousROFVisitData = {},
) => {
  try {
    let toolMilkProductionData = toolData?.[ROF_FIELDS.MILK_PRODUCTION] || {};
    let previousVisitMilkProductionData =
      previousROFVisitData?.[ROF_FIELDS.MILK_PRODUCTION] || {};

    let milkSoldEvaluationData = {};
    //handle milk sold data
    if (!stringIsEmpty(visitData?.[VISIT_TABLE_FIELDS.MILK_SOLD_EVALUATION])) {
      milkSoldEvaluationData =
        typeof visitData?.[VISIT_TABLE_FIELDS.MILK_SOLD_EVALUATION] == 'string'
          ? JSON.parse(visitData?.[VISIT_TABLE_FIELDS.MILK_SOLD_EVALUATION])
          : visitData?.[VISIT_TABLE_FIELDS.MILK_SOLD_EVALUATION];
    }
    let averageMilkProductionAnimalsInTankKg =
        toolMilkProductionData?.[ROF_FIELDS.AVERAGE_MILK_PRODUCTION_KG] ||
        milkSoldEvaluationData?.[MILK_SOLID_EVALUATION_FIELDS.OUT_PUT]
          ?.averageMilkProductionAnimalsTank ||
        previousVisitMilkProductionData?.[
          ROF_FIELDS.AVERAGE_MILK_PRODUCTION_KG
        ] ||
        0,
      lactatingCows =
        toolData?.[ROF_FIELDS.FEEDING]?.[ROF_FIELDS.LACTATING_COWS] ||
        previousROFVisitData?.[ROF_FIELDS.FEEDING]?.[ROF_FIELDS.LACTATING_COWS],
      averageMilkProductionLitresPerCowPerDay =
        calculateAverageMilkProductionPerCowPerDay(
          averageMilkProductionAnimalsInTankKg,
          lactatingCows,
        );

    return {
      [ROF_FIELDS.AVERAGE_MILK_PRODUCTION_KG]: convertNumberToString(
        averageMilkProductionAnimalsInTankKg,
      ),
      [ROF_FIELDS.MILK_PRODUCTION_KG]: convertNumberToString(
        averageMilkProductionLitresPerCowPerDay,
      ),
      [ROF_FIELDS.KG_OF_QUOTA_PER_DAY]: toolMilkProductionData?.[
        ROF_FIELDS.KG_OF_QUOTA_PER_DAY
      ]
        ? convertNumberToString(
            toolMilkProductionData?.[ROF_FIELDS.KG_OF_QUOTA_PER_DAY],
          )
        : previousVisitMilkProductionData?.[ROF_FIELDS.KG_OF_QUOTA_PER_DAY]
        ? convertNumberToString(
            previousVisitMilkProductionData?.[ROF_FIELDS.KG_OF_QUOTA_PER_DAY],
          )
        : '',
      [ROF_FIELDS.INCENTIVE_DAYS_KG_PER_DAY]: toolMilkProductionData?.[
        ROF_FIELDS.INCENTIVE_DAYS_KG_PER_DAY
      ]
        ? convertNumberToString(
            toolMilkProductionData?.[ROF_FIELDS.INCENTIVE_DAYS_KG_PER_DAY],
          )
        : previousVisitMilkProductionData?.[
            ROF_FIELDS.INCENTIVE_DAYS_KG_PER_DAY
          ]
        ? convertNumberToString(
            previousVisitMilkProductionData?.[
              ROF_FIELDS.INCENTIVE_DAYS_KG_PER_DAY
            ],
          )
        : '',
      [ROF_FIELDS.TOTAL_QUOTA_KG_PER_DAY]: toolMilkProductionData?.[
        ROF_FIELDS.TOTAL_QUOTA_KG_PER_DAY
      ]
        ? convertNumberToString(
            toolMilkProductionData?.[ROF_FIELDS.TOTAL_QUOTA_KG_PER_DAY],
          )
        : previousVisitMilkProductionData?.[ROF_FIELDS.TOTAL_QUOTA_KG_PER_DAY]
        ? convertNumberToString(
            previousVisitMilkProductionData?.[
              ROF_FIELDS.TOTAL_QUOTA_KG_PER_DAY
            ],
          )
        : '',
      [ROF_FIELDS.CURRENT_QUOTA_UTILIZATION_KG_PER_DAY]: convertNumberToString(
        calculateCurrentQuotaUtilizationKgPerDay(
          toolMilkProductionData?.[ROF_FIELDS.AVERAGE_MILK_PRODUCTION_KG] ||
            previousVisitMilkProductionData?.[
              ROF_FIELDS.AVERAGE_MILK_PRODUCTION_KG
            ],
          toolMilkProductionData?.[ROF_FIELDS.BUTTERFAT]?.[
            ROF_FIELDS.PRICE_PER_HL
          ] ||
            previousVisitMilkProductionData?.[ROF_FIELDS.BUTTERFAT]?.[
              ROF_FIELDS.PRICE_PER_HL
            ],
        ),
      ),
      [ROF_FIELDS.BUTTERFAT]: {
        [ROF_FIELDS.PRICE_PER_KG]: toolMilkProductionData?.[
          ROF_FIELDS.BUTTERFAT
        ]?.[ROF_FIELDS.PRICE_PER_KG]
          ? convertNumberToString(
              toolMilkProductionData?.[ROF_FIELDS.BUTTERFAT][
                ROF_FIELDS.PRICE_PER_KG
              ],
            )
          : previousVisitMilkProductionData?.[ROF_FIELDS.BUTTERFAT]?.[
              ROF_FIELDS.PRICE_PER_KG
            ]
          ? convertNumberToString(
              previousVisitMilkProductionData?.[ROF_FIELDS.BUTTERFAT][
                ROF_FIELDS.PRICE_PER_KG
              ],
            )
          : getMilkPricePerTonValueByType(rofPriceList, ROF_FIELDS.BUTTERFAT),
        [ROF_FIELDS.PRICE_PER_HL]: toolMilkProductionData?.[
          ROF_FIELDS.BUTTERFAT
        ]?.[ROF_FIELDS.PRICE_PER_HL]
          ? convertNumberToString(
              toolMilkProductionData?.[ROF_FIELDS.BUTTERFAT][
                ROF_FIELDS.PRICE_PER_HL
              ],
            )
          : previousVisitMilkProductionData?.[ROF_FIELDS.BUTTERFAT]?.[
              ROF_FIELDS.PRICE_PER_HL
            ]
          ? convertNumberToString(
              previousVisitMilkProductionData?.[ROF_FIELDS.BUTTERFAT][
                ROF_FIELDS.PRICE_PER_HL
              ],
            )
          : '',
        [ROF_FIELDS.PRICE_PER_KG_PER_COW]: convertNumberToString(
          calculatePriceKgPerCow(
            averageMilkProductionLitresPerCowPerDay,
            toolMilkProductionData?.[ROF_FIELDS.BUTTERFAT]?.[
              ROF_FIELDS.PRICE_PER_HL
            ] ||
              previousVisitMilkProductionData?.[ROF_FIELDS.BUTTERFAT]?.[
                ROF_FIELDS.PRICE_PER_HL
              ],
          ),
        ),
      },

      [ROF_FIELDS.PROTEIN]: {
        [ROF_FIELDS.PRICE_PER_KG]: toolMilkProductionData?.[
          ROF_FIELDS.PROTEIN
        ]?.[ROF_FIELDS.PRICE_PER_KG]
          ? convertNumberToString(
              toolMilkProductionData?.[ROF_FIELDS.PROTEIN][
                ROF_FIELDS.PRICE_PER_KG
              ],
            )
          : previousVisitMilkProductionData?.[ROF_FIELDS.PROTEIN]?.[
              ROF_FIELDS.PRICE_PER_KG
            ]
          ? convertNumberToString(
              previousVisitMilkProductionData?.[ROF_FIELDS.PROTEIN][
                ROF_FIELDS.PRICE_PER_KG
              ],
            )
          : getMilkPricePerTonValueByType(rofPriceList, ROF_FIELDS.PROTEIN),
        [ROF_FIELDS.PRICE_PER_HL]: toolMilkProductionData?.[
          ROF_FIELDS.PROTEIN
        ]?.[ROF_FIELDS.PRICE_PER_HL]
          ? convertNumberToString(
              toolMilkProductionData?.[ROF_FIELDS.PROTEIN][
                ROF_FIELDS.PRICE_PER_HL
              ],
            )
          : previousVisitMilkProductionData?.[ROF_FIELDS.PROTEIN]?.[
              ROF_FIELDS.PRICE_PER_HL
            ]
          ? convertNumberToString(
              previousVisitMilkProductionData?.[ROF_FIELDS.PROTEIN][
                ROF_FIELDS.PRICE_PER_HL
              ],
            )
          : '',
        [ROF_FIELDS.PRICE_PER_KG_PER_COW]: convertNumberToString(
          calculatePriceKgPerCow(
            averageMilkProductionLitresPerCowPerDay,
            toolMilkProductionData?.[ROF_FIELDS.PROTEIN]?.[
              ROF_FIELDS.PRICE_PER_HL
            ] ||
              previousVisitMilkProductionData?.[ROF_FIELDS.PROTEIN]?.[
                ROF_FIELDS.PRICE_PER_HL
              ],
          ),
        ),
      },

      [ROF_FIELDS.LACTOSE_AND_OTHER_SOLIDS]: {
        [ROF_FIELDS.PRICE_PER_KG]: toolMilkProductionData?.[
          ROF_FIELDS.LACTOSE_AND_OTHER_SOLIDS
        ]?.[ROF_FIELDS.PRICE_PER_KG]
          ? convertNumberToString(
              toolMilkProductionData?.[ROF_FIELDS.LACTOSE_AND_OTHER_SOLIDS][
                ROF_FIELDS.PRICE_PER_KG
              ],
            )
          : previousVisitMilkProductionData?.[
              ROF_FIELDS.LACTOSE_AND_OTHER_SOLIDS
            ]?.[ROF_FIELDS.PRICE_PER_KG]
          ? convertNumberToString(
              previousVisitMilkProductionData?.[
                ROF_FIELDS.LACTOSE_AND_OTHER_SOLIDS
              ][ROF_FIELDS.PRICE_PER_KG],
            )
          : getMilkPricePerTonValueByType(
              rofPriceList,
              ROF_FIELDS.LACTOSE_AND_OTHER_SOLIDS,
            ),
        [ROF_FIELDS.PRICE_PER_HL]: toolMilkProductionData?.[
          ROF_FIELDS.LACTOSE_AND_OTHER_SOLIDS
        ]?.[ROF_FIELDS.PRICE_PER_HL]
          ? convertNumberToString(
              toolMilkProductionData?.[ROF_FIELDS.LACTOSE_AND_OTHER_SOLIDS][
                ROF_FIELDS.PRICE_PER_HL
              ],
            )
          : previousVisitMilkProductionData?.[
              ROF_FIELDS.LACTOSE_AND_OTHER_SOLIDS
            ]?.[ROF_FIELDS.PRICE_PER_HL]
          ? convertNumberToString(
              previousVisitMilkProductionData?.[
                ROF_FIELDS.LACTOSE_AND_OTHER_SOLIDS
              ][ROF_FIELDS.PRICE_PER_HL],
            )
          : '',
        [ROF_FIELDS.PRICE_PER_KG_PER_COW]: convertNumberToString(
          calculatePriceKgPerCow(
            averageMilkProductionLitresPerCowPerDay,
            toolMilkProductionData?.[ROF_FIELDS.LACTOSE_AND_OTHER_SOLIDS]?.[
              ROF_FIELDS.PRICE_PER_HL
            ] ||
              previousVisitMilkProductionData?.[
                ROF_FIELDS.LACTOSE_AND_OTHER_SOLIDS
              ]?.[ROF_FIELDS.PRICE_PER_HL],
          ),
        ),
      },

      [ROF_FIELDS.CLASS2_PROTEIN]: {
        [ROF_FIELDS.PRICE_PER_KG]: toolMilkProductionData?.[
          ROF_FIELDS.CLASS2_PROTEIN
        ]?.[ROF_FIELDS.PRICE_PER_KG]
          ? convertNumberToString(
              toolMilkProductionData?.[ROF_FIELDS.CLASS2_PROTEIN][
                ROF_FIELDS.PRICE_PER_KG
              ],
            )
          : previousVisitMilkProductionData?.[ROF_FIELDS.CLASS2_PROTEIN]?.[
              ROF_FIELDS.PRICE_PER_KG
            ]
          ? convertNumberToString(
              previousVisitMilkProductionData?.[ROF_FIELDS.CLASS2_PROTEIN][
                ROF_FIELDS.PRICE_PER_KG
              ],
            )
          : getMilkPricePerTonValueByType(
              rofPriceList,
              ROF_FIELDS.CLASS2_PROTEIN,
            ),
      },

      [ROF_FIELDS.CLASS2_LACTOSE_AND_OTHER_SOLIDS]: {
        [ROF_FIELDS.PRICE_PER_KG]: toolMilkProductionData?.[
          ROF_FIELDS.CLASS2_LACTOSE_AND_OTHER_SOLIDS
        ]?.[ROF_FIELDS.PRICE_PER_KG]
          ? convertNumberToString(
              toolMilkProductionData?.[
                ROF_FIELDS.CLASS2_LACTOSE_AND_OTHER_SOLIDS
              ][ROF_FIELDS.PRICE_PER_KG],
            )
          : previousVisitMilkProductionData?.[
              ROF_FIELDS.CLASS2_LACTOSE_AND_OTHER_SOLIDS
            ]?.[ROF_FIELDS.PRICE_PER_KG]
          ? convertNumberToString(
              previousVisitMilkProductionData?.[
                ROF_FIELDS.CLASS2_LACTOSE_AND_OTHER_SOLIDS
              ][ROF_FIELDS.PRICE_PER_KG],
            )
          : getMilkPricePerTonValueByType(
              rofPriceList,
              ROF_FIELDS.CLASS2_LACTOSE_AND_OTHER_SOLIDS,
            ),
      },

      [ROF_FIELDS.DEDUCTIONS]: {
        [ROF_FIELDS.PRICE_PER_KG]: toolMilkProductionData?.[
          ROF_FIELDS.DEDUCTIONS
        ]?.[ROF_FIELDS.PRICE_PER_KG]
          ? convertNumberToString(
              toolMilkProductionData?.[ROF_FIELDS.DEDUCTIONS][
                ROF_FIELDS.PRICE_PER_KG
              ],
            )
          : previousVisitMilkProductionData?.[ROF_FIELDS.DEDUCTIONS]?.[
              ROF_FIELDS.PRICE_PER_KG
            ]
          ? convertNumberToString(
              previousVisitMilkProductionData?.[ROF_FIELDS.DEDUCTIONS][
                ROF_FIELDS.PRICE_PER_KG
              ],
            )
          : getMilkPricePerTonValueByType(rofPriceList, ROF_FIELDS.DEDUCTIONS),
      },
    };
  } catch (error) {
    console.log('getMilkProductionInitialFormValues fail', error);
    logEvent('getMilkProductionInitialFormValues fail', error);
  }
};

/**
 * @description
 * helper function to map milk production output fields in milk production object.
 *
 * @param {Object} toolData
 * @returns
 */
export const getMilkProductionOutputsInitialFormValues = (
  values = null,
  unit,
  formType = ROF_FORM_TYPES.TMR,
  setFieldValue,
  conversionNeeded = false,
) => {
  try {
    //#region fetch params
    //KG/HL
    let proteinKgHl =
      convertStringToNumber(
        values[ROF_FIELDS.MILK_PRODUCTION]?.[ROF_FIELDS.PROTEIN]?.[
          ROF_FIELDS.PRICE_PER_HL
        ],
      ) || 0;
    let lactoseAndOtherSolidsKgHl = convertStringToNumber(
      values[ROF_FIELDS.MILK_PRODUCTION]?.[
        ROF_FIELDS.LACTOSE_AND_OTHER_SOLIDS
      ]?.[ROF_FIELDS.PRICE_PER_HL],
    );
    let butterfatKgHl = convertStringToNumber(
      values[ROF_FIELDS.MILK_PRODUCTION]?.[ROF_FIELDS.BUTTERFAT]?.[
        ROF_FIELDS.PRICE_PER_HL
      ],
    );

    //kg/cow
    let butterfatKgCow = convertStringToNumber(
      values[ROF_FIELDS.MILK_PRODUCTION]?.[ROF_FIELDS.BUTTERFAT]?.[
        ROF_FIELDS.PRICE_PER_KG_PER_COW
      ],
    );
    let proteinKgCow = convertStringToNumber(
      values[ROF_FIELDS.MILK_PRODUCTION]?.[ROF_FIELDS.PROTEIN]?.[
        ROF_FIELDS.PRICE_PER_KG_PER_COW
      ],
    );
    let lactoseAndOtherSolidsKgCow = convertStringToNumber(
      values[ROF_FIELDS.MILK_PRODUCTION]?.[
        ROF_FIELDS.LACTOSE_AND_OTHER_SOLIDS
      ]?.[ROF_FIELDS.PRICE_PER_KG_PER_COW],
    );

    // price/kg
    let butterfatPricePerKg = convertStringToNumber(
      values[ROF_FIELDS.MILK_PRODUCTION]?.[ROF_FIELDS.BUTTERFAT]?.[
        ROF_FIELDS.PRICE_PER_KG
      ],
    );
    let deductionsPricePerKg = convertStringToNumber(
      values[ROF_FIELDS.MILK_PRODUCTION]?.[ROF_FIELDS.DEDUCTIONS]?.[
        ROF_FIELDS.PRICE_PER_KG
      ],
    );
    let proteinPricePerKg = convertStringToNumber(
      values[ROF_FIELDS.MILK_PRODUCTION]?.[ROF_FIELDS.PROTEIN]?.[
        ROF_FIELDS.PRICE_PER_KG
      ],
    );
    let class2ProteinPricePerKg = convertStringToNumber(
      values[ROF_FIELDS.MILK_PRODUCTION]?.[ROF_FIELDS.CLASS2_PROTEIN]?.[
        ROF_FIELDS.PRICE_PER_KG
      ],
    );
    let lactoseAndOtherSolidsPricePerKg = convertStringToNumber(
      values[ROF_FIELDS.MILK_PRODUCTION]?.[
        ROF_FIELDS.LACTOSE_AND_OTHER_SOLIDS
      ]?.[ROF_FIELDS.PRICE_PER_KG],
    );
    let class2LactoseAndOtherSolidsPricePerKg = convertStringToNumber(
      values[ROF_FIELDS.MILK_PRODUCTION]?.[
        ROF_FIELDS.CLASS2_LACTOSE_AND_OTHER_SOLIDS
      ]?.[ROF_FIELDS.PRICE_PER_KG],
    );

    //milk production
    let milkProductionKg = convertStringToNumber(
      values[ROF_FIELDS.MILK_PRODUCTION]?.[ROF_FIELDS.MILK_PRODUCTION_KG],
    );
    //lactating cows
    let lactatingCows = convertStringToNumber(
      values[ROF_FIELDS.FEEDING]?.[ROF_FIELDS.LACTATING_COWS],
    );

    let forages = parseFeedingIngredientsForDB(
        values[ROF_FIELDS.FEEDING]?.[ROF_FIELDS.HOME_GROWN_FORAGES],
      ),
      grains = parseFeedingIngredientsForDB(
        values[ROF_FIELDS.FEEDING]?.[ROF_FIELDS.HOME_GROWN_GRAINS],
      ),
      bulkFeed = parseFeedingIngredientsForDB(
        values[ROF_FIELDS.FEEDING]?.[ROF_FIELDS.PURCHASE_BULK_FEED],
      ),
      bagsFeed = parseFeedingIngredientsForDB(
        values[ROF_FIELDS.FEEDING]?.[ROF_FIELDS.PURCHASE_BAG_FEED],
      );

    //current quota
    let currentQuotaUtilization = convertStringToNumber(
      values[ROF_FIELDS.MILK_PRODUCTION]?.[
        ROF_FIELDS.CURRENT_QUOTA_UTILIZATION_KG_PER_DAY
      ],
    );
    //totalQuota
    let totalQuota = convertStringToNumber(
      values[ROF_FIELDS.MILK_PRODUCTION]?.[ROF_FIELDS.TOTAL_QUOTA_KG_PER_DAY],
    );

    //DB calculation values
    let totalFeedCostKgDMPerDay =
      convertStringToNumber(
        calculateTotalFeedCostKgDMPerDay(
          forages,
          grains,
          bulkFeed,
          bagsFeed,
          lactatingCows,
        ).toFixed(ROF_DECIMAL_PLACES),
        true,
      ) || 0;
    let totalFeedCostPerCowPerDay =
      convertStringToNumber(
        calculateTotalFeedCostPerCowPerDay(
          forages,
          grains,
          bulkFeed,
          bagsFeed,
          lactatingCows,
          formType,
        ).toFixed(ROF_DECIMAL_PLACES),
        true,
      ) || 0;
    let totalPurchasedCostPerCowPerDay =
      convertStringToNumber(
        calculateTotalPurchasedCostPerCowPerDay(
          bulkFeed,
          bagsFeed,
          lactatingCows,
          formType,
        ).toFixed(ROF_DECIMAL_PLACES),
        true,
      ) || 0;
    let totalConcentrateCostPerCowPerDay =
      convertStringToNumber(
        calculateTotalConcentrateCostPerCowPerDay(
          grains,
          bulkFeed,
          bagsFeed,
          lactatingCows,
          formType,
        ).toFixed(ROF_DECIMAL_PLACES),
        true,
      ) || 0;

    // some field values calculated here as we need it multiple times in diff formulas
    let rationOrButterFat = calculateRationOrButterFat(
      proteinKgHl,
      lactoseAndOtherSolidsKgHl,
      butterfatKgHl,
      conversionNeeded,
    );

    let maxAllowed = convertStringToNumber(
      values[ROF_FIELDS.MILK_PRODUCTION_OUTPUTS][ROF_FIELDS.MAX_ALLOWED],
    );

    //totalRevenuePerCowPerDay not sending conversion needed as we need this as number for now
    let totalRevenuePerCowPerDay = calculateTotalRevenuePerCowPerDay(
      milkProductionKg,
      butterfatKgHl,
      butterfatPricePerKg,
      proteinPricePerKg,
      proteinKgHl,
      class2ProteinPricePerKg,
      lactoseAndOtherSolidsKgHl,
      lactoseAndOtherSolidsPricePerKg,
      deductionsPricePerKg,
      convertStringToNumber(rationOrButterFat),
      maxAllowed,
      proteinKgCow,
      lactoseAndOtherSolidsKgCow,
      class2LactoseAndOtherSolidsPricePerKg,
      butterfatKgCow,
    );

    let totalRevenuePerLiter = calculateTotalRevenuePerLiter(
      milkProductionKg,
      totalRevenuePerCowPerDay,
      conversionNeeded,
    );
    //#endregion

    //output obj
    let milkProductionOutputs = {
      [ROF_FIELDS.RATIO_SNF_PER_BUTTERFAT]: rationOrButterFat,
      [ROF_FIELDS.MAX_ALLOWED]: conversionNeeded
        ? convertNumberToString(maxAllowed, !conversionNeeded) || ''
        : maxAllowed || 0,
      [ROF_FIELDS.TOTAL_FAT_PROTEIN]: calculateTotalFatProtein(
        proteinKgCow,
        butterfatKgCow,
        conversionNeeded,
      ),
      [ROF_FIELDS.DAIRY_EFFICIENCY]: calculateDairyEfficiency(
        milkProductionKg,
        butterfatKgHl,
        proteinKgHl,
        totalFeedCostKgDMPerDay,
        conversionNeeded,
      ),
      [ROF_FIELDS.COMPONENT_EFFICIENCY]: calculateComponentEfficiency(
        proteinKgCow,
        butterfatKgCow,
        totalFeedCostKgDMPerDay,
        conversionNeeded,
      ),
      [ROF_FIELDS.TOTAL_REVENUE_PER_LITER]: totalRevenuePerLiter,
      [ROF_FIELDS.FEED_COST]: calculateFeedCostPerLiter(
        totalFeedCostPerCowPerDay,
        milkProductionKg,
        conversionNeeded,
      ),
      [ROF_FIELDS.PURCHASED_FEED_COST]: calculatePurchasedFeedCostPerLiter(
        totalPurchasedCostPerCowPerDay,
        milkProductionKg,
        conversionNeeded,
      ),
      [ROF_FIELDS.CONCENTRATE_COST]: calculateConcentrateCostPerLiter(
        totalConcentrateCostPerCowPerDay,
        milkProductionKg,
        conversionNeeded,
      ),
      [ROF_FIELDS.CONCENTRATE_COST_PER_KG_BF]:
        calculateConcentrateCostPerKgButterFat(
          totalConcentrateCostPerCowPerDay,
          butterfatKgCow,
          conversionNeeded,
        ),
      [ROF_FIELDS.BF_REVENUE]: calculateButterFatRevenue(
        milkProductionKg,
        butterfatKgHl,
        butterfatPricePerKg,
        conversionNeeded,
      ),
      [ROF_FIELDS.PROTEIN_REVENUE]: calculateProteinRevenue(
        milkProductionKg,
        proteinPricePerKg,
        proteinKgHl,
        class2ProteinPricePerKg,
        butterfatKgHl,
        lactoseAndOtherSolidsKgHl,
        conversionNeeded,
      ),
      [ROF_FIELDS.OTHER_SOLIDS_REVENUE]: calculateOtherSolidsRevenue(
        milkProductionKg,
        lactoseAndOtherSolidsPricePerKg,
        lactoseAndOtherSolidsKgHl,
        class2LactoseAndOtherSolidsPricePerKg,
        butterfatKgHl,
        proteinKgHl,
        conversionNeeded,
      ),
      [ROF_FIELDS.DEDUCTIONS_PRICE_PER_COW_PER_DAY]:
        calculateDeductionsPerCowPerDay(
          milkProductionKg,
          deductionsPricePerKg,
          conversionNeeded,
        ),
      [ROF_FIELDS.SNF_NON_PAYMENT]: calculateSNFNonPayments(
        convertStringToNumber(rationOrButterFat),
        maxAllowed,
        proteinKgCow,
        lactoseAndOtherSolidsKgCow,
        class2ProteinPricePerKg,
        class2LactoseAndOtherSolidsPricePerKg,
        butterfatKgCow,
        conversionNeeded,
      ),
      [ROF_FIELDS.TOTAL_REVENUE_KG_FAT]: calculateTotalRevenuePerKgFat(
        totalRevenuePerCowPerDay,
        butterfatKgCow,
        conversionNeeded,
      ),
      //handling it diff as its calculated above to be used in diff calculations
      [ROF_FIELDS.TOTAL_REVENUE_COW_DAY]: conversionNeeded
        ? convertNumberToString(totalRevenuePerCowPerDay, !conversionNeeded)
        : totalRevenuePerCowPerDay,
      [ROF_FIELDS.UNDER_QUOTA_LOST_REVENUE]:
        calculateUnderQuotaLostRevenuePerMonth(
          currentQuotaUtilization,
          totalQuota,
          convertStringToNumber(totalRevenuePerLiter),
          butterfatKgHl,
          conversionNeeded,
        ),
      [ROF_FIELDS.ROF_KG_BUTTER_FAT]: calculateROFPerKgButterFat(
        totalRevenuePerCowPerDay,
        butterfatKgCow,
        totalFeedCostPerCowPerDay,
        conversionNeeded,
      ),
      [ROF_FIELDS.ROF]: calculateMilkProdOutputROF(
        totalRevenuePerCowPerDay,
        totalFeedCostPerCowPerDay,
        conversionNeeded,
      ),
    };

    if (setFieldValue) {
      //if needed at form level
      setFieldValue(ROF_FIELDS.MILK_PRODUCTION_OUTPUTS, milkProductionOutputs);
    } else {
      // else needed at save to db or get data level
      return milkProductionOutputs;
    }
  } catch (error) {
    console.log('getMilkProductionOutputsInitialFormValues fail', error);
    logEvent('getMilkProductionOutputsInitialFormValues fail', error);
  }
};

const getSummaryPreviousVisitData = (toolData = {}, unit, prevVisit = null) => {
  try {
    let toolSummary =
      toolData?.[ROF_FIELDS.SUMMARY]?.[
        ROF_FIELDS.PREVIOUS_RETURN_OVER_FEED_COSTS
      ] || {};

    let prevVisitSummary =
      prevVisit?.[ROF_FIELDS.SUMMARY]?.[
        ROF_FIELDS.CURRENT_RETURN_OVER_FEED_COSTS
      ] || {};

    return {
      [ROF_FIELDS.PREVIOUS_RETURN_OVER_FEED_COSTS]: {
        [ROF_FIELDS.RETURN_OVER_FEED_COST_PER_COW_PER_DAY]:
          convertNumberToString(
            toolSummary?.[ROF_FIELDS.RETURN_OVER_FEED_COST_PER_COW_PER_DAY],
          ) ||
          convertNumberToString(
            prevVisitSummary?.[
              ROF_FIELDS.RETURN_OVER_FEED_COST_PER_COW_PER_DAY
            ],
          ) ||
          '0',
        [ROF_FIELDS.RETURN_OVER_FEED_COST_PER_KG_OF_BF]:
          convertNumberToString(
            toolSummary?.[ROF_FIELDS.RETURN_OVER_FEED_COST_PER_KG_OF_BF],
          ) ||
          convertNumberToString(
            prevVisitSummary?.[ROF_FIELDS.RETURN_OVER_FEED_COST_PER_KG_OF_BF],
          ) ||
          '0',
        [ROF_FIELDS.RETURN_OVER_FEED_COST_PER_LITRE]:
          convertNumberToString(
            toolSummary?.[ROF_FIELDS.RETURN_OVER_FEED_COST_PER_LITRE],
          ) ||
          convertNumberToString(
            prevVisitSummary?.[ROF_FIELDS.RETURN_OVER_FEED_COST_PER_LITRE],
          ) ||
          '0',
      },
    };
  } catch (e) {
    console.log('getSummaryPreviousVisitData fail', e);
    logEvent('getSummaryPreviousVisitData fail', e);
  }
};
//#endregion

//#region handle feeding
export const setIngredientFieldsByTypeInArray = (
  formType,
  setFieldValue,
  values,
  type,
  field,
  value,
  index,
  rofPriceList = [],
  conversionNeeded = false,
  enumState,
) => {
  try {
    let ingredientArray = values[ROF_FIELDS.FEEDING][type];
    //add specific field value is added to array item on index
    if (field) {
      if (
        field === ROF_FIELDS.FORAGE_NAME ||
        field === ROF_FIELDS.GRAINS_NAME ||
        field === ROF_FIELDS.FEED_NAME
      ) {
        //make names unique in same visit for same type else dont let user enter the value
        let isSimilarName = false;
        ingredientArray?.forEach(element => {
          if (element[field] == value) {
            isSimilarName = true;
            return;
          }
        });
        if (isSimilarName) {
          return;
        }
      }
      //set current field value
      ingredientArray[index][field] = value;

      if (
        field === ROF_FIELDS.HOME_GROWN_FORAGE_TYPE ||
        field === ROF_FIELDS.HOME_GROWN_GRAINS_TYPE
      ) {
        //on changing feeding type change price per ton as per API
        let pricePerTon = rofPriceList?.filter(p => {
          return p.name == ingredientArray[index][field];
        })[0]?.price;

        ingredientArray[index][ROF_FIELDS.PRICE_PER_TON] = pricePerTon;
      }

      if (
        field === ROF_FIELDS.TOTAL_HERD_PER_DAY ||
        field === ROF_FIELDS.DRY_MATTER
      ) {
        //save totalDryMatter as per formula on changing of these fields
        let totalDryMatter = 0;

        let totalHerdPerDay =
          ingredientArray[index][ROF_FIELDS.TOTAL_HERD_PER_DAY];
        let dryMatter = ingredientArray[index][ROF_FIELDS.DRY_MATTER];

        if (conversionNeeded) {
          totalHerdPerDay = convertStringToNumber(totalHerdPerDay);
          dryMatter = convertStringToNumber(dryMatter);
        }

        if (formType == ROF_FORM_TYPES.TMR) {
          totalDryMatter = (totalHerdPerDay * dryMatter) / 100;
        } else {
          // ((totalHerdPerDay * dryMatter) / 100) * lactating animals;
          let lactatingCows =
            values[ROF_FIELDS.FEEDING][ROF_FIELDS.LACTATING_COWS];

          totalDryMatter =
            ((totalHerdPerDay * dryMatter) / 100) * lactatingCows;
        }
        totalDryMatter = parseFloat(
          totalDryMatter.toFixed(ROF_ONE_DECIMAL_PLACES),
        );

        ingredientArray[index][ROF_FIELDS.TOTAL_DRY_MATTER] = conversionNeeded
          ? convertNumberToString(totalDryMatter)
          : totalDryMatter;
      }
    } else {
      //to delete the index
      if (index != null) {
        //if these are the types we need to keep one index atleast
        if (
          (type == ROF_FIELDS.HOME_GROWN_FORAGES ||
            type == ROF_FIELDS.HOME_GROWN_GRAINS) &&
          ingredientArray.length == 1
        ) {
          return;
        } else {
          ingredientArray.splice(index, 1);
        }
      } else {
        //add new item to array
        ingredientArray.push(
          getFeedingIngredientsFormValues(type, enumState, {}, rofPriceList),
        );
      }
    }
    //save whole obj in array as a single value
    setFieldValue(values[ROF_FIELDS.FEEDING][type], ingredientArray);
  } catch (e) {
    console.log('handleFormFieldInTypeArray fail', e);
    logEvent('handleFormFieldInTypeArray fail', e);
  }
};

export const recalculateTotalDryMatterInFeeding = (
  values,
  lactatingCows = 0,
  setFieldValue,
) => {
  try {
    let feedingValues = values[ROF_FIELDS.FEEDING];

    Object.values(ROF_FEEDING_INGREDIENTS_TYPES).map(type => {
      let ingredientArray = feedingValues[type];

      ingredientArray.length > 0 &&
        ingredientArray?.map(ingredient => {
          let totalDryMatter = 0;

          let totalHerdPerDay = ingredient[ROF_FIELDS.TOTAL_HERD_PER_DAY];
          let dryMatter = ingredient[ROF_FIELDS.DRY_MATTER];

          totalHerdPerDay = convertStringToNumber(totalHerdPerDay);
          dryMatter = convertStringToNumber(dryMatter);

          // ((totalHerdPerDay * dryMatter) / 100) * lactating animals;
          totalDryMatter =
            ((totalHerdPerDay * dryMatter) / 100) * lactatingCows;

          totalDryMatter = parseFloat(
            totalDryMatter.toFixed(ROF_ONE_DECIMAL_PLACES),
          );

          ingredient[ROF_FIELDS.TOTAL_DRY_MATTER] =
            convertNumberToString(totalDryMatter);
        });

      setFieldValue(values[ROF_FIELDS.FEEDING][type], ingredientArray);
    });
  } catch (e) {
    console.log('recalculateTotalDryMatterInFeeding fail', e);
    logEvent('recalculateTotalDryMatterInFeeding fail', e);
  }
};
//#endregion

//#region handle milk production calculations

export const calculateAverageMilkProductionPerCowPerDay = (
  avgMilkProductionKg = 0,
  lactatingCows = 0,
  conversionNeeded = false,
) => {
  try {
    if (conversionNeeded) {
      avgMilkProductionKg = convertStringToNumber(avgMilkProductionKg);
      lactatingCows = convertStringToNumber(lactatingCows);
    }

    let averageMilkProductionLitresPerCowPerDay = lactatingCows
      ? parseFloat(
          (avgMilkProductionKg / lactatingCows).toFixed(ROF_DECIMAL_PLACES),
        )
      : 0;
    return conversionNeeded
      ? convertNumberToString(averageMilkProductionLitresPerCowPerDay)
      : averageMilkProductionLitresPerCowPerDay;
  } catch (error) {
    console.log('calculateAverageMilkProductionPerCowPerDay fail', error);
    logEvent('calculateAverageMilkProductionPerCowPerDay fail', error);
  }
};

export const calculateCurrentQuotaUtilizationKgPerDay = (
  averageMilkProductionKg = 0,
  butterFatPricePerHl = 0,
  conversionNeeded = false,
) => {
  try {
    // ((Average Milk Production - Animals in Tank * Butterfat kg/hl )/100)
    if (conversionNeeded) {
      averageMilkProductionKg = convertStringToNumber(averageMilkProductionKg);
      butterFatPricePerHl = convertStringToNumber(butterFatPricePerHl);
    }
    if (averageMilkProductionKg && butterFatPricePerHl) {
      let currentQuotaUtilizationKgPerDay = parseFloat(
        ((averageMilkProductionKg * butterFatPricePerHl) / 100).toFixed(
          ROF_DECIMAL_PLACES,
        ),
      );
      return conversionNeeded
        ? convertNumberToString(currentQuotaUtilizationKgPerDay)
        : currentQuotaUtilizationKgPerDay;
    } else {
      return conversionNeeded ? convertNumberToString(0.0) : 0;
    }
  } catch (e) {
    console.log('calculateCurrentQuotaUtilizationKgPerDay fail', e);
    logEvent('calculateCurrentQuotaUtilizationKgPerDay fail', e);
  }
};

export const calculatePriceKgPerCow = (
  milkProduction = 0,
  pricePerHl = 0,
  conversionNeeded = false,
) => {
  try {
    // (Milk Production * kg/hl)/100
    if (conversionNeeded) {
      milkProduction = convertStringToNumber(milkProduction);
      pricePerHl = convertStringToNumber(pricePerHl);
    }
    if (milkProduction && pricePerHl) {
      let pricePerKgPerCow = parseFloat(
        ((milkProduction * pricePerHl) / 100).toFixed(ROF_DECIMAL_PLACES),
      );
      return conversionNeeded
        ? convertNumberToString(pricePerKgPerCow)
        : pricePerKgPerCow;
    } else {
      return conversionNeeded ? convertNumberToString(0.0) : 0;
    }
  } catch (e) {
    console.log('calculatePriceKgPerCow fail', e);
    logEvent('calculatePriceKgPerCow fail', e);
  }
};

export const handlePriceKgPerCowInAllMilkingIngredients = (
  setFieldValue,
  milkProductionKg,
  toolMilkProductionData,
) => {
  try {
    //set this value in all milking fields and set value
    Object.values(ROF_MILKING_INGREDIENTS_TYPES).map(milkingType => {
      let pricePerKgPerCow = calculatePriceKgPerCow(
        milkProductionKg,
        toolMilkProductionData?.[milkingType][ROF_FIELDS.PRICE_PER_HL],
        true,
      );
      setFieldValue(
        `${ROF_FIELDS.MILK_PRODUCTION}.${milkingType}.${ROF_FIELDS.PRICE_PER_KG_PER_COW}`,
        pricePerKgPerCow,
      );
    });
  } catch (e) {
    console.log('handlePriceKgPerCowInAllMilkingIngredients fail', e);
    logEvent('handlePriceKgPerCowInAllMilkingIngredients fail', e);
  }
};

//#endregion

//#region handle milk production outputs calculations
export const handleMaxAllowedChange = (values, value, setFieldValue) => {
  try {
    //handle SNF nonpayments on this change

    let rationOrButterFat = convertStringToNumber(
      values[ROF_FIELDS.MILK_PRODUCTION_OUTPUTS][
        ROF_FIELDS.RATIO_SNF_PER_BUTTERFAT
      ],
    );
    let butterfatKgCow = convertStringToNumber(
      values[ROF_FIELDS.MILK_PRODUCTION]?.[ROF_FIELDS.BUTTERFAT]?.[
        ROF_FIELDS.PRICE_PER_KG_PER_COW
      ],
    );
    let proteinKgCow = convertStringToNumber(
      values[ROF_FIELDS.MILK_PRODUCTION]?.[ROF_FIELDS.PROTEIN]?.[
        ROF_FIELDS.PRICE_PER_KG_PER_COW
      ],
    );
    let lactoseAndOtherSolidsKgCow = convertStringToNumber(
      values[ROF_FIELDS.MILK_PRODUCTION]?.[
        ROF_FIELDS.LACTOSE_AND_OTHER_SOLIDS
      ]?.[ROF_FIELDS.PRICE_PER_KG_PER_COW],
    );

    let class2ProteinPricePerKg = convertStringToNumber(
      values[ROF_FIELDS.MILK_PRODUCTION]?.[ROF_FIELDS.CLASS2_PROTEIN]?.[
        ROF_FIELDS.PRICE_PER_KG
      ],
    );
    let class2LactoseAndOtherSolidsPricePerKg = convertStringToNumber(
      values[ROF_FIELDS.MILK_PRODUCTION]?.[
        ROF_FIELDS.CLASS2_LACTOSE_AND_OTHER_SOLIDS
      ]?.[ROF_FIELDS.PRICE_PER_KG],
    );
    let maxAllowed = convertStringToNumber(value);

    let snfNonPayments = calculateSNFNonPayments(
      rationOrButterFat,
      maxAllowed,
      proteinKgCow,
      lactoseAndOtherSolidsKgCow,
      class2ProteinPricePerKg,
      class2LactoseAndOtherSolidsPricePerKg,
      butterfatKgCow,
      true,
    );

    setFieldValue(
      `${ROF_FIELDS.MILK_PRODUCTION_OUTPUTS}.${ROF_FIELDS.SNF_NON_PAYMENT}`,
      snfNonPayments,
    );
  } catch (e) {
    console.log('handleMaxAllowedChange fail', e);
    logEvent('handleMaxAllowedChange fail', e);
  }
};
export const calculateRationOrButterFat = (
  proteinKgHl = 0,
  lactoseAndOtherSolidsKgHl = 0,
  butterfatKgHl = 0,
  keepString = false,
) => {
  try {
    // (protein kg/hl + lactose and other solids kg/hl) / butterfat kg/hl
    let rationOrButterFat = butterfatKgHl
      ? (proteinKgHl + lactoseAndOtherSolidsKgHl) / butterfatKgHl
      : 0;
    rationOrButterFat = parseFloat(
      rationOrButterFat.toFixed(ROF_DECIMAL_PLACES),
    );
    return !keepString
      ? rationOrButterFat
      : convertNumberToString(rationOrButterFat);
  } catch (e) {
    console.log('getRationOrButterFat fail', e);
    logEvent('getRationOrButterFat fail', e);
  }
};
export const calculateTotalFatProtein = (
  proteinKgCow = 0,
  butterfatKgCow = 0,
  keepString = false,
) => {
  try {
    // Butterfat kg/cow + protein kg/cow
    let totalFatProtein = butterfatKgCow + proteinKgCow;
    totalFatProtein = parseFloat(totalFatProtein.toFixed(ROF_DECIMAL_PLACES));
    return !keepString
      ? totalFatProtein
      : convertNumberToString(totalFatProtein);
  } catch (e) {
    console.log('getTotalFatProtein fail', e);
    logEvent('getTotalFatProtein fail', e);
  }
};
export const calculateDairyEfficiency = (
  milkProductionKg = 0,
  butterfatKgHl = 0,
  proteinKgHl = 0,
  totalFeedCostKgDMPerDay = 0, //db calculations
  keepString = false,
) => {
  try {
    // ((milk production *1.03*0.2594)+(12.1975*(butterfat kg/hl /100*(milk production *1.03)))+(7.707*(protein kg/hl /100*(milk production *1.03))))/total feed cost kg DM per day
    let dairyEfficiency = totalFeedCostKgDMPerDay
      ? (milkProductionKg * 1.03 * 0.2594 +
          12.1975 * ((butterfatKgHl / 100) * (milkProductionKg * 1.03)) +
          7.707 * ((proteinKgHl / 100) * (milkProductionKg * 1.03))) /
        totalFeedCostKgDMPerDay
      : 0;
    dairyEfficiency = parseFloat(dairyEfficiency.toFixed(ROF_DECIMAL_PLACES));
    return !keepString
      ? dairyEfficiency
      : convertNumberToString(dairyEfficiency);
  } catch (e) {
    console.log('getDairyEfficiency fail', e);
    logEvent('getDairyEfficiency fail', e);
  }
};
export const calculateComponentEfficiency = (
  proteinKgCow = 0,
  butterfatKgCow = 0,
  totalFeedCostKgDMPerDay = 0, //DB calculations
  keepString = false,
) => {
  try {
    // ((butterfat kg/cow + protein kg/cow))/total feed cost kg DM per day *100
    let componentEfficiency = totalFeedCostKgDMPerDay
      ? ((butterfatKgCow + proteinKgCow) / totalFeedCostKgDMPerDay) * 100
      : 0;
    componentEfficiency = parseFloat(
      componentEfficiency.toFixed(ROF_DECIMAL_PLACES),
    );
    return !keepString
      ? componentEfficiency
      : convertNumberToString(componentEfficiency);
  } catch (e) {
    console.log('getComponentEfficiency fail', e);
    logEvent('getComponentEfficiency fail', e);
  }
};
export const calculateFeedCostPerLiter = (
  totalFeedCostPerCowPerDay = 0, //DB calculations
  milkProductionKg = 0,
  keepString = false,
) => {
  try {
    // total feed cost cost per cow per day (from the backend/milk production
    let feedCostPerLiterMilk = milkProductionKg
      ? totalFeedCostPerCowPerDay / milkProductionKg
      : 0;

    feedCostPerLiterMilk = parseFloat(
      feedCostPerLiterMilk.toFixed(ROF_DECIMAL_PLACES),
    );
    return !keepString
      ? feedCostPerLiterMilk
      : convertNumberToString(feedCostPerLiterMilk);
  } catch (e) {
    console.log('getFeedCost fail', e);
    logEvent('getFeedCost fail', e);
  }
};
export const calculatePurchasedFeedCostPerLiter = (
  totalPurchasedCostPerCowPerDay = 0, //DB calculations
  milkProductionKg = 0,
  keepString = false,
) => {
  try {
    // Total Purchased Cost for cost/cow/day from DB Calculations / milk production from milk production tab
    let purchasedFeedCostPerLiter = milkProductionKg
      ? totalPurchasedCostPerCowPerDay / milkProductionKg
      : 0;
    purchasedFeedCostPerLiter = parseFloat(
      purchasedFeedCostPerLiter.toFixed(ROF_DECIMAL_PLACES),
    );
    return !keepString
      ? purchasedFeedCostPerLiter
      : convertNumberToString(purchasedFeedCostPerLiter);
  } catch (e) {
    console.log('getPurchasedFeedCost fail', e);
    logEvent('getPurchasedFeedCost fail', e);
  }
};
export const calculateConcentrateCostPerLiter = (
  totalConcentrateCostPerCowPerDay = 0, //DB calculations
  milkProductionKg = 0,
  keepString = false,
) => {
  try {
    // total concentrate cost of cost/cow/day from db calculations / milk production from milk production tab
    let concentrateCostPerLiter = milkProductionKg
      ? totalConcentrateCostPerCowPerDay / milkProductionKg
      : 0;
    concentrateCostPerLiter = parseFloat(
      concentrateCostPerLiter.toFixed(ROF_DECIMAL_PLACES),
    );
    return !keepString
      ? concentrateCostPerLiter
      : convertNumberToString(concentrateCostPerLiter);
  } catch (e) {
    console.log('getConcentrateCost fail', e);
    logEvent('getConcentrateCost fail', e);
  }
};
export const calculateConcentrateCostPerKgButterFat = (
  totalConcentrateCostPerCowPerDay = 0, //DB calculations
  butterfatKgCow = 0,
  keepString = false,
) => {
  try {
    // total concentrate cost of cost/cow/day from db calculations  / Price (kg/cow) of Butterfat
    let concentrateCostPerButterFat = butterfatKgCow
      ? totalConcentrateCostPerCowPerDay / butterfatKgCow
      : 0;
    concentrateCostPerButterFat = parseFloat(
      concentrateCostPerButterFat.toFixed(ROF_DECIMAL_PLACES),
    );
    return !keepString
      ? concentrateCostPerButterFat
      : convertNumberToString(concentrateCostPerButterFat);
  } catch (e) {
    console.log('getConcentrateCostPerButterFat fail', e);
    logEvent('getConcentrateCostPerButterFat fail', e);
  }
};
export const calculateButterFatRevenue = (
  milkProductionKg = 0,
  butterfatKgHl = 0,
  butterfatPricePerKg = 0,
  keepString = false,
) => {
  try {
    // milk production from milk production tab * (Price (kg/hl of Butterfat/100) * Price (currency/kg of Butterfat)
    let butterFatRevenue =
      milkProductionKg * (butterfatKgHl / 100) * butterfatPricePerKg;
    butterFatRevenue = parseFloat(butterFatRevenue.toFixed(ROF_DECIMAL_PLACES));
    return !keepString
      ? butterFatRevenue
      : convertNumberToString(butterFatRevenue);
  } catch (e) {
    console.log('getButterFatRevenue fail', e);
    logEvent('getButterFatRevenue fail', e);
  }
};

const getProteines = (
  proteinKgHl = 0,
  butterfatKgHl = 0,
  lactoseAndOtherSolidsKgHl = 0,
) => {
  try {
    // IF ((Butterfat kg/hl*2)*(Protein kg/hl/(Protein kg/hl + Lactose and other solids kg/hl))) is greater than Protein kg/hl, Return Protein kg/hl
    // Else (Butterfat kg/hl*2)*(Protein kg/hl / (Protein kg/hl+Lactose and other solids kg/hl))

    let condition1 =
      proteinKgHl + lactoseAndOtherSolidsKgHl
        ? butterfatKgHl *
          2 *
          (proteinKgHl / (proteinKgHl + lactoseAndOtherSolidsKgHl))
        : 0;

    if (condition1 > proteinKgHl) {
      return parseFloat(proteinKgHl.toFixed(ROF_DECIMAL_PLACES));
    } else {
      return parseFloat(condition1.toFixed(ROF_DECIMAL_PLACES));
    }
  } catch (e) {
    console.log('getProteines fail', e);
    logEvent('getProteines fail', e);
  }
};
export const calculateProteinRevenue = (
  milkProductionKg = 0,
  proteinPricePerKg = 0,
  proteinKgHl = 0,
  class2ProteinPricePerKg = 0,
  butterfatKgHl = 0,
  lactoseAndOtherSolidsKgHl = 0,
  keepString = false,
) => {
  try {
    // (milk production from milk production tab*(protéines/100) * Price (currency/kg of Protein)+(milk production from milk production tab*(protein kg/hl - protéines/100) * Price (currency/kg of Class 2 Protein)
    //where protéines = is from above function as per condition

    let proteines = getProteines(
      proteinKgHl,
      butterfatKgHl,
      lactoseAndOtherSolidsKgHl,
    );

    let proteinRevenue =
      milkProductionKg * (proteines / 100) * proteinPricePerKg +
      milkProductionKg *
        ((proteinKgHl - proteines) / 100) *
        class2ProteinPricePerKg;

    proteinRevenue = parseFloat(proteinRevenue.toFixed(ROF_DECIMAL_PLACES));
    return !keepString ? proteinRevenue : convertNumberToString(proteinRevenue);
  } catch (e) {
    console.log('getProteinRevenue fail', e);
    logEvent('getProteinRevenue fail', e);
  }
};

const getLactoses = (
  butterfatKgHl = 0,
  lactoseAndOtherSolidsKgHl = 0,
  proteinKgHl = 0,
) => {
  try {
    // IF((Butterfat kg/hl*2)*(Lactose and other solids kg/hl /(Protein kg/hl+Lactose and other solids kg/hl))>Lactose and other solids kg/hl, Return Lactose and other solids kg/hl
    // ELSE (Butterfat kg/hl*2)*(Lactose and other solids kg/hl/(Protein kg/hl+D76)))

    let condition1 =
      proteinKgHl + lactoseAndOtherSolidsKgHl
        ? butterfatKgHl *
          2 *
          (lactoseAndOtherSolidsKgHl /
            (proteinKgHl + lactoseAndOtherSolidsKgHl))
        : 0;

    if (condition1 > lactoseAndOtherSolidsKgHl) {
      return parseFloat(lactoseAndOtherSolidsKgHl.toFixed(ROF_DECIMAL_PLACES));
    } else {
      return parseFloat(condition1.toFixed(ROF_DECIMAL_PLACES));
    }
  } catch (e) {
    console.log('getLactoses fail', e);
    logEvent('getLactoses fail', e);
  }
};
export const calculateOtherSolidsRevenue = (
  milkProductionKg = 0,
  lactoseAndOtherSolidsPricePerKg = 0,
  lactoseAndOtherSolidsKgHl = 0,
  class2LactoseAndOtherSolidsPricePerKg = 0,
  butterfatKgHl = 0,
  proteinKgHl = 0,
  keepString = false,
) => {
  try {
    // (milk production from milk production tab*(lactoses/100) * Price (currency/kg of Lactose and other solids)+(milk production from milk production tab*(Lactose & other solids kg/hl - lactoses/100)* Price (currency/kg of Class 2 Lactose and other solids)
    //where Lactoses = is from above function as per condition

    let lactoses = getLactoses(
      butterfatKgHl,
      lactoseAndOtherSolidsKgHl,
      proteinKgHl,
    );
    let otherSolidsRevenue =
      milkProductionKg * (lactoses / 100) * lactoseAndOtherSolidsPricePerKg +
      milkProductionKg *
        ((lactoseAndOtherSolidsKgHl - lactoses) / 100) *
        class2LactoseAndOtherSolidsPricePerKg;

    otherSolidsRevenue = parseFloat(
      otherSolidsRevenue.toFixed(ROF_DECIMAL_PLACES),
    );
    return !keepString
      ? otherSolidsRevenue
      : convertNumberToString(otherSolidsRevenue);
  } catch (e) {
    console.log('getOtherSolidsRevenue fail', e);
    logEvent('getOtherSolidsRevenue fail', e);
  }
};
export const calculateDeductionsPerCowPerDay = (
  milkProductionKg = 0,
  deductionsPricePerKg = 0,
  keepString = false,
) => {
  try {
    // milk production from milk production tab * Price (currency/kg of Deductions
    let deductionsPricePerCowPerDay = milkProductionKg * deductionsPricePerKg;
    deductionsPricePerCowPerDay = parseFloat(
      deductionsPricePerCowPerDay.toFixed(ROF_DECIMAL_PLACES),
    );
    return !keepString
      ? deductionsPricePerCowPerDay
      : convertNumberToString(deductionsPricePerCowPerDay);
  } catch (e) {
    console.log('getDeductionsPerCowPerDay fail', e);
    logEvent('getDeductionsPerCowPerDay fail', e);
  }
};
export const calculateSNFNonPayments = (
  rationOrButterFat = 0,
  maxAllowed = 0,
  proteinKgCow = 0,
  lactoseAndOtherSolidsKgCow = 0,
  class2ProteinPricePerKg = 0,
  class2LactoseAndOtherSolidsPricePerKg = 0,
  butterfatKgCow = 0,
  keepString = false,
) => {
  try {
    // If:(Ratio S.N.F./Butterfat > Max Allowed ----> Use the following formula:
    // Protein Nonpayment + Other Solids Nonpayment
    // Else: Return 0

    if (rationOrButterFat > maxAllowed) {
      // Protein Nonpayment = (Protein kg/cow / (Protein kg/cow+Other solids kg/cow))*SNF Over*Class 2 protein $/kg
      // Other Solids Nonpayment = (Other solids kg/cow / (Other solids kg/cow+Protein kg/cow))*SNF Over*Class 2 lactose and other solids $/kg
      // SNF Over = (Other solids kg/cow+Protein kg/cow)-(Butterfat kg/cow*Max Allowed)
      let SNFOver =
        lactoseAndOtherSolidsKgCow + proteinKgCow - butterfatKgCow * maxAllowed;

      let proteinNonpayment =
        (proteinKgCow / (lactoseAndOtherSolidsKgCow + proteinKgCow)) *
        SNFOver *
        class2ProteinPricePerKg;

      let otherSolidsNonpayment =
        (lactoseAndOtherSolidsKgCow /
          (lactoseAndOtherSolidsKgCow + proteinKgCow)) *
        SNFOver *
        class2LactoseAndOtherSolidsPricePerKg;

      let snfNonPayments =
        lactoseAndOtherSolidsKgCow + proteinKgCow
          ? proteinNonpayment + otherSolidsNonpayment
          : 0;

      snfNonPayments = parseFloat(snfNonPayments.toFixed(ROF_DECIMAL_PLACES));
      return !keepString
        ? snfNonPayments
        : convertNumberToString(snfNonPayments);
    }
    return !keepString ? 0 : convertNumberToString(0.0);
  } catch (e) {
    console.log('getSNFNonPayments fail', e);
    logEvent('getSNFNonPayments fail', e);
  }
};
export const calculateTotalRevenuePerCowPerDay = (
  milkProductionKg = 0,
  butterfatKgHl = 0,
  butterfatPricePerKg = 0,
  proteinPricePerKg = 0,
  proteinKgHl = 0,
  class2ProteinPricePerKg = 0,
  lactoseAndOtherSolidsKgHl = 0,
  lactoseAndOtherSolidsPricePerKg = 0,
  deductionsPricePerKg = 0,
  rationOrButterFat = 0,
  maxAllowed = 0,
  proteinKgCow = 0,
  lactoseAndOtherSolidsKgCow = 0,
  class2LactoseAndOtherSolidsPricePerKg = 0,
  butterfatKgCow = 0,
) => {
  try {
    // BF Revenue + Protein Revenue + Other Solids Revenue - Deductions - SNF nonpayment

    let butterFatRevenue = calculateButterFatRevenue(
      milkProductionKg,
      butterfatKgHl,
      butterfatPricePerKg,
    );
    let proteinRevenue = calculateProteinRevenue(
      milkProductionKg,
      proteinPricePerKg,
      proteinKgHl,
      class2ProteinPricePerKg,
      butterfatKgHl,
      lactoseAndOtherSolidsKgHl,
    );
    let otherSolidsRevenue = calculateOtherSolidsRevenue(
      milkProductionKg,
      lactoseAndOtherSolidsPricePerKg,
      lactoseAndOtherSolidsKgHl,
      class2ProteinPricePerKg,
      butterfatKgHl,
      proteinKgHl,
    );
    let deductions = calculateDeductionsPerCowPerDay(
      milkProductionKg,
      deductionsPricePerKg,
    );
    let snfNonPayments = calculateSNFNonPayments(
      rationOrButterFat,
      maxAllowed,
      proteinKgCow,
      lactoseAndOtherSolidsKgCow,
      class2ProteinPricePerKg,
      class2LactoseAndOtherSolidsPricePerKg,
      butterfatKgCow,
    );
    let totalRevenuePerCowPerDay =
      butterFatRevenue +
      proteinRevenue +
      otherSolidsRevenue -
      deductions -
      snfNonPayments;
    //not handling conversion here as its being used in other fields. will convert when needed on field level
    return (
      parseFloat(totalRevenuePerCowPerDay.toFixed(ROF_DECIMAL_PLACES)) || 0
    );
  } catch (e) {
    console.log('getTotalRevenuePerCowPerDay fail', e);
    logEvent('getTotalRevenuePerCowPerDay fail', e);
  }
};
export const calculateTotalRevenuePerKgFat = (
  totalRevenuePerCowPerDay = 0,
  butterfatKgCow = 0,
  keepString = false,
) => {
  try {
    // Total Revenue ($/cow/day) / Price (kg/cow) of Butterfat

    let totalRevenuePricePerKgFat = butterfatKgCow
      ? totalRevenuePerCowPerDay / butterfatKgCow
      : 0;
    totalRevenuePricePerKgFat = parseFloat(
      totalRevenuePricePerKgFat.toFixed(ROF_DECIMAL_PLACES),
    );
    return !keepString
      ? totalRevenuePricePerKgFat
      : convertNumberToString(totalRevenuePricePerKgFat);
  } catch (e) {
    console.log('getTotalRevenuePerKgFat fail', e);
    logEvent('getTotalRevenuePerKgFat fail', e);
  }
};
export const calculateTotalRevenuePerLiter = (
  milkProductionKg = 0,
  totalRevenuePerCowPerDay = 0,
  keepString = false,
) => {
  try {
    // (Total Revenue ($/cow/d)/milk production)

    let totalRevenuePerLiter = milkProductionKg
      ? totalRevenuePerCowPerDay / milkProductionKg
      : 0;

    totalRevenuePerLiter = parseFloat(
      totalRevenuePerLiter.toFixed(ROF_DECIMAL_PLACES),
    );
    return !keepString
      ? totalRevenuePerLiter
      : convertNumberToString(totalRevenuePerLiter);
  } catch (e) {
    console.log('getTotalRevenue fail', e);
    logEvent('getTotalRevenue fail', e);
  }
};
export const calculateUnderQuotaLostRevenuePerMonth = (
  currentQuotaUtilization = 0,
  totalQuota = 0,
  totalRevenuePerLiter = 0,
  butterfatKgHl = 0,
  keepString = false,
) => {
  try {
    // If: Current quota utilization > Total quota, RETURN 0
    // Else:-(Total quota-Current quota utilization)*((Total Revenue per Litre*100/Butterfat kg/hl)*30.4))

    if (currentQuotaUtilization > totalQuota) {
      return !keepString ? 0.0 : convertNumberToString(0.0);
    } else {
      let underQuotaLostRevenuePerMonth = butterfatKgHl
        ? -(totalQuota - currentQuotaUtilization) *
          (((totalRevenuePerLiter * 100) / butterfatKgHl) * 30.4)
        : 0;

      underQuotaLostRevenuePerMonth = parseFloat(
        underQuotaLostRevenuePerMonth.toFixed(ROF_DECIMAL_PLACES),
      );

      return !keepString
        ? underQuotaLostRevenuePerMonth
        : convertNumberToString(underQuotaLostRevenuePerMonth);
    }
  } catch (e) {
    console.log('getLostRevenuePerMonth fail', e);
    logEvent('getLostRevenuePerMonth fail', e);
  }
};
export const calculateROFPerKgButterFat = (
  totalRevenuePerCowPerDay = 0,
  butterfatKgCow = 0,
  totalFeedCostPerCowPerDay = 0, //DB calculations
  keepString = false,
) => {
  try {
    // (Total Revenue ($/cow/day) / Price (kg/cow) of Butterfat) - (Total feed cost of cost/cow/day/Price (kg/cow) of Butterfat)

    let rofPerKgButterFat = butterfatKgCow
      ? totalRevenuePerCowPerDay / butterfatKgCow -
        totalFeedCostPerCowPerDay / butterfatKgCow
      : 0;

    rofPerKgButterFat = parseFloat(
      rofPerKgButterFat.toFixed(ROF_DECIMAL_PLACES),
    );
    return !keepString
      ? rofPerKgButterFat
      : convertNumberToString(rofPerKgButterFat);
  } catch (e) {
    console.log('getROFPerKgButterFat fail', e);
    logEvent('getROFPerKgButterFat fail', e);
  }
};
export const calculateMilkProdOutputROF = (
  totalRevenuePerCowPerDay = 0,
  totalFeedCostPerCowPerDay = 0, //DB calculations
  keepString = false,
) => {
  try {
    // Total Revenue ($/cow/day) - Total feed cost of cost/cow/day

    let returnOnFeed = totalRevenuePerCowPerDay - totalFeedCostPerCowPerDay;
    returnOnFeed = parseFloat(returnOnFeed.toFixed(ROF_DECIMAL_PLACES));
    return !keepString ? returnOnFeed : convertNumberToString(returnOnFeed);
  } catch (e) {
    console.log('getMilkProdOutputROF fail', e);
    logEvent('getMilkProdOutputROF fail', e);
  }
};
//#endregion

//#region DB calculations

//#region calculateFeedCostPerDay
const calculateFeedCostPerDay = (
  totalDryMatterOrHerdPerDay = 0,
  pricePerTon = 0,
  lactatingCows = 0,
  formType = ROF_FORM_TYPES.TMR,
  isBulkOrBag = false,
) => {
  if (isBulkOrBag && formType == ROF_FORM_TYPES.INDIVIDUAL_COWS) {
    return calculateFeedCostPerDayIndividualCowsBulkAndBags(
      totalDryMatterOrHerdPerDay,
      pricePerTon,
      lactatingCows,
    );
  } else {
    return totalDryMatterOrHerdPerDay * (pricePerTon / 1000);
  }
};

const calculateFeedCostPerDayIndividualCowsBulkAndBags = (
  totalHerdPerDay = 0,
  pricePerTon = 0,
  lactatingCows = 0,
) => {
  return lactatingCows * totalHerdPerDay * (pricePerTon / 1000);
};

const calculateTotalCost = (
  items = [],
  isBulkOrBag = false,
  lactatingCows = 0,
  formType = ROF_FORM_TYPES.TMR,
) => {
  return items.reduce((total, item) => {
    return (
      total +
      calculateFeedCostPerDay(
        isBulkOrBag ? item?.totalHerdPerDay : item?.totalDryMatter,
        item?.pricePerTon,
        lactatingCows,
        formType,
        isBulkOrBag,
      )
    );
  }, 0);
};

const calculateTotalPurchasedCostPerDay = (
  bulkFeed = [],
  bagsFeed = [],
  lactatingCows = 0,
  formType = ROF_FORM_TYPES.TMR,
) => {
  return (
    calculateTotalCost(bulkFeed, true, lactatingCows, formType) +
    calculateTotalCost(bagsFeed, true, lactatingCows, formType)
  );
};

const calculateTotalConcentrateCostPerDay = (
  grains = [],
  bulkFeed = [],
  bagsFeed = [],
  lactatingCows = 0,
  formType = ROF_FORM_TYPES.TMR,
) => {
  return (
    calculateTotalCost(grains) +
    calculateTotalCost(bulkFeed, true, lactatingCows, formType) +
    calculateTotalCost(bagsFeed, true, lactatingCows, formType)
  );
};

const calculateTotalFeedCostPerDay = (
  forages = [],
  grains = [],
  bulkFeed = [],
  bagsFeed = [],
  lactatingCows = 0,
  formType = ROF_FORM_TYPES.TMR,
) => {
  return (
    calculateTotalCost(forages) +
    calculateTotalCost(grains) +
    calculateTotalPurchasedCostPerDay(
      bulkFeed,
      bagsFeed,
      lactatingCows,
      formType,
    )
  );
};
//#endregion

//#region calculateCostPerCowPerDay
const calculateCostPerCowPerDay = (feedCost = 0, lactatingCows = 0) => {
  return lactatingCows ? feedCost / lactatingCows : 0;
};

const calculateTotalCostPerCowPerDay = (
  items = [],
  lactatingCows = 0,
  isBulkOrBag = false,
  formType = ROF_FORM_TYPES.TMR,
) => {
  return items.reduce((total, item) => {
    return (
      total +
      calculateCostPerCowPerDay(
        calculateFeedCostPerDay(
          isBulkOrBag ? item?.totalHerdPerDay : item?.totalDryMatter,
          item?.pricePerTon,
          lactatingCows,
          formType,
          isBulkOrBag,
        ),
        lactatingCows,
      )
    );
  }, 0);
};

const calculateTotalPurchasedCostPerCowPerDay = (
  bulkFeed = [],
  bagsFeed = [],
  lactatingCows = 0,
  formType = ROF_FORM_TYPES.TMR,
) => {
  return (
    convertStringToNumber(
      calculateTotalCostPerCowPerDay(
        bulkFeed,
        lactatingCows,
        true,
        formType,
      ).toFixed(ROF_DECIMAL_PLACES),
      true,
    ) +
    convertStringToNumber(
      calculateTotalCostPerCowPerDay(
        bagsFeed,
        lactatingCows,
        true,
        formType,
      ).toFixed(ROF_DECIMAL_PLACES),
      true,
    )
  );
};

const calculateTotalConcentrateCostPerCowPerDay = (
  grains = [],
  bulkFeed = [],
  bagsFeed = [],
  lactatingCows = 0,
  formType = ROF_FORM_TYPES.TMR,
) => {
  return (
    convertStringToNumber(
      calculateTotalCostPerCowPerDay(
        grains,
        lactatingCows,
        false,
        formType,
      ).toFixed(ROF_DECIMAL_PLACES),
      true,
    ) +
    convertStringToNumber(
      calculateTotalCostPerCowPerDay(
        bulkFeed,
        lactatingCows,
        true,
        formType,
      ).toFixed(ROF_DECIMAL_PLACES),
      true,
    ) +
    convertStringToNumber(
      calculateTotalCostPerCowPerDay(
        bagsFeed,
        lactatingCows,
        true,
        formType,
      ).toFixed(ROF_DECIMAL_PLACES),
      true,
    )
  );
};

const calculateTotalFeedCostPerCowPerDay = (
  forages = [],
  grains = [],
  bulkFeed = [],
  bagsFeed = [],
  lactatingCows = 0,
  formType = ROF_FORM_TYPES.TMR,
) => {
  return (
    convertStringToNumber(
      calculateTotalCostPerCowPerDay(
        forages,
        lactatingCows,
        false,
        formType,
      ).toFixed(ROF_DECIMAL_PLACES),
      true,
    ) +
    convertStringToNumber(
      calculateTotalCostPerCowPerDay(
        grains,
        lactatingCows,
        false,
        formType,
      ).toFixed(ROF_DECIMAL_PLACES),
      true,
    ) +
    convertStringToNumber(
      calculateTotalPurchasedCostPerCowPerDay(
        bulkFeed,
        bagsFeed,
        lactatingCows,
        formType,
      ).toFixed(ROF_DECIMAL_PLACES),
      true,
    )
  );
};
//#endregion

//#region calculateKgDMPerDay
const calculateKgDMPerDay = (totalDryMatter = 0, lactatingCows = 0) => {
  return lactatingCows ? totalDryMatter / lactatingCows : 0;
};

const calculateTotalCostKgDMPerDay = (items = [], lactatingCows = 0) => {
  return items.reduce((total, item) => {
    return total + calculateKgDMPerDay(item?.totalDryMatter, lactatingCows);
  }, 0);
};

const calculateTotalPurchasedCostKgDMPerDay = (
  bulkFeed = [],
  bagsFeed = [],
  lactatingCows = 0,
) => {
  return (
    convertStringToNumber(
      calculateTotalCostKgDMPerDay(bulkFeed, lactatingCows).toFixed(
        ROF_DECIMAL_PLACES,
      ),
      true,
    ) +
    convertStringToNumber(
      calculateTotalCostKgDMPerDay(bagsFeed, lactatingCows).toFixed(
        ROF_DECIMAL_PLACES,
      ),
      true,
    )
  );
};

const calculateTotalConcentrateCostKgDMPerDay = (
  grains = [],
  bulkFeed = [],
  bagsFeed = [],
  lactatingCows = 0,
) => {
  return (
    calculateTotalCostKgDMPerDay(grains, lactatingCows) +
    calculateTotalCostKgDMPerDay(bulkFeed, lactatingCows) +
    calculateTotalCostKgDMPerDay(bagsFeed, lactatingCows)
  );
};

const calculateTotalFeedCostKgDMPerDay = (
  forages = [],
  grains = [],
  bulkFeed = [],
  bagsFeed = [],
  lactatingCows = 0,
) => {
  return (
    convertStringToNumber(
      calculateTotalCostKgDMPerDay(forages, lactatingCows).toFixed(
        ROF_DECIMAL_PLACES,
      ),
      true,
    ) +
    convertStringToNumber(
      calculateTotalCostKgDMPerDay(grains, lactatingCows).toFixed(
        ROF_DECIMAL_PLACES,
      ),
      true,
    ) +
    convertStringToNumber(
      calculateTotalPurchasedCostKgDMPerDay(
        bulkFeed,
        bagsFeed,
        lactatingCows,
      ).toFixed(ROF_DECIMAL_PLACES),
      true,
    )
  );
};

//#endregion

//#region forage percentage
const calculatePercentForage = (
  forages = [],
  grains = [],
  bulkFeed = [],
  bagsFeed = [],
) => {
  const forageDM = forages.reduce((sum, item) => sum + item.totalDryMatter, 0);
  const totalDM =
    forageDM +
    grains.reduce((sum, item) => sum + item.totalDryMatter, 0) +
    bulkFeed.reduce((sum, item) => sum + item.totalDryMatter, 0) +
    bagsFeed.reduce((sum, item) => sum + item.totalDryMatter, 0);

  return totalDM ? (forageDM / totalDM) * 100 : 0;
};
//#endregion

export const getCalculatedOutputsForTool = (
  values,
  formType = ROF_FORM_TYPES.TMR,
) => {
  try {
    let forages = values[ROF_FIELDS.FEEDING]?.[ROF_FIELDS.HOME_GROWN_FORAGES],
      grains = values[ROF_FIELDS.FEEDING]?.[ROF_FIELDS.HOME_GROWN_GRAINS],
      purchasedBulk =
        values[ROF_FIELDS.FEEDING]?.[ROF_FIELDS.PURCHASE_BULK_FEED],
      purchasedBags =
        values[ROF_FIELDS.FEEDING]?.[ROF_FIELDS.PURCHASE_BAG_FEED],
      lactatingCows = values[ROF_FIELDS.FEEDING]?.[ROF_FIELDS.LACTATING_COWS];

    let calculatedOutputs = {
      [ROF_FIELDS.FORAGE_FEED_COST_PER_DAY]: calculateTotalCost(forages),
      [ROF_FIELDS.GRAINS_FEED_COST_PER_DAY]: calculateTotalCost(grains),
      [ROF_FIELDS.PURCHASED_BULK_FEED_COST_PER_DAY]: calculateTotalCost(
        purchasedBulk,
        true,
        lactatingCows,
        formType,
      ),
      [ROF_FIELDS.PURCHASED_BAGS_FEED_COST_PER_DAY]: calculateTotalCost(
        purchasedBags,
        true,
        lactatingCows,
        formType,
      ),
      [ROF_FIELDS.TOTAL_PURCHASED_COST_PER_DAY]:
        calculateTotalPurchasedCostPerDay(
          purchasedBulk,
          purchasedBags,
          lactatingCows,
          formType,
        ),
      [ROF_FIELDS.TOTAL_CONCENTRATE_COST_PER_DAY]:
        calculateTotalConcentrateCostPerDay(
          grains,
          purchasedBulk,
          purchasedBags,
          lactatingCows,
          formType,
        ),
      [ROF_FIELDS.TOTAL_FEED_COST_PER_DAY]: calculateTotalFeedCostPerDay(
        forages,
        grains,
        purchasedBulk,
        purchasedBags,
        lactatingCows,
        formType,
      ),
      //FEED_COST_PER_COW_PER_DAY
      [ROF_FIELDS.FORAGE_FEED_COST_PER_COW_PER_DAY]:
        calculateTotalCostPerCowPerDay(forages, lactatingCows, false, formType),
      [ROF_FIELDS.GRAINS_COST_PER_COW_PER_DAY]: calculateTotalCostPerCowPerDay(
        grains,
        lactatingCows,
        false,
        formType,
      ),
      [ROF_FIELDS.PURCHASED_BULK_FEED_PER_COW_PER_DAY]:
        calculateTotalCostPerCowPerDay(
          purchasedBulk,
          lactatingCows,
          true,
          formType,
        ),
      [ROF_FIELDS.PURCHASED_BAGS_FEED_PER_COW_PER_DAY]:
        calculateTotalCostPerCowPerDay(
          purchasedBags,
          lactatingCows,
          true,
          formType,
        ),
      [ROF_FIELDS.TOTAL_PURCHASED_COST_PER_COW_PER_DAY]:
        calculateTotalPurchasedCostPerCowPerDay(
          purchasedBulk,
          purchasedBags,
          lactatingCows,
          formType,
        ),
      [ROF_FIELDS.TOTAL_CONCENTRATE_COST_PER_COW_PER_DAY]:
        calculateTotalConcentrateCostPerCowPerDay(
          grains,
          purchasedBulk,
          purchasedBags,
          lactatingCows,
          formType,
        ),
      [ROF_FIELDS.TOTAL_FEED_COST_PER_COW_PER_DAY]:
        calculateTotalFeedCostPerCowPerDay(
          forages,
          grains,
          purchasedBulk,
          purchasedBags,
          lactatingCows,
          formType,
        ),
      //KG_DM_PER_DAY
      [ROF_FIELDS.FORAGE_KG_DM_PER_DAY]: calculateTotalCostKgDMPerDay(
        forages,
        lactatingCows,
      ),
      [ROF_FIELDS.GRAINS_KG_DM_PER_DAY]: calculateTotalCostKgDMPerDay(
        grains,
        lactatingCows,
      ),
      [ROF_FIELDS.PURCHASED_BULK_KG_DM_PER_DAY]: calculateTotalCostKgDMPerDay(
        purchasedBulk,
        lactatingCows,
      ),
      [ROF_FIELDS.PURCHASED_BAGS_KG_DM_PER_DAY]: calculateTotalCostKgDMPerDay(
        purchasedBags,
        lactatingCows,
      ),
      [ROF_FIELDS.TOTAL_PURCHASED_COST_KG_DM_PER_DAY]:
        calculateTotalPurchasedCostKgDMPerDay(
          purchasedBulk,
          purchasedBags,
          lactatingCows,
        ),
      [ROF_FIELDS.TOTAL_CONCENTRATE_COST_KG_DM_PER_DAY]:
        calculateTotalConcentrateCostKgDMPerDay(
          grains,
          purchasedBulk,
          purchasedBags,
          lactatingCows,
        ),
      [ROF_FIELDS.TOTAL_FEED_COST_KG_DM_PER_DAY]:
        calculateTotalFeedCostKgDMPerDay(
          forages,
          grains,
          purchasedBulk,
          purchasedBags,
          lactatingCows,
        ),
      //FORAGE_PERCENTAGE
      [ROF_FIELDS.FORAGE_PERCENTAGE]: calculatePercentForage(
        forages,
        grains,
        purchasedBulk,
        purchasedBags,
      ),
    };
    return calculatedOutputs;
  } catch (error) {
    console.log('getCalculatedOutputsForTool fail', error);
    logEvent('getCalculatedOutputsForTool fail', error);
  }
};
//#endregion

//#region handle summary calculations

const calculateNoOfCowsToFillQuota = (totalQuota = 0, butterfatKgCow = 0) => {
  try {
    return butterfatKgCow
      ? parseFloat((totalQuota / butterfatKgCow).toFixed(ROF_DECIMAL_PLACES))
      : 0;
  } catch (error) {
    console.log('calculateNoOfCowsToFillQuota fail', error);
    logEvent('calculateNoOfCowsToFillQuota fail', error);
  }
};

const calculateTotalRevenuePerKgButterFat = (
  totalRevenuePerCowPerDay = 0,
  butterfatKgCow = 0,
) => {
  try {
    return butterfatKgCow
      ? parseFloat(
          (totalRevenuePerCowPerDay / butterfatKgCow).toFixed(
            ROF_DECIMAL_PLACES,
          ),
        )
      : 0;
  } catch (error) {
    console.log('calculateTotalRevenuePerKgButterFat fail', error);
    logEvent('calculateTotalRevenuePerKgButterFat fail', error);
  }
};

const calculateFeedCostPerKgOfBF = (
  totalFeedCostPerCowPerDay = 0,
  butterfatKgCow = 0,
) => {
  try {
    return butterfatKgCow
      ? parseFloat(
          (totalFeedCostPerCowPerDay / butterfatKgCow).toFixed(
            ROF_DECIMAL_PLACES,
          ),
        )
      : 0;
  } catch (error) {
    console.log('calculateFeedCostPerKgOfBF fail', error);
    logEvent('calculateFeedCostPerKgOfBF fail', error);
  }
};

const calculateFeedCostPerLiterMilk = (
  totalFeedCostPerCowPerDay = 0,
  averageMilkProductionLitresPerCowPerDay = 0,
) => {
  try {
    return averageMilkProductionLitresPerCowPerDay
      ? parseFloat(
          (
            totalFeedCostPerCowPerDay / averageMilkProductionLitresPerCowPerDay
          ).toFixed(ROF_DECIMAL_PLACES),
        )
      : 0;
  } catch (error) {
    console.log('calculateFeedCostPerLiterMilk fail', error);
    logEvent('calculateFeedCostPerLiterMilk fail', error);
  }
};

const calculateROFPerCowPerDay = (
  totalRevenuePerCowPerDay = 0,
  totalFeedCostPerCowPerDay = 0,
) => {
  try {
    return totalRevenuePerCowPerDay - totalFeedCostPerCowPerDay || 0;
  } catch (error) {
    console.log('calculateROFPerCowPerDay fail', error);
    logEvent('calculateROFPerCowPerDay fail', error);
  }
};

const calculateROFPerLiter = (
  revenuePerLiter = 0,
  feedCostPerLiterMilk = 0,
) => {
  try {
    return revenuePerLiter - feedCostPerLiterMilk || 0;
  } catch (error) {
    console.log('calculateROFPerLiter fail', error);
    logEvent('calculateROFPerLiter fail', error);
  }
};

export const getROFSummaryResultValues = (values, calculatedOutputs) => {
  try {
    let toolHerdProfileData = values[ROF_FIELDS.HERD_PROFILE] || {};
    let toolFeedingData = values[ROF_FIELDS.FEEDING] || {};
    let toolMilkProductionData = values[ROF_FIELDS.MILK_PRODUCTION] || {};
    let toolMilkProductionOutputsData =
      values[ROF_FIELDS.MILK_PRODUCTION_OUTPUTS] || {};
    let previousROFVisitSummaryData =
      values[ROF_FIELDS.SUMMARY]?.[
        ROF_FIELDS.PREVIOUS_RETURN_OVER_FEED_COSTS
      ] || {};

    let averageMilkProductionLitresPerCowPerDay =
      calculateAverageMilkProductionPerCowPerDay(
        toolMilkProductionData?.[ROF_FIELDS.AVERAGE_MILK_PRODUCTION_KG],
        toolFeedingData[ROF_FIELDS.LACTATING_COWS],
      );

    let feedCostPerLiterMilk = calculateFeedCostPerLiterMilk(
      calculatedOutputs[ROF_FIELDS.TOTAL_FEED_COST_PER_COW_PER_DAY],
      averageMilkProductionLitresPerCowPerDay,
    );
    let feedCostPerKgOfBF = calculateFeedCostPerKgOfBF(
      calculatedOutputs[ROF_FIELDS.TOTAL_FEED_COST_PER_COW_PER_DAY],
      toolMilkProductionData?.[ROF_FIELDS.BUTTERFAT][
        ROF_FIELDS.PRICE_PER_KG_PER_COW
      ],
    );

    return {
      [ROF_FIELDS.HERD_BASELINE]: {
        [ROF_FIELDS.LACTATING_COWS]: toolFeedingData[ROF_FIELDS.LACTATING_COWS],
        [ROF_FIELDS.DAYS_IN_MILK]: toolFeedingData[ROF_FIELDS.DAYS_IN_MILK],
        [ROF_FIELDS.MUN]: toolHerdProfileData[ROF_FIELDS.MUN] || 0,
      },
      [ROF_FIELDS.QUOTA]: {
        [ROF_FIELDS.KG_OF_QUOTA]:
          toolMilkProductionData?.[ROF_FIELDS.KG_OF_QUOTA_PER_DAY] || 0,
        [ROF_FIELDS.INCENTIVE_DAYS]:
          toolMilkProductionData?.[ROF_FIELDS.INCENTIVE_DAYS_KG_PER_DAY] || 0,
        [ROF_FIELDS.TOTAL_QUOTA]:
          toolMilkProductionData?.[ROF_FIELDS.TOTAL_QUOTA_KG_PER_DAY] || 0,
        [ROF_FIELDS.NO_OF_COWS_TO_FILL_QUOTA]: calculateNoOfCowsToFillQuota(
          toolMilkProductionData?.[ROF_FIELDS.TOTAL_QUOTA_KG_PER_DAY],
          toolMilkProductionData?.[ROF_FIELDS.BUTTERFAT][
            ROF_FIELDS.PRICE_PER_KG_PER_COW
          ],
        ),
        [ROF_FIELDS.NO_OF_COWS_TO_FILL_50_KG]:
          toolMilkProductionOutputsData[ROF_FIELDS.TOTAL_FAT_PROTEIN] || 0,
        [ROF_FIELDS.RATIO_SNF_PER_BUTTERFAT]:
          toolMilkProductionOutputsData[ROF_FIELDS.RATIO_SNF_PER_BUTTERFAT] ||
          0,
        [ROF_FIELDS.MAX_ALLOWED]:
          toolMilkProductionOutputsData[ROF_FIELDS.MAX_ALLOWED] || 0,
      },
      [ROF_FIELDS.MILK_PRODUCTION]: {
        [ROF_FIELDS.AVERAGE_MILK_PRODUCTION_ANIMALS_IN_TANK]:
          toolMilkProductionData?.[ROF_FIELDS.AVERAGE_MILK_PRODUCTION_KG] || 0,
        [ROF_FIELDS.AVERAGE_MILK_PRODUCTION_LITRES_COW_DAY]:
          averageMilkProductionLitresPerCowPerDay,
        [ROF_FIELDS.DAIRY_EFFICIENCY]:
          toolMilkProductionOutputsData[ROF_FIELDS.DAIRY_EFFICIENCY] || 0,
        [ROF_FIELDS.BUTTERFAT]: {
          [ROF_FIELDS.PRICE_PER_KG]:
            toolMilkProductionData?.[ROF_FIELDS.BUTTERFAT][
              ROF_FIELDS.PRICE_PER_KG
            ],
          [ROF_FIELDS.PRICE_PER_KG_PER_COW]:
            toolMilkProductionData?.[ROF_FIELDS.BUTTERFAT][
              ROF_FIELDS.PRICE_PER_KG_PER_COW
            ],
        },
        [ROF_FIELDS.PROTEIN]: {
          [ROF_FIELDS.PRICE_PER_KG]:
            toolMilkProductionData?.[ROF_FIELDS.PROTEIN][
              ROF_FIELDS.PRICE_PER_KG
            ],
          [ROF_FIELDS.PRICE_PER_KG_PER_COW]:
            toolMilkProductionData?.[ROF_FIELDS.PROTEIN][
              ROF_FIELDS.PRICE_PER_KG_PER_COW
            ],
        },
        [ROF_FIELDS.LACTOSE_AND_OTHER_SOLIDS]: {
          [ROF_FIELDS.PRICE_PER_KG]:
            toolMilkProductionData?.[ROF_FIELDS.LACTOSE_AND_OTHER_SOLIDS][
              ROF_FIELDS.PRICE_PER_KG
            ],
          [ROF_FIELDS.PRICE_PER_KG_PER_COW]:
            toolMilkProductionData?.[ROF_FIELDS.LACTOSE_AND_OTHER_SOLIDS][
              ROF_FIELDS.PRICE_PER_KG_PER_COW
            ],
        },
      },
      [ROF_FIELDS.REVENUE]: {
        [ROF_FIELDS.BF_REVENUE]:
          toolMilkProductionOutputsData[ROF_FIELDS.BF_REVENUE] || 0,
        [ROF_FIELDS.PROTEIN_REVENUE]:
          toolMilkProductionOutputsData[ROF_FIELDS.PROTEIN_REVENUE] || 0,
        [ROF_FIELDS.OTHER_SOLIDS_REVENUE]:
          toolMilkProductionOutputsData[ROF_FIELDS.OTHER_SOLIDS_REVENUE] || 0,
        [ROF_FIELDS.SUBTOTAL]:
          toolMilkProductionOutputsData[ROF_FIELDS.BF_REVENUE] +
          toolMilkProductionOutputsData[ROF_FIELDS.PROTEIN_REVENUE] +
          toolMilkProductionOutputsData[ROF_FIELDS.OTHER_SOLIDS_REVENUE],
        [ROF_FIELDS.DEDUCTIONS_PRICE_PER_COW_PER_DAY]:
          toolMilkProductionOutputsData[
            ROF_FIELDS.DEDUCTIONS_PRICE_PER_COW_PER_DAY
          ] || 0,
        [ROF_FIELDS.SNF_NON_PAYMENT]:
          toolMilkProductionOutputsData[ROF_FIELDS.SNF_NON_PAYMENT] || 0,
        [ROF_FIELDS.TOTAL_REVENUE_COW_DAY]:
          toolMilkProductionOutputsData[ROF_FIELDS.TOTAL_REVENUE_COW_DAY] || 0,
        [ROF_FIELDS.TOTAL_REVENUE_PER_KG_BUTTERFAT]:
          calculateTotalRevenuePerKgButterFat(
            toolMilkProductionOutputsData[ROF_FIELDS.TOTAL_REVENUE_COW_DAY],
            toolMilkProductionData?.[ROF_FIELDS.BUTTERFAT][
              ROF_FIELDS.PRICE_PER_KG_PER_COW
            ],
          ) || 0,
        [ROF_FIELDS.TOTAL_REVENUE_PER_LITER]:
          toolMilkProductionOutputsData[ROF_FIELDS.TOTAL_REVENUE_PER_LITER] ||
          0,
      },
      [ROF_FIELDS.FEED_COSTS]: {
        [ROF_FIELDS.FORAGE_FEED_COST_PER_COW_PER_DAY]:
          calculatedOutputs[ROF_FIELDS.FORAGE_FEED_COST_PER_COW_PER_DAY] || 0,
        [ROF_FIELDS.GRAINS_COST_PER_COW_PER_DAY]:
          calculatedOutputs[ROF_FIELDS.GRAINS_COST_PER_COW_PER_DAY] || 0,
        [ROF_FIELDS.TOTAL_ON_FARM_FEED_COST_PER_COW_PER_DAY]:
          calculatedOutputs[ROF_FIELDS.FORAGE_FEED_COST_PER_COW_PER_DAY] +
            calculatedOutputs[ROF_FIELDS.GRAINS_COST_PER_COW_PER_DAY] || 0,
        [ROF_FIELDS.PURCHASED_BULK_FEED_PER_COW_PER_DAY]:
          calculatedOutputs[ROF_FIELDS.PURCHASED_BULK_FEED_PER_COW_PER_DAY] ||
          0,
        [ROF_FIELDS.PURCHASED_BAGS_FEED_PER_COW_PER_DAY]:
          calculatedOutputs[ROF_FIELDS.PURCHASED_BAGS_FEED_PER_COW_PER_DAY] ||
          0,
        [ROF_FIELDS.TOTAL_PURCHASED_COST_PER_COW_PER_DAY]:
          calculatedOutputs[ROF_FIELDS.TOTAL_PURCHASED_COST_PER_COW_PER_DAY] ||
          0,
        [ROF_FIELDS.TOTAL_FEED_COST_PER_COW_PER_DAY]:
          calculatedOutputs[ROF_FIELDS.TOTAL_FEED_COST_PER_COW_PER_DAY] || 0,
        [ROF_FIELDS.TOTAL_CONCENTRATE_COST_PER_COW_PER_DAY]:
          calculatedOutputs[
            ROF_FIELDS.TOTAL_CONCENTRATE_COST_PER_COW_PER_DAY
          ] || 0,
        [ROF_FIELDS.FEED_COST_PER_KG_OF_BF]: feedCostPerKgOfBF,
        [ROF_FIELDS.FEED_COST_PER_LITRE_OF_MILK]: feedCostPerLiterMilk,
        [ROF_FIELDS.FORAGE_PERCENTAGE]:
          calculatedOutputs[ROF_FIELDS.FORAGE_PERCENTAGE] || 0,
      },
      [ROF_FIELDS.CURRENT_RETURN_OVER_FEED_COSTS]: {
        [ROF_FIELDS.RETURN_OVER_FEED_COST_PER_COW_PER_DAY]:
          calculateROFPerCowPerDay(
            toolMilkProductionOutputsData[ROF_FIELDS.TOTAL_REVENUE_COW_DAY],
            calculatedOutputs[ROF_FIELDS.TOTAL_FEED_COST_PER_COW_PER_DAY],
          ),
        [ROF_FIELDS.RETURN_OVER_FEED_COST_PER_KG_OF_BF]:
          calculateROFPerKgButterFat(
            toolMilkProductionOutputsData[ROF_FIELDS.TOTAL_REVENUE_COW_DAY],
            toolMilkProductionData?.[ROF_FIELDS.BUTTERFAT][
              ROF_FIELDS.PRICE_PER_KG_PER_COW
            ],
            calculatedOutputs[ROF_FIELDS.TOTAL_FEED_COST_PER_COW_PER_DAY],
          ),
        [ROF_FIELDS.RETURN_OVER_FEED_COST_PER_LITRE]: calculateROFPerLiter(
          toolMilkProductionOutputsData[ROF_FIELDS.TOTAL_REVENUE_PER_LITER],
          feedCostPerLiterMilk,
        ),
      },
      [ROF_FIELDS.PREVIOUS_RETURN_OVER_FEED_COSTS]: {
        [ROF_FIELDS.RETURN_OVER_FEED_COST_PER_COW_PER_DAY]:
          previousROFVisitSummaryData[
            ROF_FIELDS.RETURN_OVER_FEED_COST_PER_COW_PER_DAY
          ],
        [ROF_FIELDS.RETURN_OVER_FEED_COST_PER_KG_OF_BF]:
          previousROFVisitSummaryData[
            ROF_FIELDS.RETURN_OVER_FEED_COST_PER_KG_OF_BF
          ],
        [ROF_FIELDS.RETURN_OVER_FEED_COST_PER_LITRE]:
          previousROFVisitSummaryData[
            ROF_FIELDS.RETURN_OVER_FEED_COST_PER_LITRE
          ],
      },
    };
  } catch (error) {
    console.log('getROFSummaryResultValues fail', error);
    logEvent('getROFSummaryResultValues fail', error);
  }
};

//#endregion

//#region handle ROF DB model
const parseFeedingIngredientsForDB = (items = []) => {
  try {
    let data = [];
    items?.map(item => {
      let obj = {
        ...item,
        [ROF_FIELDS.TOTAL_HERD_PER_DAY]: convertStringToNumber(
          item[ROF_FIELDS.TOTAL_HERD_PER_DAY],
        ),
        [ROF_FIELDS.DRY_MATTER]: convertStringToNumber(
          item[ROF_FIELDS.DRY_MATTER],
        ),
        [ROF_FIELDS.TOTAL_DRY_MATTER]: convertStringToNumber(
          item[ROF_FIELDS.TOTAL_DRY_MATTER],
        ),
        [ROF_FIELDS.PRICE_PER_TON]: convertStringToNumber(
          item[ROF_FIELDS.PRICE_PER_TON],
        ),
      };
      data.push(obj);
    });
    return data;
  } catch (error) {
    console.log('parseFeedingIngredientsForDB fail', error);
    logEvent('parseFeedingIngredientsForDB fail', error);
  }
};

export const getROFToolDataForDB = (
  values,
  unit,
  formType = ROF_FORM_TYPES.TMR,
) => {
  try {
    let toolHerdProfileData = values[ROF_FIELDS.HERD_PROFILE] || {};
    let toolFeedingData = values[ROF_FIELDS.FEEDING] || {};
    let toolMilkProductionData = values[ROF_FIELDS.MILK_PRODUCTION] || {};
    let milkProductionOutputData = getMilkProductionOutputsInitialFormValues(
      values,
      unit,
      formType,
    );
    let toolSummaryDataPreviousROFCost =
      values[ROF_FIELDS.SUMMARY]?.[
        ROF_FIELDS.PREVIOUS_RETURN_OVER_FEED_COSTS
      ] || {};

    let toolData = {
      [ROF_FIELDS.HERD_PROFILE]: {
        [ROF_FIELDS.BREED]: toolHerdProfileData[ROF_FIELDS.BREED] || null,
        [ROF_FIELDS.OTHER_BREED_TYPE]:
          toolHerdProfileData[ROF_FIELDS.OTHER_BREED_TYPE] || null,
        [ROF_FIELDS.FEEDING_TYPE]:
          toolHerdProfileData[ROF_FIELDS.FEEDING_TYPE] || null,
        [ROF_FIELDS.NUMBER_OF_TMR_GROUPS]: toolHerdProfileData[
          ROF_FIELDS.NUMBER_OF_TMR_GROUPS
        ]
          ? convertStringToNumber(
              toolHerdProfileData[ROF_FIELDS.NUMBER_OF_TMR_GROUPS],
            )
          : null,
        [ROF_FIELDS.TYPE_OF_SUPPLEMENT]:
          toolHerdProfileData[ROF_FIELDS.TYPE_OF_SUPPLEMENT] || null,
        [ROF_FIELDS.COOL_AID]:
          toolHerdProfileData[ROF_FIELDS.COOL_AID] || false,
        [ROF_FIELDS.FORTISSA_FIT]:
          toolHerdProfileData[ROF_FIELDS.FORTISSA_FIT] || false,
        [ROF_FIELDS.MUN]: toolHerdProfileData[ROF_FIELDS.MUN]
          ? convertStringToNumber(toolHerdProfileData[ROF_FIELDS.MUN])
          : null,
        [ROF_FIELDS.MILKING_PER_DAY]: toolHerdProfileData[
          ROF_FIELDS.MILKING_PER_DAY
        ]
          ? convertStringToNumber(
              toolHerdProfileData[ROF_FIELDS.MILKING_PER_DAY],
            )
          : null,
      },
      [ROF_FIELDS.FEEDING]: {
        [ROF_FIELDS.LACTATING_COWS]: toolFeedingData[ROF_FIELDS.LACTATING_COWS]
          ? convertStringToNumber(toolFeedingData[ROF_FIELDS.LACTATING_COWS])
          : null,
        [ROF_FIELDS.DAYS_IN_MILK]: toolFeedingData[ROF_FIELDS.DAYS_IN_MILK]
          ? convertStringToNumber(toolFeedingData[ROF_FIELDS.DAYS_IN_MILK])
          : null,
        [ROF_FIELDS.HOME_GROWN_FORAGES]: parseFeedingIngredientsForDB(
          toolFeedingData[ROF_FIELDS.HOME_GROWN_FORAGES],
        ),
        [ROF_FIELDS.HOME_GROWN_GRAINS]: parseFeedingIngredientsForDB(
          toolFeedingData[ROF_FIELDS.HOME_GROWN_GRAINS],
        ),
        [ROF_FIELDS.PURCHASE_BULK_FEED]: parseFeedingIngredientsForDB(
          toolFeedingData[ROF_FIELDS.PURCHASE_BULK_FEED],
        ),
        [ROF_FIELDS.PURCHASE_BAG_FEED]: parseFeedingIngredientsForDB(
          toolFeedingData[ROF_FIELDS.PURCHASE_BAG_FEED],
        ),
      },
      [ROF_FIELDS.MILK_PRODUCTION]: {
        [ROF_FIELDS.AVERAGE_MILK_PRODUCTION_KG]: toolMilkProductionData?.[
          ROF_FIELDS.AVERAGE_MILK_PRODUCTION_KG
        ]
          ? convertStringToNumber(
              toolMilkProductionData?.[ROF_FIELDS.AVERAGE_MILK_PRODUCTION_KG],
            )
          : null,
        [ROF_FIELDS.MILK_PRODUCTION_KG]: toolMilkProductionData?.[
          ROF_FIELDS.MILK_PRODUCTION_KG
        ]
          ? convertStringToNumber(
              toolMilkProductionData?.[ROF_FIELDS.MILK_PRODUCTION_KG],
            )
          : null,
        [ROF_FIELDS.KG_OF_QUOTA_PER_DAY]: toolMilkProductionData?.[
          ROF_FIELDS.KG_OF_QUOTA_PER_DAY
        ]
          ? convertStringToNumber(
              toolMilkProductionData?.[ROF_FIELDS.KG_OF_QUOTA_PER_DAY],
            )
          : null,
        [ROF_FIELDS.INCENTIVE_DAYS_KG_PER_DAY]: toolMilkProductionData?.[
          ROF_FIELDS.INCENTIVE_DAYS_KG_PER_DAY
        ]
          ? convertStringToNumber(
              toolMilkProductionData?.[ROF_FIELDS.INCENTIVE_DAYS_KG_PER_DAY],
            )
          : null,
        [ROF_FIELDS.TOTAL_QUOTA_KG_PER_DAY]: toolMilkProductionData?.[
          ROF_FIELDS.TOTAL_QUOTA_KG_PER_DAY
        ]
          ? convertStringToNumber(
              toolMilkProductionData?.[ROF_FIELDS.TOTAL_QUOTA_KG_PER_DAY],
            )
          : null,
        [ROF_FIELDS.CURRENT_QUOTA_UTILIZATION_KG_PER_DAY]:
          convertStringToNumber(
            calculateCurrentQuotaUtilizationKgPerDay(
              toolMilkProductionData?.[ROF_FIELDS.AVERAGE_MILK_PRODUCTION_KG],
              toolMilkProductionData?.[ROF_FIELDS.BUTTERFAT]?.[
                ROF_FIELDS.PRICE_PER_HL
              ],
              true,
            ),
          ) || null,

        [ROF_FIELDS.BUTTERFAT]: {
          [ROF_FIELDS.PRICE_PER_KG]: toolMilkProductionData?.[
            ROF_FIELDS.BUTTERFAT
          ]?.[ROF_FIELDS.PRICE_PER_KG]
            ? convertStringToNumber(
                toolMilkProductionData?.[ROF_FIELDS.BUTTERFAT][
                  ROF_FIELDS.PRICE_PER_KG
                ],
              )
            : null,
          [ROF_FIELDS.PRICE_PER_HL]: toolMilkProductionData?.[
            ROF_FIELDS.BUTTERFAT
          ]?.[ROF_FIELDS.PRICE_PER_HL]
            ? convertStringToNumber(
                toolMilkProductionData?.[ROF_FIELDS.BUTTERFAT][
                  ROF_FIELDS.PRICE_PER_HL
                ],
              )
            : null,
          [ROF_FIELDS.PRICE_PER_KG_PER_COW]:
            convertStringToNumber(
              calculatePriceKgPerCow(
                toolMilkProductionData?.[ROF_FIELDS.MILK_PRODUCTION_KG],
                toolMilkProductionData?.[ROF_FIELDS.BUTTERFAT]?.[
                  ROF_FIELDS.PRICE_PER_HL
                ],
                true,
              ),
            ) || null,
        },

        [ROF_FIELDS.PROTEIN]: {
          [ROF_FIELDS.PRICE_PER_KG]: toolMilkProductionData?.[
            ROF_FIELDS.PROTEIN
          ]?.[ROF_FIELDS.PRICE_PER_KG]
            ? convertStringToNumber(
                toolMilkProductionData?.[ROF_FIELDS.PROTEIN][
                  ROF_FIELDS.PRICE_PER_KG
                ],
              )
            : null,
          [ROF_FIELDS.PRICE_PER_HL]: toolMilkProductionData?.[
            ROF_FIELDS.PROTEIN
          ]?.[ROF_FIELDS.PRICE_PER_HL]
            ? convertStringToNumber(
                toolMilkProductionData?.[ROF_FIELDS.PROTEIN][
                  ROF_FIELDS.PRICE_PER_HL
                ],
              )
            : null,
          [ROF_FIELDS.PRICE_PER_KG_PER_COW]:
            convertStringToNumber(
              calculatePriceKgPerCow(
                toolMilkProductionData?.[ROF_FIELDS.MILK_PRODUCTION_KG],
                toolMilkProductionData?.[ROF_FIELDS.PROTEIN]?.[
                  ROF_FIELDS.PRICE_PER_HL
                ],
                true,
              ),
            ) || null,
        },

        [ROF_FIELDS.LACTOSE_AND_OTHER_SOLIDS]: {
          [ROF_FIELDS.PRICE_PER_KG]: toolMilkProductionData?.[
            ROF_FIELDS.LACTOSE_AND_OTHER_SOLIDS
          ]?.[ROF_FIELDS.PRICE_PER_KG]
            ? convertStringToNumber(
                toolMilkProductionData?.[ROF_FIELDS.LACTOSE_AND_OTHER_SOLIDS][
                  ROF_FIELDS.PRICE_PER_KG
                ],
              )
            : null,
          [ROF_FIELDS.PRICE_PER_HL]: toolMilkProductionData?.[
            ROF_FIELDS.LACTOSE_AND_OTHER_SOLIDS
          ]?.[ROF_FIELDS.PRICE_PER_HL]
            ? convertStringToNumber(
                toolMilkProductionData?.[ROF_FIELDS.LACTOSE_AND_OTHER_SOLIDS][
                  ROF_FIELDS.PRICE_PER_HL
                ],
              )
            : null,
          [ROF_FIELDS.PRICE_PER_KG_PER_COW]:
            convertStringToNumber(
              calculatePriceKgPerCow(
                toolMilkProductionData?.[ROF_FIELDS.MILK_PRODUCTION_KG],
                toolMilkProductionData?.[ROF_FIELDS.LACTOSE_AND_OTHER_SOLIDS]?.[
                  ROF_FIELDS.PRICE_PER_HL
                ],
                true,
              ),
            ) || null,
        },

        [ROF_FIELDS.CLASS2_PROTEIN]: {
          [ROF_FIELDS.PRICE_PER_KG]: toolMilkProductionData?.[
            ROF_FIELDS.CLASS2_PROTEIN
          ]?.[ROF_FIELDS.PRICE_PER_KG]
            ? convertStringToNumber(
                toolMilkProductionData?.[ROF_FIELDS.CLASS2_PROTEIN][
                  ROF_FIELDS.PRICE_PER_KG
                ],
              )
            : null,
        },

        [ROF_FIELDS.CLASS2_LACTOSE_AND_OTHER_SOLIDS]: {
          [ROF_FIELDS.PRICE_PER_KG]: toolMilkProductionData?.[
            ROF_FIELDS.CLASS2_LACTOSE_AND_OTHER_SOLIDS
          ]?.[ROF_FIELDS.PRICE_PER_KG]
            ? convertStringToNumber(
                toolMilkProductionData?.[
                  ROF_FIELDS.CLASS2_LACTOSE_AND_OTHER_SOLIDS
                ][ROF_FIELDS.PRICE_PER_KG],
              )
            : null,
        },

        [ROF_FIELDS.DEDUCTIONS]: {
          [ROF_FIELDS.PRICE_PER_KG]: toolMilkProductionData?.[
            ROF_FIELDS.DEDUCTIONS
          ]?.[ROF_FIELDS.PRICE_PER_KG]
            ? convertStringToNumber(
                toolMilkProductionData?.[ROF_FIELDS.DEDUCTIONS][
                  ROF_FIELDS.PRICE_PER_KG
                ],
              )
            : null,
        },
      },
      [ROF_FIELDS.MILK_PRODUCTION_OUTPUTS]: milkProductionOutputData,
      [ROF_FIELDS.SUMMARY]: {
        [ROF_FIELDS.PREVIOUS_RETURN_OVER_FEED_COSTS]: {
          [ROF_FIELDS.RETURN_OVER_FEED_COST_PER_COW_PER_DAY]:
            toolSummaryDataPreviousROFCost[
              ROF_FIELDS.RETURN_OVER_FEED_COST_PER_COW_PER_DAY
            ]
              ? convertStringToNumber(
                  toolSummaryDataPreviousROFCost[
                    ROF_FIELDS.RETURN_OVER_FEED_COST_PER_COW_PER_DAY
                  ],
                )
              : 0,
          [ROF_FIELDS.RETURN_OVER_FEED_COST_PER_KG_OF_BF]:
            toolSummaryDataPreviousROFCost[
              ROF_FIELDS.RETURN_OVER_FEED_COST_PER_KG_OF_BF
            ]
              ? convertStringToNumber(
                  toolSummaryDataPreviousROFCost[
                    ROF_FIELDS.RETURN_OVER_FEED_COST_PER_KG_OF_BF
                  ],
                )
              : 0,
          [ROF_FIELDS.RETURN_OVER_FEED_COST_PER_LITRE]:
            toolSummaryDataPreviousROFCost[
              ROF_FIELDS.RETURN_OVER_FEED_COST_PER_LITRE
            ]
              ? convertStringToNumber(
                  toolSummaryDataPreviousROFCost[
                    ROF_FIELDS.RETURN_OVER_FEED_COST_PER_LITRE
                  ],
                )
              : 0,
        },
      },
    };

    let calculatedOutputs = getCalculatedOutputsForTool(toolData, formType);
    let summaryValues = getROFSummaryResultValues(toolData, calculatedOutputs);

    toolData[ROF_FIELDS.SUMMARY] = summaryValues;
    toolData[ROF_FIELDS.CALCULATED_OUTPUTS] = calculatedOutputs;

    return toolData;
  } catch (e) {
    console.log('getROFToolDataForDB fail', e);
    logEvent('getROFToolDataForDB fail', e);
  }
};
//#endregion

// #region graphs
function filterRecentVisitsWithComparingVisitsIds(
  recentVisits,
  comparingVisitIds,
) {
  try {
    // filter visits that are selected for comparing
    const filteredVisits = recentVisits?.filter(el =>
      comparingVisitIds?.includes(el.id || el.visitId),
    );

    let formattedRecentVisits = [];
    if (filteredVisits?.length > 0) {
      formattedRecentVisits = filteredVisits.map(visitObj => {
        const parsedVisitData = getParsedToolData(
          visitObj[VISIT_TABLE_FIELDS.ROF],
        );

        return {
          rof: parsedVisitData || null,
          visitId: visitObj?.id,
          date: visitObj?.visitDate,
          mobileLastUpdatedTime: visitObj?.mobileLastUpdatedTime,
        };
      });
    }

    return formattedRecentVisits;
  } catch (error) {
    logEvent(
      'helpers -> rofHelper -> filterRecentVisitsWithComparingVisitsIds error ',
      error,
    );
    return [];
  }
}

export const createModelDataForROFGraphs = (
  currentToolData = {},
  recentVisits = [],
  comparingVisitIds = [],
  formType = '',
) => {
  try {
    const formattedRecentVisits = filterRecentVisitsWithComparingVisitsIds(
      recentVisits,
      comparingVisitIds,
    );

    const graphData = [];
    if (formattedRecentVisits?.length > 0) {
      const currentVisitROF = {
        ...getParsedToolData(formattedRecentVisits[0]?.rof),
        ...currentToolData,
      };
      formattedRecentVisits[0].rof = currentVisitROF;

      const rof = {
          dataPoints: [],
          onScreeColor: customColor.topScaleColor,
        },
        totalFeedCost = {
          dataPoints: [],
          onScreeColor: customColor.middleScaleColor,
        },
        totalRevenue = {
          dataPoints: [],
          onScreeColor: customColor.bottomScaleColor,
        };

      formattedRecentVisits.map(item => {
        const toolFormData = item.rof?.[formType];

        if (toolFormData && Object.keys(toolFormData)?.length > 0) {
          rof.dataPoints.push({
            x:
              getFormattedDate(item.date, DATE_FORMATS.MM_dd) +
              item?.mobileLastUpdatedTime,
            y: toolFormData?.[ROF_FIELDS.SUMMARY]?.[
              ROF_FIELDS.CURRENT_RETURN_OVER_FEED_COSTS
            ]?.[ROF_FIELDS.RETURN_OVER_FEED_COST_PER_COW_PER_DAY],
            onScreen:
              toolFormData?.[ROF_FIELDS.SUMMARY]?.[
                ROF_FIELDS.CURRENT_RETURN_OVER_FEED_COSTS
              ]?.[ROF_FIELDS.RETURN_OVER_FEED_COST_PER_COW_PER_DAY],
          });
          totalFeedCost.dataPoints.push({
            x:
              getFormattedDate(item.date, DATE_FORMATS.MM_dd) +
              item?.mobileLastUpdatedTime,
            y: toolFormData?.[ROF_FIELDS.SUMMARY]?.[ROF_FIELDS.FEED_COSTS]?.[
              ROF_FIELDS.TOTAL_FEED_COST_PER_COW_PER_DAY
            ],
            onScreen:
              toolFormData?.[ROF_FIELDS.SUMMARY]?.[ROF_FIELDS.FEED_COSTS]?.[
                ROF_FIELDS.TOTAL_FEED_COST_PER_COW_PER_DAY
              ],
          });
          totalRevenue.dataPoints.push({
            x:
              getFormattedDate(item.date, DATE_FORMATS.MM_dd) +
              item?.mobileLastUpdatedTime,
            y: toolFormData?.[ROF_FIELDS.SUMMARY]?.[ROF_FIELDS.REVENUE]?.[
              ROF_FIELDS.TOTAL_REVENUE_COW_DAY
            ],
            onScreen:
              toolFormData?.[ROF_FIELDS.SUMMARY]?.[ROF_FIELDS.REVENUE]?.[
                ROF_FIELDS.TOTAL_REVENUE_COW_DAY
              ],
          });
        }
      });

      graphData.push(rof);
      graphData.push(totalFeedCost);
      graphData.push(totalRevenue);
    }

    return graphData;
  } catch (e) {
    logEvent('helpers -> rofHelper -> createModelDataForROFGraphs fail', e);
    return null;
  }
};

/**
 * @description
 * helper function to model data for download or share using options like image or excel
 *
 * @returns
 */
export const downloadShareRofGraphDataModel = (
  rofToolData,
  currentVisit,
  comparingRofVisitsIds,
  recentVisits,
  formType,
  weightUnit,
  currencySymbol,
) => {
  if (rofToolData) {
    const model = {
      visitName: currentVisit?.visitName,
      visitDate: dateHelper.getFormattedDate(
        currentVisit?.visitDate,
        DATE_FORMATS.dd_MMM_yy,
      ),
      fileName:
        currentVisit?.visitName +
        '-' +
        i18n.t('ReturnOverFeed') +
        '-' +
        i18n.t(formType),
      toolName: i18n.t('ReturnOverFeed'),
      returnOverFeedLabel: i18n.t('ReturnOverFeed'),
      secondLabel: i18n
        .t('totalFeedCostPerCowPerDay')
        .replaceAll('$', currencySymbol)
        .replaceAll('kg', weightUnit),
      thirdLabel: i18n
        .t('totalRevenueCowDay')
        .replaceAll('$', currencySymbol)
        .replaceAll('kg', weightUnit),
      yAxisLabel: i18n.t('visitDate'),
      dataPoints: [],
    };

    model.dataPoints = createModelDataForROFGraphsForExports(
      rofToolData,
      recentVisits,
      comparingRofVisitsIds,
      formType,
    );

    return model;
  }

  return null;
};

function createModelDataForROFGraphsForExports(
  currentToolData = {},
  recentVisits = [],
  comparingVisitIds = [],
  formType = '',
) {
  try {
    const formattedRecentVisits = filterRecentVisitsWithComparingVisitsIds(
      recentVisits,
      comparingVisitIds,
    );

    const dataPoints = [];
    if (formattedRecentVisits?.length > 0) {
      const currentVisitROF = {
        ...getParsedToolData(formattedRecentVisits[0]?.rof),
        ...currentToolData,
      };
      formattedRecentVisits[0].rof = currentVisitROF;

      formattedRecentVisits.map(item => {
        const toolFormData = item.rof?.[formType];

        if (toolFormData && Object.keys(toolFormData)?.length > 0) {
          dataPoints.push({
            xAxis: getFormattedDate(item.date, DATE_FORMATS.MM_dd),

            returnOverFeed: parseFloat(
              toolFormData?.[ROF_FIELDS.SUMMARY]?.[
                ROF_FIELDS.CURRENT_RETURN_OVER_FEED_COSTS
              ]?.[ROF_FIELDS.RETURN_OVER_FEED_COST_PER_COW_PER_DAY].toFixed(
                ROF_DECIMAL_PLACES,
              ),
            ),

            totalFeedCost: parseFloat(
              toolFormData?.[ROF_FIELDS.SUMMARY]?.[ROF_FIELDS.FEED_COSTS]?.[
                ROF_FIELDS.TOTAL_FEED_COST_PER_COW_PER_DAY
              ].toFixed(ROF_DECIMAL_PLACES),
            ),

            totalRevenue: parseFloat(
              toolFormData?.[ROF_FIELDS.SUMMARY]?.[ROF_FIELDS.REVENUE]?.[
                ROF_FIELDS.TOTAL_REVENUE_COW_DAY
              ].toFixed(ROF_DECIMAL_PLACES),
            ),
          });
        }
      });
    }

    return dataPoints;
  } catch (e) {
    logEvent('helpers -> rofHelper -> createModelDataForROFGraphs fail', e);
    return null;
  }
}
//#endregion
