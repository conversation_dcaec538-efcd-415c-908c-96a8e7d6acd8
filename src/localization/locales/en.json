{"%": "%", "$": "$", "addContact": "Add contact", "addImage": "Add image", "addNew": "Add new", "addProspect": "Add prospect", "address": "Address", "all": "All", "animalClass": "Animal class", "animalInputPen": "Animal inputs, pen", "animalInputSite": "Animal input site", "animalsInPen": "Animals in pen", "animals": "Animals", "apply": "Apply", "asFedIntake": "As-fed intake", "avatorName": "Hidden cove ranch", "bacteriaCellCount": "Bacteria cell count", "bacteriaCellCountUnit": "1,000 cfu/mL", "barnName": "Barn name", "basicInformation": "Basic information", "businessAddress": "Business address", "businessName": "Business name", "businessNameExist": "Business name already exists", "cannotLogoutWhileSyncing": "Cannot logout while syncing", "cargillEmailPlaceholder": "Type your cargill email here...", "cargillLogin": "Login with cargill", "changeImage": "Change image", "chinese": "Chinese", "chooseLoginOptions": "Choose login option", "city": "City", "close": "Close", "confirmation": "Confirmation", "contactDetail": "Varius dictum tellus in orci vulputate eget tortor.", "continue": "Continue", "country": "Country", "createNewProspect": "Create new prospect", "createPen": "Create pen", "createProspect": "Create prospect", "createSite": "Create site", "currentMilkPrice": "Milk price", "currentMilkPriceHeatStress": "Milk price ($/Kg)", "customer": "Customer", "customerCode": "Customer code", "customerDetails": "Customer details", "customers": "Customers", "dairyEnteligen": "Dairy enteligen", "dashboard": "Dashboard", "dataLossMessage": "The changes you made will be discarded. Are you sure you want to continue?", "dateRange": "Date range", "days": "Days", "daysInMilk": "Days in milk (DIM)", "diet": "Diet", "dietInputPen": "Diet inputs, pen", "dietInputSite": "Diet input, site (lactating animals)", "dryMatterIntake": "Dry matter intake", "editCustomer": "Edit customer", "editProspect": "Edit prospect", "emailAddress": "Email address", "emailPlaceholder": "Enter email here...", "emailValidationError": "Incorrect email format", "emptyCustomerDescription": "Try refreshing your screen or contact the admin for support…", "emptyCustomerTitle": "No customers found!", "emptyProspectDescription": "Try adding a new one using the create prospect button…", "emptyProspectTitle": "No prospects found!", "english": "English", "error": "Error", "farmProducer": "Farm producer", "feedingSystem": "Feeding system", "filters": "Filters", "forgotPassword": "Forgot password?", "french": "French", "frenchCanada": "French (canada)", "fullName": "Full name", "general": "General", "generalCustomerSiteSetup": "General customer site setup", "hello": "Hello", "history": "History", "home": "Home", "housingSystem": "Housing system", "imageSizeError": "The image exceeds 20 mb limit. Please try again!", "inProgress": "In progress", "inputPlaceholder": "Enter text here...", "italian": "Italian", "kg": "kg", "korean": "Korean", "lactatingAnimals": "Animals in herd", "lactatingAnimal": "Lactating animals", "lastSynced": "Last synced", "lastVisit": "Last visit", "lastVisited": "Last visited", "login": "<PERSON><PERSON>", "loginWithOkta": "Login with okta", "logout": "Log out", "logoutConfirm": "Are you sure you want to log out?", "logoutConfirmation": "Are you sure you want to logout?", "menu": "<PERSON><PERSON>", "milkFat": "Milk fat", "milking_frequency": "Milking frequency", "milkingSystem": "Milking system", "milkOtherSolids": "Milk other solids", "milkProtein": "Milk protein", "milkYield": "Milk production", "more": "more", "NA": "N/A", "needHelpSigningIn": "Need help signing in?", "NELDairy": "NEL dairy", "NELDairyUnit": "Mcal/Kg", "newPen": "New pen", "newSite": "New site", "newVisit": "New visit", "next": "Next", "no": "No", "noAccountFound": "No account found", "noAccountFoundDescription": "We can’t find account matching your search.", "noContact": "No contacts", "noInternetConnection": "No internet connection", "noPensFound": "No pens found!", "noPensFoundDescription": "Try adding a new one using the create pen button…", "noRecordFound": "No records found!", "noResultShow": "No results found!", "noResultShowDescription": "Try changing the search criteria and try again…", "noSitesFound": "No sites found!", "noSitesFoundDescription": "Try adding a new one using the create site button...", "number": "Number", "number_of_stalls": "Number of stalls", "numberPlaceholder": "0", "ok": "Ok", "oktaEmailPlaceholder": "Type your email here...", "oktaLogin": "<PERSON><PERSON> login", "onboardingSubTitle1": "Start a new visit right from the home screen", "onboardingSubTitle2": "Note data for multiple tools & pens on the same screen", "onboardingSubTitle3": "Take notes on the go & set reminders for next visits", "onboardingSubTitle4": "Filter years’ old or recent information with your preferred criteria", "onboardingSubTitle5": "Pick & choose what you need to add to the visit report", "onboardingTitle1": "Faster processes", "onboardingTitle2": "Concurrent tools & pens", "onboardingTitle3": "Smart notebook", "onboardingTitle4": "Consolidated history", "onboardingTitle5": "Smart reporting", "or": "or", "other": "Other", "parlor": "<PERSON><PERSON><PERSON>", "password": "Password", "passwordPlaceholder": "Type your account password here...", "pen": "Pen", "penName": "Pen name", "pens": "Pens", "penSetup": "Pen setup", "phoneNumberPlaceholder": "Enter number here...", "pinned": "Pinned", "plusSign": "+", "polish": "Polish", "portugese": "Portugese", "postal/zipCode": "Postal/zip code", "primaryContactEmail": "Primary contact's email", "primaryContactInfo": "Primary contact info", "primaryContactInformation": "Primary contact information", "primaryContactPhone": "Primary contact's phone", "profilePhoto": "Profile photo", "prospect": "Prospect", "prospects": "Prospects", "prospectDetails": "Prospect details", "published": "Published", "rationCostPerAnimal": "Ration cost per animal", "recent": "Recent", "recentVisit": "Recent visits", "remove": "Remove", "required": "Required", "requiredFieldMsg": "Indicates a required field", "reset": "Reset", "resources": "Resources", "robot": "Robot", "russian": "Russian", "search": "Search", "searchCity": "Search city", "searchCountry": "Search country", "searchSegment": "Search segment", "searchState/province/region": "Search state/province/region", "searchType": "Search type", "segment": "Segment", "selecOne": "Select one…", "selectAnimalClass": "Select animal class", "selectCity": "Select city", "selectCountry": "Select country", "selectDateRange": "Select date range", "selectOne": "Select one...", "selectSegment": "Select segment", "selectState/province/region": "Select state/province/region", "selectType": "Select type", "site": "Site", "siteDescription": "Hiddencoveranch / agroacres", "siteName": "Site name", "sites": "Sites", "siteSetup": "Site setup", "skip": "<PERSON><PERSON>", "somaticCellCount": "Somatic cell count", "somaticCellCountUnit": "1,000 cells/mL", "somethingWentWrongError": "Something went wrong! Please try again later.", "stalls": "Stalls", "starSign": "*", "state/province/region": "State/province/region", "success": "Success", "successfullyCreated": "Successfully created!", "successfullyUpdated": "Successfully updated!", "switchTo": "Switch to", "syncNow": "Sync now", "toolsUsed": "Tools used", "totalStallsInParlor": "Total stalls in parlor", "type": "Type", "unsyncedDataLogoutMsg": "There is some unsynced data that you may want to sync before logout. Are you sure you want to continue?", "unsyncedItems": "Unsynced items", "update": "Update", "updatePen": "Update pen", "updateSite": "Update site", "uploadFromGallery": "<PERSON><PERSON>e gallery", "usd": "USD", "useCamera": "Use camera", "yes": "Yes", "primaryContactFullName": "Primary contact’s full name", "invalidNumber": "Invalid number", "selectSite": "Select site", "searchSite": "Search site", "siteSelectorInfoText": "Only one site can be selected", "customerProspect": "Customer / prospect", "selectCustomerProspect": "Select customer / prospect", "pinnedContacts": "Pinned accounts", "visitName": "Visit name", "visitNamePlaceholder": "Enter the visit name...", "selectTool": "Select tool", "favourite": "Favorites", "earTag": "Ear tag", "DIM": "DIM", "BCS": "BCS", "locomotion": "Locomotion", ":": ":", "save": "Save", "earTagPlaceholder": "Type to search or add a new ear tag...", "enterTagNumber": "Enter tag number", "selectCategory": "Select category", "locomotionScore": "Locomotion score", "noEarTagsMessage": "Seems like this is your first time adding ear tags.. Start typing in the box above to get going!", "newTagInfoText": "Type to create new tag", "selectDiet": "Select diet", "Dry": "Dry", "Heifer": "He<PERSON>", "Calf": "Calf", "Male": "Male", "Fresh": "Fresh", "Milking": "Milking", "LowForage": "Low forage", "Pasture": "Pasture", "FreshHeifer": "Fresh heifer", "AAEfficiency": "AA efficiency", "CloseUp": "Close up", "FarOff": "Far off", "CloseUpHeifer": "Close up heifer", "ShortDryPeriod": "Short dry period", "Bull": "Bull", "Steer": "Steer", "Lactating": "<PERSON><PERSON>", "confirm": "Confirm", "siteDetails": "Site details", "fetchingSyncData": "Fetching and syncing data...", "selectFollowing": "Select the following", "tools": "Tools", "toolsCap": "Tools", "selectToolToSwitch": "Select any tool to switch", "animalAnalysis": "Animal analysis", "penAnalysis": "Pen analysis", "herdAnalysis": "Herd analysis", "done": "Done", "results": "Results", "noRecordFoundDescription": "Try refreshing your screen or sync the application to view results…", "chewing": "Chewing", "notChewing": "Not chewing", "cow": "Cow", "addNewCow": "Add new cow", "steps": "Steps", "compare": "Compare", "cancel": "Cancel", "addAnimal": "Add animal", "selectPen": "Select pen", "searchPen": "Search pen", "selectScale": "Select scale", "CREATE": "Create", "category": "Category", "penPercent": "Pen %", "animalsObserved": "Animals observed", "animalsObs": "Animals obs", "goals": "Goals", "chewsPerCud": "Chews per cud", "scoreAnalysis": "Score analysis", "referenceTable": "Reference table", "cat": "Cat.", "lossCow": "Loss / cow", "fromPenSetup": "From pen setup", "animalInHerd": "Animals in herd", "animalInPen": "Animals in pen", "animalObserved": "Animals observed", "milkProduction": "Milk production", "milkProductionHeatStress": "Milk production (Kgs)", "milkLoss": "Milk loss", "revenue": "Revenue", "herdAverage": "Herd avg", "totalAnimals": "Total animals", "herdGoal": "Herd goal", "fromSiteSetup": "From site setup", "kgs": "Kgs", "scaleChangeMessage": "Changing the scale may result in data loss. Are you sure you want to continue?", "alreadyUsedEarTagMsg": "This tag is already being used in the current visit.", "cudChewingScoreAnalysis": "Cud chewing score analysis", "avgChewsPerCud": "Avg chews per cud", "farOffDry": "Far-off dry", "closeUpDry": "Close-up dry", "fresh": "Fresh", "earlyLactation": "Early lactation", "peakMilk": "Peak milk", "midLactation": "Mid lactation", "lateLactation": "Late lactation", "cudChewingGoals": "Cud chewing goals", "cudChews": "Cud chews", "noOfChewsPerCud": "No. of chews", "cudChewingPercent": "Cud chewing %", "cudChewing": "Cud chewing", "goalCudChewingPercent": "Goal cud chewing %", "noOfChews": "No. of chews", "chewsPerRegurgitation": "No. of chews per regurgitation", "goalChews": "Goal chews", "animal": "Animal", "selectAnimal": "Select animal", "searchAnimal": "Search animal", "avg": "Avg", "std": "Std", "compareVisits": "Compare visits", "CalfandHeifer": "Calf & heifer", "Comfort": "Comfort", "Health": "Health", "Nutrition": "Nutrition", "Productivity": "Productivity", "CalfHeiferScorecard": "Calf and heifer scorecard", "HeatStress": "Heat stress evaluation", "PenTimeBudgetTool": "Pen time budget", "RumenHealth": "Rumen health cud chewing", "RumenHealthManureScore": "Rumen health manure score", "ManureScreening": "Manure screener", "manureScreening": "Manure screening", "ManureScreener": "Rumen health manure screening", "RumenFill": "Rumen fill", "LocomotionScore": "Locomotion score", "BodyCondition": "Body condition score", "UrinePHTool": "Urine PH tool", "MetabolicIncidence": "Metabolic incidence", "ReadyToMilk": "Ready to milk", "ForageAuditScorecard": "Forage audit", "PileAndBunker": "Forage Inventories", "Revenue": "Revenue", "MilkSoldEvaluation": "Milk sold evaluation", "RoboticMilkEvaluation": "Robotic milking evaluation", "leastObserveAnimals": "A minimum of 10 animals need to be observed", "toolsInProgress": "tool(s) in progress", "dimUpdateValidation": "This value will also be changed in site/pen setup and/or other tools. Are you sure you want to continue?", "alert": "<PERSON><PERSON>", "milkKg": "Milk (Kg)", "decimalNumberPlaceholder": "0.00", "singleDecimalNumberPlaceholder": "0.0", "milkPrice": "Milk price", "milkProductionKg": "Milk production", "MilkLoss": "Milk loss", "milkLossKGDay": "Milk loss", "milkLossKGYear": "Milk loss", "RevenueLossDay": "Revenue loss", "RevenueLossYear": "Revenue loss", "milkPriceKG": "Milk price ($/Kg)", "milkProductionInKg": "Milk production (Kg)", "MilkLossKg": "Milk loss (Kg)", "goal": "Goal", "percent": "Percent", "locomotionScoreAnalysis": "Locomotion score analysis", "totalAnimalsInPen": "Total animals in pen", "herdLocomotionScore": "Herd locomotion score", "lactationRange1": "less than -21", "lactationRange2": "-21 to -1", "lactationRange3": "0 to 15", "lactationRange4": "16 to 60", "lactationRange5": "61 to 120", "lactationRange6": "121 to 200", "lactationRange7": "greater than 200", "lactationRange8": "", "lactationRange9": "", "lactationRange10": "", "lactationRange11": "", "BCSByStageOfLactation": "BCS by stage of lactation", "scoreStageLactation": "Score by stage of lactation", "GoalMIN": "Goal % (min)", "GoalMAX": "Goal % (max)", "milkHdDay": "Milk/hd/day", "BCSAvg": "BCS avg", "min": "Min", "max": "Max", "lactationStages": "Lactation stages", "milk": "Milk", "searchToolByName": "Search tool by name...", "excel": "Excel", "jpeg": "JPEG", "downloadVia": "Download via", "shareVia": "Share via", "downloadedSuccessfully": "Downloaded successfully", "attachedFileSuccessfully": "File attached successfully", "errorDownloadingFile": "Error downloading your file", "markedAsFavoriteSuccess": "Updated successfully", "deleteVisitAlertTitle": "Delete visit", "deleteVisitAlertDesc": "Deleting will lose all your data within the visit. Are you sure you want to continue?", "publish": "Publish", "visitDetails": "Visit details", "visitReport": "Visit report", "deleteVisit": "Delete visit", "deleteAnimalMessage": "Are you sure you want to delete this animal?", "bcsDataTransferMessage": "Animal analysis is optional and the data will be transferred to pen analysis", "noFavoritesFound": "No favorites found!", "noFavoritesFoundDescription": "Try marking a few frequent tools as favorite to see results…", "visitHistory": "Visit history", "searchByVisitName": "Search by visit name", "dairyFarmConsultant": "Dairy farm consultant", "-": "-", "outputs": "Outputs", "animalsInTank": "Animals in tank", "milkPickUp": "Milk pickup", "dryMatterIntake(Kg)": "Dry matter intake", "milkUreaMeasure": "Milk urea measure", "asFedIntake(Kg)": "As-fed intake", "NELDairy(Kg)": "NEL dairy", "dryMatterIntakeIn(Kg)": "Dry matter intake (Kg)", "asFedIntakeIn(Kg)": "As-fed intake (Kg)", "NELDairyIn(Kg)": "NEL dairy (Mcal/Kg)", "rationCostPerAnimal($)": "Ration cost, per animal ($)", "rationCostPerAnimalWithCurrency($)": "Ration cost, per animal", "herdLevelInfo": "Herd level information", "milkProcessorInfo": "Milk processor information", "addPickup": "Add pickup", "editPickup": "Edit pickup", "milkSolid(kg)": "Milk sold", "milkSolidIn(kg)": "Milk sold (Kg)", "daysInTank": "Days in tank", "milkFat(%)": "Milk fat (%)", "milkProtein(%)": "Milk protein (%)", "nonFatSolid": "Non-fat solid", "MUN(mg/dL)_Milk Urea(mg/dL)": "MUN (mg/dL) or milk urea (mg/dL)", "MUN(mg/dL)": "MUN (mg/dL)", "MilkUrea(mg/dL)": "Milk urea (mg/dL)", "somaticCellCountWithUnit": "Somatic cell count (1,000 cells/mL)", "bacteriaCellCountWithUnit": "Bacteria cell count (1,000 cfu/mL)", "Mastitis(#/Month)": "Mastitis (#/month)", "deletePickupMessage": "Are you sure you want to delete this pickup?", "evaluationDays": "Evaluation days", "averageMilkProduction": "Average milk production", "averageMilkProductionAnimalsInTank": "Average milk production - animals in tank", "averageMilkFat": "Average milk fat (%)", "averageMilkYield": "Milk fat yield", "averageMilkProtein": "Average milk protein", "milkProteinYield": "Milk protein yield", "milkFatProteinYield": "Milk fat + protein yield", "averageMilkProductionKg": "Average milk production (Kg)", "averageMilkProductionAnimalsInTankKg": "Average milk production - animals in tank (kg)", "milkSolidNonFat": "Milk solids non-fat", "MUNMilkUrea": "MUN (mg/dL) or milk urea (mg/dL)", "averageSomaticCellCount": "Average somatic cell count (1,000 cells/mL)", "averageBacteriaCellCount": "Average bacteria cell count (1,000 cfu/mL)", "feedEfficiencyRatio": "Feed efficiency (ratio)", "manureScore": "Manure score", "avgManureScore": "Avg manure score", "herdLevelInformation": "Herd level information", "totalFreshCowsPerYear": "Total fresh cows/year", "replacementCowCost": "Replacement cow cost", "costOfExtraDaysOpen": "Cost of extra days open", "metabolicIncidenceCases": "Metabolic incidence cases", "metabolicIncidenceCasesContent": "Enter the number of fresh cows and the number of metabolic incidence cases during the evaluation period. This will be converted to an annualized incidence cost in the outputs.", "totalFreshCowsEvaluation": "Total fresh cows/evaluation", "retainedPlacenta": "Retained placenta", "metritis": "<PERSON><PERSON><PERSON>", "displacedAbomasum": "Displaced abomasum", "ketosis": "Ketosis", "milkFever": "Milk fever", "dystocia": "<PERSON><PERSON><PERSON><PERSON>", "deathLoss": "Death loss", "performanceAndTreatmentCosts": "Performance & treatment costs", "performanceAndTreatmentCostsContent": "Reference data used to calculate the economic impact of each metabolic incidence.", "milkPerCow": "Milk/cow", "daysOpen": "Days open", "treatmentDefault": "Treatment default", "metabolicIncidencePercent": "Metabolic incidence %", "case": "Case", "email": "Email", "componentEfficiencyOfDMI": "Component efficiency (% of DMI)", "milkProductionDim": "Milk production & dim", "componentYieldEfficiency": "Component yield & efficiency", "milkFatMilkProtein": "Milk fat % & milk protein %", "somaticCellCountMilkUrea": "Somatic cell count & milk urea", "dryMatterIntakeEfficiency": "Dry matter intake & feed efficiency", "result": "Result", "searchResult": "Search result", "componentYield": "Component yield", "componentEfficiency": "Component efficiency", "milkUrea": "Milk urea", "feedEfficiency": "Feed efficiency", "indicateRequiredField": "Indicates a required field", "robotType": "Type of robot", "cowFlowDesign": "Cow flow design", "robotsInHerd": "Robots in herd", "lactatingCows": "Lactating cows", "averageMilkYieldLely": "Milk/cow/day (Kg/cow)", "averageMilkYieldDeLaval": "Avg. milk yield (Kg/cow)", "averageMilkYieldOther": "Avg. milk yield (Kg/cow)", "averageMilkYieldGEA": "Avg. milk production (Kg/cow)", "milkingsLely": "Milkings/cow/day", "milkingsDeLaval": "Avg. daily milkings per animal (cow)", "milkingsGEA": "Avg. nr milkings/cow (/cow)", "milkingsOther": "Milkings (/cow)", "robotFreeTimeLely": "Free time (%)", "robotFreeTimeGEA": "Free time (%)", "robotFreeTimeDeLaval": "Idle time (%)", "robotFreeTimeOther": "Robot free time (%)", "milkingRefusalsLely": "Refusals", "milkingRefusalsDeLaval": "Refusals", "milkingRefusalsGEA": "Refusals (/cow)", "milkingRefusalsOther": "Milking refusals (/cow)", "totalMilkingFailuresLely": "Failure (Total/day)", "totalMilkingFailuresDeLaval": "Nb incomplete (/herd)", "totalMilkingFailuresGEA": "Number incomplete milkings (/herd)", "totalMilkingFailuresOther": "Total milking failures (/herd)", "maximumConcentrate": "Maximum concentrate (Kg/cow)", "averageConcentrateFedLely": "Avg. concentrate fed (Kg/cow)", "averageConcentrateFedDeLaval": "Avg. concentrate consumed (Kg/cow)", "averageConcentrateFedGEA": "Concentrate fed (Kg/cow)", "averageConcentrateFedOther": "Avg. concentrate fed (Kg/cow)", "minimumConcentrate": "Minimum concentrate (Kg/cow)", "averageBoxTimeLely": "Box time (min/cow)", "averageBoxTimeDeLaval": "Avg. milk duration per milking (min/cow)", "averageBoxTimeGEA": "Avg. staying time (min/cow)", "averageBoxTimeOther": "Avg. box time (min/cow)", "milkingSpeedLely": "Milk speed (Kg/min)", "milkingSpeedOther": "Milking speed (Kg/min)", "milkingSpeedDeLaval": "Avg. daily harvesting flow per animal (Kg/min)", "milkingSpeedGEA": "Avg. milk speed (Kg/min)", "concentratePer100KGMilkLely": "Concen./kg milk (Kg/100 Kg milk)", "concentratePer100KGMilkDeLaval": "Feed/milk ratio (Kg/100 Kg milk)", "concentratePer100KGMilkGEA": "Concen./100kg milk (Kg/100 Kg milk)", "concentratePer100KGMilkOther": "Concen./100kg milk (Kg/100 Kg milk)", "restFeedLely": "Rest feed (%)", "restFeedDeLaval": "Concentrate NOT cons. (%)", "restFeedGEA": "Rest feed (%)", "restFeedOther": "Rest feed (%)", "restFeedLavalInfo": "Rest feed: 100 - % cons. Yest.", "restFeedGeaInfo": "Calculate rest feed (Kg) / total fed", "100KGMilkLaval": "System shows 1 kg/ milk so x 100", "totalMilkingFailuresLavalInfo": "Total milkings x % incomplete", "totalMilkingFailuresGeaInfo": "Calculate refusal; Avg. NR of visit/cow - Avg.NR milkings/cow", "milkingFailuresLavalInfo": "Static system: total refusals/cow number", "milkingFailuresGeaInfo": "Avg. NR of visit/cow - Avg. NR milkings/cow", "robotFreeGeaInfoUtilizationTime": "Utilization time: 100-system", "robotFreeGeaInfoUtilizationFreeTime": "Utilization % = free time", "cowsPerRobot": "Cows per robot", "milkingsPerRobot": "Milkings per robot", "milkPerRobot": "Milk per robot (Kg)", "milkingFailures": "Milking failures (/robot)", "amsUtilization": "AMS utilization", "cowEfficiency": "Cow efficiency", "avgBoxTime": "Avg. box time (min/cow)", "avgConcentrate": "Avg. concentrate (Kg/cow)", "analysis": "Analysis", "summary": "Summary", "graph": "Graph", "incidence": "Incidence", "difference": "Difference", "annualEconomicImpact": "Annual economic impact", "milkLossValue": "Milk loss value", "increasedDaysOpen": "Increased days open", "treatmentCost": "Treatment cost", "totalAnnualLosses": "Total annual losses", "annualEconomicImpactTotal": "Annual economic impact - total", "totalCost": "Total cost", "currentCostPerCow": "Current cost/cow", "metabolicDisorderCostPerCow": "Metabolic disorder cost/cow", "selectGraph": "Select graph", "current": "Current", "agree": "Agree", "disagree": "Disagree", "appSetting": "App setting", "appSettings": "App settings", "unitOfMeasure": "Unit of measure", "unitOfMeasureDescription": "The following option can be changed later in the app settings.", "bodyConditionScoreScale": "Body condition score scale", "brandPlaceholder": "Brand placeholder", "currency": "<PERSON><PERSON><PERSON><PERSON>", "endUserLicenseAgreement": "End user license agreement", "privacyStatement": "Privacy statement", "privacyStatementEn": "Privacy statement", "siteNameExist": "Site name already exists", "penNameExist": "Pen name already exists", "&": "&", "dataInput": "Data input", "analysisSelection": "Analysis selection", "noResourcesAvailable": "No resources available!", "somaticCellCountMilkMun": "Somatic cell count & mun", "mun": "<PERSON><PERSON>", "concentrateDistribution": "Concentrate distribution", "selectMonth": "Select month in ", "selectYear": "Select year", "sitePenValueChange": "Some values like DIM, animals etc will be updated in pen/site setup and/ or tools of in progress visits, if changed here.", "allNotes": "All notes", "newNotes": "New note", "editNote": "Edit note", "addInventories": "Add inventories", "pile": "<PERSON><PERSON>", "bunker": "Bunker", "bag": "Bag", "topUnloadingSilo": "Top unloading silo", "bottomUnloadingSilo": "Bottom unloading silo", "inventoryList": "Inventory list", "densityConverter": "Density converter", "densityConverterFirstColumnHeader": "lb/ft^3", "densityConverterSecondColumnHeader": "Kg/m^3", "add": "Add", "pileName": "Pile name", "capacity": "Capacity", "feedOut": "Feedout", "pileDimension": "Pile dimension", "height": "Height", "topWidth": "Top width", "bottomWidth": "Bottom width", "bottomLength": "Bottom length", "topLength": "Top length", "dryMatterPercent": "Dry matter (%)", "silageDMDensity": "Silage DM density", "tonsDM": "Tons DM", "tonsAF": "Tons AF", "remainingInSilo": "(Remaining in silo)", "topUnloadingSiloDimension": "Top unloading silo dimension", "feedOutRateInformation": "Feed out rate information", "topUnloadingSiloName": "Top unloading silo name", "fillHeight": "Filled height", "heightOfSilageLeftInSilo": "Height of silage left in silo", "dryMatter": "Dry matter", "feedoutRateInformation": "Feedout rate information", "feedingRate": "Feeding rate (as-fed / cow)", "cowsToBeFed": "Cows to be fed", "lbsDMinFoot": "Lbs DM in 1 foot Or Kgs DM in 1 m.", "inchesCmPerDay": "Inches or Cm per Day", "at3InchesDayOrAt7CmPerDay": "At 3 inches per day Or at 7 cm. per day", "at3InchesDay": "At 3 inches per day", "at7CmDay": "At 7 cm. per day", "at6InchesDayOrAt15CmPerDay": "At 6 inches per day Or at 15 cm. per day", "at6InchesDay": "At 6 inches per day", "at15CmPerDay": "At 15 cm. per day", "bottomUnloadingSiloDimension": "Bottom unloading silo dimension", "bottomUnloadingSiloName": "Bottom unloading silo name", "meterUnit": "m.", "feetUnit": "ft.", "meters": "meters", "feet": "feet", "imperialDensityUnit": "lbs/ft^3", "metricDensityUnit": "kg/m^3", "silageAFDensity": "Silage AF density", "pileSilageAFDensityGoal": "Goal: > 705", "pileSlopeGoal": "Goal > 3.5 to 1.0", "slope": "Slope", "to1": "to 1.0", "lbsDMIn1Foot": "lbs DM in 1 foot", "kgsDMIn1M": "Kgs DM in 1 m.", "feedOutSurfaceArea": "Feed out surface area", "footSquare": "ft^2", "meterSquare": "m^2", "inchesPerDay": "Inches per day", "cmPerDay": "Cm per day", "tonsPerDay": "Tons per day", "cowsDayNeeded": "Cows/day needed", "at3InchesPerDay": "At 3 inches per day", "at7cmPerDay": "At 7 cm. per day", "at6InchesPerDay": "At 6 inches per day", "at15cmPerDay": "At 15 cm. per day", "startDate": "Start date", "endDate": "End date", "deleteInventoryMessage": "Are you sure you want to delete this inventory item?", "select": "Select...", "namePlaceholder": "Enter name here...", "bunkerName": "Bunker name", "bunkerDimension": "Bunker dimension", "bunkerSlopeGoal": "Goal > 3.5 to 1.0", "bagName": "Bag name", "bagDimension": "Bag dimension", "length": "Length", "diameter": "Diameter", "dmDensity": "DM density", "PSP": "PSPS", "addPSPS": "Add PSPS", "scorer": "Scorer", "top(19mm)": "Top (19 mm) (g)", "mid1(18mm)": "Mid 1 (8 mm) (g)", "mid2(4mm)": "Mid 2 (4 mm) (g)", "tray(g)": "Tray (g)", "silage": "Silage", "twoNumberPlaceholder": "00", "silageName": "Silage name", "scorerChangeMessage": "Changing the scorer may result in data loss. Are you sure you want to continue?", "goalMinPercent": "Goal % (min)", "goalMaxPercent": "Goal % (max)", "maximumScoresAllowed": "A maximum of 10 scores per visit can be added", "addTmrScore": "Add TMR score", "tmrScore": "TMR score", "tmrScoreName": "TMR score name", "enterName": "Enter name", "top": "Top", "mid1": "Mid 1 (8mm)", "mid2": "Mid 2 (4mm)", "tray": "Tray", "onScreen": "On screen %", "foragePenToastMessage": "A maximum of 10 forage penn states per visit can be added", "tmrParticleScoreToastMessage": "A maximum of 10 scores per visit can be added", "foragePennState": "Forage penn state", "silageCreatedSuccess": "Silage created successfully", "silageUpdatedSuccess": "Silage updated successfully", "silageNameExist": "Silage name already exists", "avgTmrParticleScore": "Avg. TMR particle score", "standardDeviation": "Standard deviation", "goalMax%": "Goal - min (%)", "goalMin%": "Goal - max (%)", "tmrParticleScore": "TMR penn state analysis", "avgRumenFillScore": "Avg rumen fill score", "rumenFillScore": "Rumen fill score", "rumenFill": "Rumen fill", "emptyNotesTitle": "No notes found!", "emptyNoteDescription": "Try refreshing your screen or contact the admin for support…", "creationDate": "Creation date", "updateDate": "Update date", "selectVisit": "Select visit", "searchVisit": "Search visit", "searchTool": "Search tool", "toolName": "Tool name", "visitSelected": "visits selected", "toolsSelected": "tools selected", "accountsSelected": "accounts selected", "sitesSelected": "sites selected", "action": "Action", "requiredTitleField": "Title is required.", "requiredNoteField": "Note required.", "requiredActionNoteDateTime": "Set reminder date & time is required.", "syncMessage": "You haven't synced the app in the last 24 hours. Please sync it asap to avoid any data loss!", "notifications": "Notifications", "markAllRead": "Mark all as read", "ForagePennState": "Forage penn state", "ago": "ago", "noNotification": "There is no notifications in app", "emptyList": "List is empty", "upcomingActions": "Upcoming actions", "noPinnedContacts": "No pinned accounts", "viewAll": "View all", "noRecentVisits": "No recent visits", "noneSelected": "None selected", "removeTrail": "Remove trail", "setReminder": "Set reminder", "titleExists": "Title already exists", "primaryContactFirstName": "Primary contact’s first name", "primaryContactLastName": "Primary contact’s last name", "sharepointReportError": "Unable to download the file. Please contact your data steward to check mapping.", "tryAddingNewOne": "Try adding a new one...", "summaryReport": "Summary report", "detailedReport": "Detailed report", "tomorrow": "Tomorrow", "today": "Today", "older": "Older", "actionCapital": "Action", "noUpcomingActions": "No upcoming actions", "noteBookTrailInfo": "Removing the trail will limit the search to title and date only. Once removed, cannot be restored", "noActionText": "Try adding a new note as action from the notebook and it will start appearing here to remind you of your important tasks...", "Untitled": "Untitled", "fieldsAreEmpty": "Required fields are empty", "photos": "Photos", "videos": "Videos", "dateOfVisit": "Date of visit", "selectSiteDetails": "Select site details", "allDetails": "All details", "siteInputsSelected": "sites inputs selected", "penAll": "Pen (all)", "pensSelected": "pens selected", "toolDetails": "Tool details", "lastUpdated": "Last updated:", "noToolsInProgress": "No tools in progress", "addComments": "Add comments", "share": "Share", "download": "Download", "herd": "Herd", "penIsRequired": "Pen is required", "notes": "Notes", "attachments": "Attachments", "attachMoreNotes": "Attach more notes for this tool", "addTool": "Add tool", "updateTool": "Update tool", "removeTool": "Remove tool", "animalsSelected": "animals selected", "animalAll": "Animal (all)", "selectAnyFollowing": "Select any of the following", "manureScreenerName": "Screener name", "top(g)": "Top (g)", "middle(g)": "Middle (g)", "bottom(g)": "Bottom (g)", "observation": "Observation", "onScreen(%)": "On screen (%)", "categories": "Categories", "animalInputs": "Animal inputs", "weather": "Weather", "exposure": "Exposure", "humidity(%)": "Humidity (%)", "hoursOfSun": "Hours of sun", "temperature(C)": "Temperature (°C)", "temperature(F)": "Temperature (°F)", "stressThreshold": "Stress threshold", "stressThresholdText": "Respiration exceeds 60 BPM | repro losses detectable | rectal temperature exceeds 38.5°C (101.3°F)", "mildModerateStress": "Mild - moderate stress", "mildModerateStressText": "Respiration exceeds 75 BPM  | rectal temperature exceeds 39°C (102.2°F)", "moderateSevereStress": "Moderate - severe stress", "moderateSevereStressText": "Respiration exceeds 85 BPM  | rectal temperature exceeds 40°C (104°F)", "severeStress": "Severe stress", "severeStressText": "Respiration exceeds 120-140 BPM  | rectal temperature exceeds 41°C (106°F)", "temperatureHumidityIndex": "Temperature humidity index", "intakeAdjustmentPercent": "Intake adjustment", "dmiReductionPercent": "DMI adjustment", "estimatedDryMatterIntakeWeightInkg": "Estimated dry matter intake (Kg)", "estimatedDryMatterIntakeWeightInLbs": "Estimated dry matter intake (lbs)", "reductionInDMIWeightInkg": "Reduction in DMI (Kg)", "reductionInDMIWeightInLbs": "Reduction in DMI (lbs)", "lossOfEnergyConsumedInMcal": "Loss of energy consumed (Mcal)", "energyEquivalentMilkLossWeightInkg": "Energy equivalent milk loss (Kg)", "energyEquivalentMilkLossWeightInLbs": "Energy equivalent milk loss (lbs)", "milkValueLossPerDay": "Milk value loss (per day)", "milkValueLossPerMonth": "Milk value loss (per month)", "legend": "Legend", "heatStressSheet": "Heat stress sheet", "exportLogs": "Export logs", "exportLogsAlertText": "Device logs uploaded successfully", "completed": "Completed", "score": "Score", "responses": "Responses", "question": "Question", "answers": "Answers", "optimal": "Optimal", "viewOverAllScore": "View overall score", "overallForageScore": "Overall forage score", "forageAuditCategories": "Forage audit categories", "forageManagement": "Forage management", "forageQualityRation": "Forage quality in the ration", "bunkerAndPiles": "Bunkers and piles", "towerSilos": "Tower silos", "silageBags": "Silage bags", "baleage": "Baleage", "cornSilage": "Corn silage", "haylage": "Hay<PERSON>", "otherSilage": "Other silage", "cornSilageQuestion1": "How often is forage inventory monitored?", "cornSilageQuestion2": "Are the number of cows and forage needs to be planned annually?", "cornSilageQuestion3": "Are the forage storage units sized for capacity compared to the needs of the dairy herd? (no overfilling)", "cornSilageQuestion4": "Are all forages inspected for spoilage and mold and is spoiled forage discarded?", "cornSilageQuestion5": "Are forage harvesting conditions, field, and storage locations documented?", "cornSilageQuestion6": "Is whole plant moisture determined for each field?", "cornSilageQuestion7": "Are forages harvested at proper moisture and moisture for crop type and storage facility?", "cornSilageQuestion8": "Are forages harvested at proper maturity and moisture for crop type and storage facility?", "cornSilageQuestion9": "How long does it take to complete the harvest? (Adequate equipment and labor)", "cornSilageQuestion10": "Is the length of cut monitored with a penn state shaker box at harvest?", "cornSilageQuestion11": "What is the length of chop of the sample? (cargill forage particle score)", "cornSilageQuestion12": "How many whole kernels are present in 1L (32 ounces)?", "cornSilageQuestion13": "What is the kernel hardness score in the corn silage?", "cornSilageQuestion14": "Are silage additives, inoculants, or aerobic stabilizers used? (H7)", "cornSilageQuestion15": "What are ash levels?", "cornSilageQuestion16": "What are the lactic to acetic acid ratios?", "haylageQuestion6": "Are forages harvested at proper maturity/moisture for crop type and storage facility?", "haylageQuestion7": "How long does it take to complete the harvest? Are the equipment and labor adequate?", "haylageQuestion8": "Is the length of cut monitored with a Penn State Shaker box?", "haylageQuestion9": "Are silage additives, inoculants, or aerobic stabilizers used?", "haylageQuestion10": "What are the butyric acid levels?", "otherSilageQuestion8": "Is the length of cut monitored with a penn state shaker box at harvest?", "forageQualityRationQuestion1": "Is a stabilizer/mold inhibitor used in TMR during hot/humid weather?", "forageQualityRationQuestion2": "Does the TMR mix smell good?", "forageQualityRationQuestion3": "Is the TMR mix cool to the touch?", "forageQualityRationQuestion4": "Are there any refusals removed and measured daily?", "bunkerPilesQuestion1": "What is the cleanliness of the feed area?", "bunkerPilesQuestion2": "Packing: are layers 15 cm (6 inches) or less?", "bunkerPilesQuestion3": "What is the porosity score with respect to the DM?", "bunkerPilesQuestion4": "Is the pile and bunker slope less than a 3:1 run to rise ratio?", "bunkerPilesQuestion5": "Are the tires/splits touching?", "bunkerPilesQuestion6": "Are side walls sealed with plastic?", "bunkerPilesQuestion7": "Are bunkers/piles covered with 2 layers of plastic and a layer of non-permeable plastic?", "bunkerPilesQuestion8": "How long after packing are bunkers/piles sealed with 8mm plastic?", "bunkerPilesQuestion9": "Is the silage free from any visible signs of soil contamination?", "bunkerPilesQuestion10": "Loose or 'faced' feed is fed within", "bunkerPilesQuestion11": "What is the face removal rate?", "bunkerPilesQuestion12": "To what frequency is the cover plastic removed from silage?", "bunkerPilesQuestion13": "Is the face smooth? (no indication of disrupted layers allowing oxygen penetration?", "silageBagsQuestion1": "Are bags placed on a stable, well-managed all-season surface?", "silageBagsQuestion2": "Are trash, vegetation, and rodents controlled around bags?", "silageBagsQuestion3": "Is a security cover used? (SB3)", "silageBagsQuestion4": "Are bags inspected for pest hole damage and repaired on a regular basis?", "silageBagsQuestion5": "What is the face removal rate?", "silageBagsQuestion6": "Is the face clean and well managed with no indication of loose feed heating and shrinking?", "silageBagsQuestion7": "What is the porosity score consistent, with respect to the DM?", "baleageQuestion1": "Are bales placed on a stable, well-managed all-season surface?", "baleageQuestion2": "Are trash, vegetation, and rodents controlled around bales?", "baleageQuestion3": "Are bales inspected for pest hole damage and repaired on weekly basis?", "baleageQuestion4": "Does the water shed off the plastic and not into baleage? (challenge with large square bales)", "baleageQuestion5": "Are bales wrapped with:", "towerSilosQuestion1": "Is the silo covered for a month after filling if not used immediately?", "towerSilosQuestion2": "Is the filling time per silo 3 days or less?", "towerSilosQuestion3": "Is the removal rate greater than 10 cm (4 inches) per day?", "quarterly": "Quarterly", "semiAnnual": "Semi annual", "annual": "Annual", "lessThanFourDays": "Less than 4 days", "fourToSevenDays": "4 to 7 days", "greaterThanSevenDays": "Greater than 7 days", "moreThan40Percent": "> 40%", "20To40Percent": "20-40%", "lessThan20Percent": "< 20%", "notMeasured": "Not measured", "noWholeKernels": "No whole kernels", "lessThan5Kernels": "Less than 5 whole kernels", "moreThan5Kernels": "Greater than 5", "lessThan15": "< 15 (kernel hardness)", "between15And20": "Between 15 and 20", "moreThan20": "> 20", "lessThan5": "< 5%", "moreThan5": "> 5%", "moreThan3": "> 3:1", "lessThan3": "< 3:1", "lessThan10Percent": "< 10%", "10To13Percent": "10-13%", "moreThan13Percent": "> 13%", "lessThan1Percent": "< 1%", "1To3Percent": "1-3%", "moreThan3Percent": "> 3%", "good": "Good", "average": "Average", "poor": "Poor", "lessThan40PercentDM": "< 40% DM", "moreThan40PercentDM": "> 40% DM", "lessThan1Hour": "Less than 1 hour", "withIn8Hours": "Within 8 hours", "moreThan8Hours": "Greater than 8 hours", "1To6Hours": "1 to 6 hours", "6To12Hours": "6 to 12 hours", "moreThan12Hours": "Greater than 12 hours", "12InchesOrMore": "12 inches or greater", "6To12Inches": "6 to 12 inches", "lessThan12Inches": "Less than 6 inches", "3xWeek": "3x a week", "2xWeek": "2x a week", "1xWeek": "1x a week", "weekly": "Weekly", "biWeekly": "Bi-weekly", "monthly": "Monthly", "moreThan30cm": "Greater than 30 cm (12 inches) per day", "15To30cm": "15 to 30 cm (6 to 12 inches per day", "lessThan15cm": "Less than 15 cm (6 inches) per day", "moreThan8layersPlastic": "More than 8 layers of plastic", "6To8layersPlastic": "6 to 8 layers", "lessThan6layersPlastic": "Less than 6 layers", "removedAndMeasured": "Removed and measured", "removedOnly": "Removed only", "notRemoved": "Not removed", "walkingTimeToParlor": "Walking time to parlor (hrs)", "timeInParlor": "Time in parlor (hrs)", "walkingTimeFromParlors": "Walking time from parlor (hrs)", "milkingFrequency": "Milking frequency", "timeInLockUp": "Time in lock-up (hrs)", "otherNonRestTime": "Other non-rest time (hrs)", "restingRequirements": "Resting requirement (hrs)", "eatingTime": "Eating time (hrs)", "drinkingGroomingTime": "Drinking/grooming time (hrs)", "overCrowding": "Overcrowding (%)", "timePerMilking": "Time per milking (hrs)", "parlorTurnsPerHour": "Parlor turns per hour", "animalsMilkedPerHour": "Animals milked per hour", "totalTimeMilking": "Total time milking (hrs)", "walkingToFindStall": "Walking to find stall (hrs)", "totalNonRestingTime": "Total non-resting time (hrs)", "timeRemainingForResting": "Time remaining for resting (hrs)", "timeRequiredForResting": "Time required for resting (hrs)", "restingDifference": "Resting difference (hrs)", "potentialMilkLossGain": "Potential milk loss/gain", "energyChange": "Energy change (Mcals)", "bodyWeightChange": "Body weight change", "bodyConditionScoreChange": "Body condition score change (per 100 days)", "hours": "Hours", "timeRequired": "Time required", "timeRemaining": "Time remaining", "timeAvailableForResting": "Time available for resting", "lbs": "lbs", "potentialMilkDifference": "Potential milk difference", "penTimeBudget": "Pen time budget", "shareNoteBookEmailError": "Could not send email. Please try again later.", "syncAgain": "Sync again", "syncFailed": "Sync interrupted", "syncFailMsg": "Check your internet connection and sync again to avoid data corruption…", "top_goal_max": "Top goal max", "middle_goal_max": "Middle goal max", "bottom_goal_max": "Bottom goal max", "top_goal_min": "Top goal min", "middle_goal_min": "Middle goal min", "bottom_goal_min": "Bottom goal min", "bottom": "Bottom", "middle": "Middle", "syncAgainMsg": "Sync failed! Please sync again to continue data entry...", "createVisitError": "The selected Site does not have any pens. To continue with this visit, please create pen(s) for this site!", "milkFatYieldkg": "Milk fat yield (Kg)", "milkProteinYieldKg": "Milk protein yield (Kg)", "milkFatProteinYieldKg": "Milk fat + protein yield (Kg)", "milkSolidNonFatkg": "Milk solids non-fat (Kg)", "pickup": "Pickup", "mastitis": "Mastitis", "backPenAnalysis": "Back to pen analysis", "backToolListing": "Back to tools listing", "analysisSection": "Back to analysis selection", "nextStep": "Next step", "refreshTokenExpiry": "Your current session has timed out. Please login again to continue!", "unauthorizedUser": "The email or username does not exist. Please contact the system admin and try again! ", "cases": "Cases", "details": "Details", "metricTonsDM": "Metric Tons DM", "metricTonsAF": "<PERSON>ric <PERSON> A<PERSON>", "enterText": "Enter text", "topInfo": "For fresh pen <20% in the top screen is okay", "ensureEmailConfigured": "Please ensure your email is configured before sharing.", "toolHeaderWarningMessage": "Due to technical reasons, some tools aren’t functioning properly. Please resync your app and try again!", "unAvailable": "unavailable", "noTMRAdded": "No TMR added!", "addTMRForHerdData": "Please add TMR scores to see data here", "noTMRScores": "There are no TMR scores to show...", "refreshTokenExpiryRelogin": "Your current session has timed out. Do you want to login again to sync your data or logout? ", "analyzed": "Analyzed", "formulated": "Formulated", "improvements": "Improvements", "forageAudit": "Forage Audit", "maxSelectionLimitReached": "Maximum selection limit reached!", "imperialNELDairyUnit": "Mcal/lbs", "filterToolsText": "Tools", "Mcal": "M<PERSON>", "syncErrors": "Sync errors", "noSyncErrors": "No sync errors!", "inContactDetail": "in contact detail", "in": "In", "visit": "visit", "imageMaxSizeValidation": "Image size is too large. Maximum limit is 10Mb", "videoMaxSizeValidation": "Video size is too large. Maximum limit is 50Mb", "TMRParticleScore": "TMR penn state", "noPensAdded": "No pens added!", "addPensForHerdData": "Please add pens to see data here", "noPensToShow": "There are no pens scores to show...", "mediaLimitExceeds": "Media limit exceeds", "attachmentLimitExceed": "Attachment limit exceeded! ", "addedToTheNote": "files were not added to the note.", "visitNotSynced": "Visit is not synced", "USD": "United States of America ($ USD)", "Euro": "Euro Member Countries (€ EUR)", "GBP": "United Kingdom (GBP GBP)", "CAD": "Canada (CA$ CAD)", "DZD": "Algeria (DA DZD)", "ARS": "Argentina ($ ARS)", "AUD": "Australia ($ AUD)", "CNY": "China (CNY CNY)", "CZK": "Czech Republic (CZK CZK)", "GTQ": "Guatemala (Q GTQ)", "HNL": "Honduras (HNL HNL)", "HUF": "Hungary (Ft HUF)", "BRL": "Brazil (R$ BRL)", "INR": "India (INR INR)", "IDR": "Indonesia (Rp IDR)", "MYR": "Malaysia (MYR MYR)", "MXN": "Mexico (PESO MXN)", "NIO": "Nicaragua (NIO NIO)", "PEN": "Peru (S/. PEN)", "PHP": "Philippines ($ PHP)", "PLN": "Poland (zł PLN)", "PON": "Romania (lei PON)", "RUB": "Russia (₽ RUB)", "SAR": "Saudi Arabia (﷼ SAR)", "ZAR": "South Africa (ZAR ZAR)", "KRW": "South Korea (₩ KRW)", "SRD": "Surinam ($ SRD)", "CHF": "Switzerland (CHF CHF)", "TWD": "Taiwan (NT$ TWD)", "THB": "Thailand (THB THB)", "UAH": "Ukraine (UAH UAH)", "VEF": "Venezuela (Bs VEF)", "VND": "Vietnam (₫ VND)", "CLP": "Chile ($ CLP)", "Cargill": "Cargill", "Purina": "<PERSON><PERSON><PERSON>", "Provimi": "<PERSON><PERSON><PERSON>", "ProvimiUS": "Provimi US", "Imperial": "Imperial", "Metric": "Metric", "Screen": "Screen", "ScreenOld": "Screen Old", "ScreenNew": "Screen New", "Straw": "<PERSON><PERSON>", "Dryhay": "Dry Hay", "Haylage": "Haylage/Grass", "Corn": "Corn", "Other": "Other", "General": "General", "unsyncedVisitReportGenerationError": "Unsynced visit! Report can't be generated.", "Report.Cargill.Report": "Cargill - report", "Report.Visit.Report": "Visit report", "Report.Tool.Details": "Tool details", "Visit.Report.Footer.Patent": "Cargill incorporated, its parents and affiliates does not warrant the accuracy of these estimates, due to many factors. There is no guarantee of production or financial results. ©2024 cargill, incorporated. All rights reserved.", "Report.AvgRumenFillScore": "Avg. rumen fill score", "Report.ForagePennState": "Forage penn state", "Report.PercentageOnScreen": "On screen (%)", "Report.PenTimeBudgetTimeRemaining": "Time remaining", "Report.PenTimeBudgetTimeRequired": "Time required", "Report.PenTimeBudget.TimeAvailableForResting.Label": "Hours", "Report.PenTimeBudget.TimeAvailableForResting.CategoryLabel": "Time available for resting", "Report.Heatstress.Temperature.In.Celcius": "Temperature ℃", "Report.Heatstress.Temperature.In.Farenhiet": "Temperature ℉", "Report.Heatstress.Intake.Adjustment": "Intake adjustment", "Report.Heatstress.Estimated.Dry.Matter.Intake": "Estimated dry matter intake ({0})", "Report.Heatstress.Loss.Of.Energy.Consumed": "Loss of energy consumed (mcal)", "Report.Heatstress.Milk.Value.Loss.Perday": "Milk value loss (per day) ({0})", "Report.Heatstress.Dmi.Adjustment": "Dmi adjustment", "Report.Heatstress.Reduction.In.Dmi": "Reduction in dmi ({0})", "Report.Heatstress.Energy.Equivalent.Milk.Loss": "Energy equivalent milk loss ({0})", "Report.Heatstress.Milk.Value.Loss.PerMonth": "Milk value loss (per month) ({0})", "Report.Pentime.Budget.Hours": "Hours", "Report.Heatstress.TemperatureHumidityIndex": "Temperature humidity index", "Report.Heatstress.Legend": "Legend", "Report.Heatstress.Legends": "Legends", "Report.Heatstress.Stress.Threshold": "Stress threshold", "Report.Heatstress.Mild.Moderate.Stress": "Mild - moderate stress", "Report.Heatstress.Moderate.Severe.Stress": "Moderate - severe stress", "Report.Heatstress.Severe.Stress": "Severe stress", "Report.Heatstress.Mild.Moderate.Stress.Message": "Respiration exceeds 75 bpm | rectal temperature exceeds 39℃ (102.2℉)", "Report.Heatstress.Stress.Threshold.Message": "Respiration exceeds 60 bpm | repro losses detectable | rectal temperature exceeds 38.5℃ (101.3℉)", "Report.Heatstress.Moderate.Severe.Stress.Message": "Respiration exceeds 85 bpm | rectal temperature exceeds 40℃ (104℉)", "Report.Heatstress.Severe.Stress.Message": "Respiration exceeds 120-140 bpm | rectal temperature exceeds 41℃ (106℉)", "Report.RumenHealthManureScreening.Top": "Top", "Report.RumenHealthManureScreening.Middle": "Middle", "Report.RumenHealthManureScreening.Bottom": "Bottom", "Report.RumenHealthManureScreening.TopGoalMin": "Top goal min", "Report.RumenHealthManureScreening.TopGoalMax": "Top goal max", "Report.RumenHealthManureScreening.MiddleGoalMin": "Middle goal min", "Report.RumenHealthManureScreening.MiddleGoalMax": "Middle goal max", "Report.RumenHealthManureScreening.BottomGoalMin": "Bottom goal min", "Report.RumenHealthManureScreening.BottomGoalMax": "Bottom goal max", "Report.Heatstress.Temperature": "Temperature", "Report.Heatstress.Relative.Humidity=": "Relative humidity (%)", "Thirdparty": "Thirdparty", "Consumer": "Consumer", "Competitor": "Competitor", "Report.Chewing": "Chewing", "Report.Not.Chewing": "Not chewing", "Report.Animal.Analysis": "Animal analysis", "Report.General.Comments": "General comments", "Lactation": "Lactation", "FreshCow": "Fresh cow", "DryCow": "Dry cow", "TransitionCow": "Transition cow", "HalfPointScale": "Half Point Scale", "QuarterPointScale": "Quarter Point Scale", "Topic": "Topic", "On": "On", "ScreenTwo": "Screen", "markRead": "<PERSON> read", "YourVisit": "Your visit", "HasBeenAutoPublished": "has been auto published!", "YourAction": "Your action", "DueByTomorrow": "is due by tomorrow,", "DueByToday": "is due by today,", "ClickToSeeDetails": "Click to see more details!", "warning": "Warning", "forageMgmtCornSilage": "Forage management - Corn silage", "forageMgmtHaylage": "Forage management - Haylage", "forageMgmtOther": "Forage management - Other silage", "BunkersAndPileCornSilage": "Bunkers and piles - Corn silage", "BunkersAndPileHaylage": "Bunkers and piles - Haylage", "BunkersAndPileOther": "Bunkers and piles - Other silage", "SilageBagsCornSilage": "Silage bags - Corn silage", "SilageBagsHaylage": "Silage bags - Haylage", "SilageBagsOther": "Silage bags - Other silage", "BaleageCornSilage": "Baleage - Corn silage", "BaleageHaylage": "Baleage - Haylage", "BaleageOther": "Baleage - Other silage", "currentAppVersion": "Current app version", "logsUploadedSuccessfully": "Logs uploaded successfully", "logsGenerationError": "Unable to generate export logs", "top_19mm": "Top", "mid1_18mm": "Mid 1", "mid2_4mm": "Mid 2", "selectAll": "Select all", "unSelectAll": "Unselect all", "forageAuditManualSelection": "remaining. Forage Audit and <PERSON><PERSON> scorecard requires manual selection to proceed", "view": "View", "max12Questions": "Select at most 12 questions from below to proceed", "createdBy": "Created by", "used": "Used", "unused": "Unused", "robotic_milk_evaluation_cows_per_robot_redRange": "<50 or >70", "robotic_milk_evaluation_cows_per_robot_greenRange": "55 to 62", "robotic_milk_evaluation_cows_per_robot_yellowRange": "50 to 55 or 62 to 70", "robotic_milk_evaluation_robot_free_time_redRange": "<10 or >20", "robotic_milk_evaluation_robot_free_time_greenRange": "12 to 18", "robotic_milk_evaluation_robot_free_time_yellowRange": "10 to 12 or 18 to 20", "robotic_milk_evaluation_milkings_redRange": "<2.5", "robotic_milk_evaluation_milkings_greenRange": ">2.7", "robotic_milk_evaluation_milkings_yellowRange": "2.5 to 2.7", "robotic_milk_evaluation_milking_refusals_redRange": "<0.8 or >1.6", "robotic_milk_evaluation_milking_refusals_greenRange": "1.1 to 1.3", "robotic_milk_evaluation_milking_refusals_yellowRange": "0.8 to 1.1 or 1.3 to 1.6", "robotic_milk_evaluation_milking_failures_redRange": ">6", "robotic_milk_evaluation_milking_failures_greenRange": "<3", "robotic_milk_evaluation_milking_failures_yellowRange": "3 to 6", "robotic_milk_evaluation_average_box_time_redRange": ">8", "robotic_milk_evaluation_average_box_time_greenRange": "<6", "robotic_milk_evaluation_average_box_time_yellowRange": "6 to 8", "robotic_milk_evaluation_average_concentrated_fed_redRange": "<3 or >8", "robotic_milk_evaluation_average_concentrated_fed_greenRange": "4 to 6", "robotic_milk_evaluation_average_concentrated_fed_yellowRange": "3 to 4 or 6 to 8", "robotic_milk_evaluation_concentrate_per_100_kg_milk_redRange": ">14", "robotic_milk_evaluation_concentrate_per_100_kg_milk_greenRange": "<12", "robotic_milk_evaluation_concentrate_per_100_kg_milk_yellowRange": "12 to 14", "attachmentDetail": "Attachment should not be more than the above limits", "lessThan10MB": "(each photo < 10MB)", "lessThan50MB": "(each video < 50MB)", "lessThanEqualTo2": "<=2.0", "greaterThanEqualTo4": ">=4.0", "generateReport": "Generate report", "cargillReport": "Cargill - report", "visitDate": "Visit date", "dietInputsSiteLactating": "Diet inputs, site (lactating animals)", "penDetails": "Pen details", "visitReportFooterPatent": "Cargill incorporated, its parents and affiliates does not warrant the accuracy of these estimates, due to many factors. There is no guarantee of production or financial results. ©2025 cargill, incorporated. All rights reserved.", "averageChews": "Average chews", "numberOfChews": "Number of chews", "page": "Page", "of": "of", "cudChewingPen%": "<PERSON><PERSON> Chewing (% of Pen)", "accounts": "Accounts", "emptyAccountTitle": "No Account found!", "stagesOfLactation": "Stages of Lactation", "animalInformation": "Animal information", "avgBCSCalculated": "Avg BCS (Calculated)", "percentOfPen": "Percent of Pen (%)", "ManureScorePercentPerPen": "Manure Score (% per Pen)", "avgManureScoreCalculated": "Avg manure score (calculated)", "avgChewsCud": "Avg Chews/Cud", "particleScorePercentOnScreen": "Particle Score (% on Screen)", "goalTopPercent": "Goal Top (%)", "goalMidPercent": "Goal Mid (%)", "goalBottomPercent": "Goal Bottom (%)", "goalMid1Percent": "Goal Mid 1 (%)", "goalMid2Percent": "Goal Mid 2 (%)", "goalTrayPercent": "Goal Tray (%)", "min|max": "Min | Max", "locomotionPercentPerPen": "Locomotion (% per Pen)", "locomotionNoInPen": "Locomotion (No. in Pen)", "locomotionPercentPerHerd": "Locomotion (% per Herd)", "locomotionNumberInHerd": "Locomotion (No. in Herd)", "milkLossKgPerDay": "Milk Loss (Kg/day)", "milkLossKgPerYear": "Milk Loss (Kg/year)", "revenueLossKgPerDay": "Revenue Loss ($/day)", "revenueLossKgPerYear": "Revenue Loss ($/year)", "visitComparison": "Visit Comparison", "currentVisitTMRComparison": "Current Visit - TMR Comparison", "currentVisitPSPSComparison": "Current Visit - PSPS Comparison", "milkYields": "Milk yield", "dietInputLactatingAnimals": "Diet inputs (lactating animals)", "animalsPerPen": "Animals per pen", "generalComments": "General comments", "day": "day", "year": "year", "updateAvailable": "New version of the application is available", "forceUpdateAvailable": "New version of the application is available, and is mandatory to update.", "uploadingVisitReport": "Uploading visit reports! Hold on", "mbs": "mbs", "deleteNoteMessage": "Are you sure you want to delete this note?", "delete": "Delete", "creator": "Creator", "noteCharacterLimitForVisitReport": "Character limit reached 250!", "charactersAllowedInReport": "Only 250 character will be included in the visit report from this note", "editTrail": "Edit trail", "addTrail": "Add trail", "at": "at", "unsavedNotes": "Unsaved notes", "discardNotes": "Are you sure you want to discard your unsaved notes?", "discard": "Discard", "1": "1", "2": "2", "maxAttachmentsLimit": "Attachment should not be more than 500mbs", "profitabilityAnalysis": "Profitability Analysis", "totalMilkingCows": "Total number of milk cows(milking, PP, dry)", "totalLactatingAnimals": "Total number of lactating animals", "breed": "Breed", "productionSystem": "Production system", "milkingNumber": "Number of milkings", "milkInformation": "Milk information", "productionIn150Dim": "Production in 150 DIM (cow)", "totalProductionHerd": "Total production herd", "totalProduction": "Total production", "updateSiteSetup": "Update site setup", "feedingInformation": "Feeding information", "selectCreator": "Select creator", "creatorSelected": "Creator selected", "mineralBaseMix": "Mineral base mix", "commercialConcentrate": "Commercial concentrate", "nutritek": "Nutritek", "xpcUltra": "XPC ultra", "actiforBoost": "Actifor boost", "buffer": "<PERSON><PERSON><PERSON>", "nutrigorduraLac": "Nutrigordura lac", "ice": "ICE", "energyIce": "Energy ICE", "monensin": "<PERSON><PERSON><PERSON>", "soyPassBR": "Soy pass BR", "concentrateTotalConsumed": "Concentrate total consumed", "hay": "Hay", "waterQuality": "Water quality", "beddingQuality": "Bedding quality", "ventilation": "Ventilation", "sprinkler": "Sprinkler", "airRU": "Air RU %", "THI": "Temperature humidity index (THI)", "respiratoryMovement": "Respiratory movement", "cowLayingDown": "% Cow laying down", "totalDietCost": "Total diet cost", "revenuePerCowPerDay": "Revenue per cow per day", "milkLitersPerKgConcentrate": "Milk liters/ kg concentrate", "feedConcentrate": "Feed concentrate", "IOFC": "IOFC", "errorCreatingNote": "Error creating note.", "addGeneralNotes": "Add general notes", "attachMoreNotesForThisVisit": "Attach more notes for this visit", "selectNotes": "Select notes", "addNotes": "Add notes", "generalNotes": "General notes", "feedingCostPerLiterMilk": "Feeding cost per liter of milk", "commentLimitExceeds": "Comment limit exceeds", "totalNumberOfCows": "Total number of cows", "penSelected": "pen(s) selected", "selectPens": "Select pens", "downloadingNotebookMedia": "Syncing notebook media!", "comfortAndWellBeing": "Comfort and well being", "historical": "Historical", "updateRequired": "Update Required", "updateRequiredDescription": "A new build is ready with updates! Install now.", "buildReadyDescription": "A new build is ready with updates! Go to the Menu and download it now for the latest features and improvements", "installUpdate": "Install Update", "restorePreviousVisitSilo": "<PERSON><PERSON> last visit silos", "noSiloFound": "No any silo found in last visit", "UserCreated": "User Created", "DDW": "Farm Data", "shareLogsFile": "Share logs file", "pleaseFindAttachedLogsFile": "Please find the attached logs file.", "surveyCategories": "Survey categories", "colostrum": "Colostrum", "preWeaned": "Pre-weaned", "postWeaned": "Post-weaned", "CalfHeiferColostrum": "Colostrum", "CalfHeiferPreweaned": "Pre-weaned", "CalfHeiferPostweaned": "Post-weaned", "CalfHeiferGrowerPuberty": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Pregnancy, Close-up", "CalfHeiferKeyBenchmarks": "Key Benchmarks", "Colostrum_CleanAndDryCalvingArea": "Clean and dry calving area", "Colostrum_RefrigeratedColostrumStoredLess": "Refrigerated colostrum stored less than 24 hours", "Colostrum_NumberOfCowsInCalvingArea": "Number of cows in calving area", "Colostrum_PasteurizeColostrumBeforeFeeding": "Pasteurized colostrum is fed", "Colostrum_PercentageOfNavelsDippedInSevenPercent": "% of navels dipped in 7% iodine in 1 hour", "Colostrum_BrixPercentOfColostrumFed": "Brix % of colostrum fed", "Colostrum_HoursTillCalfReceivesColostrum": "Hours till calf receives colostrum", "Colostrum_AmountOfColostrumOrFed": "Amount of colostrum or fed", "Colostrum_HoursTillCalfIsRemovedFromMother": "Hours till calf is removed from mother", "Colostrum_CleanAndSanitizeCalfFeedingEquipment": "Clean and sanitize calf feeding equipment between feedings", "Colostrum_CleanCalfCartToTransportCalf": "Clean calf cart to transport calf", "Preweaned_CleanAndDryPen": "Clean and dry pen", "Preweaned_SizeOfPenAdequatePerHeifer": "Size of pen is adequate per heifer", "Preweaned_WellVentilatedPenWithNoDraftOnCalf": "Well ventilated pen with no draft on calf", "Preweaned_ForageAvailability": "Forage availability", "Preweaned_CleanAndSanitizeCalfFeedingEquipment": "Properly clean and sanitize calf feeding equipment between feedings", "Preweaned_CMRIsProperlyMixedAndAdequatelyFed": "CMR is properly mixed and adequately fed", "Preweaned_ConsistentFeedingTimesAndProtocols": "Consistent feeding times and protocols", "Preweaned_FreeChoiceCleanWaterIsAvailable": "Free choice, clean water is available", "Preweaned_FreeChoiceFreshCalfStarterIsAvailable": "Free choice, fresh calf starter is available", "Preweaned_WeaningAtIntakeOfOnekgStarterPerDay": "Weaning at intake of 1kg starter per day", "Preweaned_EvidenceOfScoursOrPneumonia": "Evidence of scours or pneumonia", "GrowerPubertyPregnancyCloseup_CleanAndDryPen": "Clean and dry pen", "Postweaned_SizeOfPenAdequate": "Size of pen is adequate per heifer", "Postweaned_SizeOfBunkSpace": "Size of bunk space adequate per calf", "Postweaned_FreshQualityStarterAvailable": "Fresh, quality starter/grower is available", "Postweaned_FeedBunkIsCleanedDaily": "Feed bunk is cleaned daily, refusals removed", "Postweaned_EvidenceOfAcidosisInManure": "Evidence of acidosis in manure", "GrowerPubertyPregnancyCloseup_PercentageOfOverCrowding": "% of overcrowding", "GrowerPubertyPregnancyCloseup_SizeOfBunkSpace": "Size of bunk space is adequate per heifer", "GrowerPubertyPregnancyCloseup_GroupWithUniformHeiferSize": "Groups with uniform heifer size", "GrowerPubertyPregnancyCloseup_EvidenceOfLooseManure": "Evidence of loose manure", "GrowerPubertyPregnancyCloseup_RationsBalanceForGrowth": "Rations balanced for growth targets, reviewed often", "GrowerPubertyPregnancyCloseup_FeedBunkIsCleanedDaily": "Feed bunk is cleaned daily, refusals removed", "GrowerPubertyPregnancyCloseup_DesiredBCSIsAchieved": "Desired BCS is achieved for stage of maturity", "KeyBenchmarks_SerumlgG": "Serum IgG (g/L) at 48 hours", "KeyBenchmarks_NintyDaysMorbidity": "90 days morbidity", "KeyBenchmarks_NintyDaysMortality": "90 days mortality", "KeyBenchmarks_FifteenPercentOfMatureBodyWeight": "15% of mature body weight at 90 days", "KeyBenchmarks_FiftyFivePercentOfMatureBodyWeight": "55% of mature body weight at pregnancy", "KeyBenchmarks_NintyFourPercentOfMatureBodyWeight": "94% of mature body weight before calving", "KeyBenchmarks_PercentOfHeifersPregnant": "Percent of heifers pregnant at 15 months", "KeyBenchmarks_AgeInMonthAtFirstCalving": "Age in months at first calving", "KeyBenchmarks_HeiferPeakProduce": "Heifer peak milk as a % of herd average", "KeyBenchmarks_CalvingAndHeiferRecord": "Calving and heifer record keeping system used", "twoToFive": "2-5", "greaterThanFive": ">5", "hundredPercent": "100%", "fiftyToHundredPercent": "50-100%", "lessThanFiftyPercent": "<50%", "greaterThanTwentyTwoPercent": ">22%", "twentyToTwentyOnePercent": "20-21%", "lessThen20OrNotTested": "<20% or do not test", "lessThanOne": "<1", "twoToThree": "2-3", "threeToFive": "3-5", "greaterThanThreeL": ">3L", "twoToThreeL": "2-3L", "lessThanTwoL": "<2L", "oneToSix": "1-6", "greaterThanSix": ">6", "textureFeed": "Texture feed", "lessThan15Percent": "<15%", "greaterThan15Percent": ">15%", "between11To30Percent": "11-30%", "greaterThan30Percent": ">30%", "greaterThan10": ">10", "between8To10": "8-10", "lessThan10": "<10", "lessThan5Percent": "<5%", "between5To10Percent": "5-10%", "lessThan11Percent": "<11%", "between11To13Percent": "11-13%", "between14To16Percent": "14-16%", "between16To17Percent": "16-17%", "greaterThan17Percent": ">17%", "lessThan51Percent": "<51%", "between51To53Percent": "51-53%", "between54To55Percent": "54-55%", "between56To57Percent": "56-57%", "greaterThan57Percent": ">57%", "lessThan90Percent": "<90%", "between91To93Percent": "91-93%", "between94To95Percent": "94-95%", "between96To97Percent": "96-97%", "greaterThan97Percent": ">97%", "lessThan60Percent": "<60%", "between60To75Percent": "60-75%", "greaterThan75Percent": ">75%", "lessThan24M": "<24m", "between24To26M": "24-26m", "greaterThan26M": ">26m", "between70To75Percent": "70-75%", "between60To70Percent": "60-70%", "pasteurizedMilkFed": "Pasturized whole milk fed", "overallCalfHeiferScore": "Overall Calf & Heifer score", "phase": "Phase", "notesComments": "Notes / Comments", "comments": "Comments", "grower": "Grower", "puberty": "Puberty", "pregnancy": "Pregnancy", "closeUp": "Close-up", "viewComment": "View comment", "merge": "<PERSON><PERSON>", "permanentDelete": "Delete permanently", "confirmDeletePen": "Are you sure you want to delete this pen permanently", "toolsUsingPen": "Tools using pen", "followingVisitUsingPen": "Following visits are using this pen", "rumenHealth": "Rumen health cud chewing", "bodyCondition": "Body condition score", "rumenHealthManureScore": "Rumen health manure score", "penTimeBudgetTool": "Pen time budget", "manureScreener": "Rumen health manure screener", "filterResult": "Filter Result:", "invalidName": "Invalid name", "tmr": "TMR", "individualCow": "Individual cow", "herdProfile": "Herd profile", "feeding": "Feeding", "numberOfTmrGroups": "Number of TMR Goups", "milkProductionOutputs": "Milk production outputs", "coolAid": "Cool aid", "fortissaFit": "Fortissa fit", "milkingPerDay": "Milking/day", "enterValue": "Enter value", "homeGrownForages": "Home grown forages", "addHomeGrownForages": "Add home grown forages", "homeGrownGrains": "Home grown grains", "addHomeGrownGrains": "Add home grown grains", "purchaseBulkFeed": "Purchase bulk feed", "addPurchaseBulkFeed": "Add purchase bulk feed", "purchaseBagsFeed": "Purchase bags feed", "addPurchaseBagsFeed": "Add purchase bags feed", "forageName": "Forage name", "grainName": "Grain name", "feedName": "Feed name", "incentiveDays": "Incentive day(s)", "totalQuota": "Total quota", "perDay": "/day", "butterfat": "<PERSON><PERSON><PERSON><PERSON>", "protein": "<PERSON><PERSON>", "lactoseAndOtherSolids": "Lactose and other solids", "class2Protein": "Class 2 protein", "class2LactoseAndOtherSolids": "Class 2 lactose and other solids", "deductions": "Deductions", "totalHerdPerDay": "Total/herd/day (As Fed(kg))", "totalDryMatter": "Total kg of dry matter", "pricePerTon": "Price per ton ($)", "ratioSNFPerButterfat": "Ratio S.N.F./Butterfat", "maxAllowed": "Max allowed", "totalFatProtein": "Total kg (fat+protien)/cow/day", "dairyEfficiency": "Dairy efficiency (Energy corrected milk)", "totalRevenuePerLiter": "Total revenue per litre", "feedCostPerLiter": "Feed cost per litre", "purchasedFeedCostPerLiter": "Purchased feed cost per litre", "concentrateCostPerLiter": "Concentrate cost per litre", "concentrateCostPerKgBF": "Concentrate cost per kg BF", "bfRevenue": "BF revenue ($/cow/day)", "proteinRevenue": "Protein revenue ($/cow/day)", "otherSolidsRevenue": "Other solids revenue ($/cow/day)", "deductionsPricePerCowPerDay": "Deductions ($/cow/day)", "snfNonPayment": "SNF nonpayment ($/cow/day)", "totalRevenuePricePerKgFat": "Total revenue ($/kg fat)", "totalRevenueCowDay": "Total revenue ($/cow/day)", "underQuotaLostRevenuePerMonth": "Under quota lost revenue per month", "rofPerKgButterFat": "ROF per kg Butterfat", "rof": "ROF (Return on Feed)", "ReturnOverFeed": "Return over feed", "herdBaseline": "Herd baseline", "quota": "<PERSON><PERSON><PERSON>", "kgOfQuota": "kg of quota (kg/day)", "noOfCowsToFillQuota": "No. of cows to fill quota", "noOfCowsToFill50Kg": "No. of cows to fill 50 kg", "averageMilkProductionLitresPerCowPerDay": "Average milk production (litres/cow/day)", "subtotal": "Subtotal", "totalRevenuePricePerKgButterFat": "Revenue/kg of butterfat ($/kg of BF)", "feedCosts": "Feed costs", "forageFeedCostPerCowPerDay": "Forage feed cost ($/cow/day)", "grainsCostPerCowPerDay": "On farm grains cost ($/cow/day)", "totalOnFarmFeedCostPerCowPerDay": "Total on farm feed cost ($/cow/day)", "purchasedBulkFeedPerCowPerDay": "Purchased bulk feed ($/cow/day)", "purchasedBagsFeedPerCowPerDay": "Purchased bags feed ($/cow/day)", "totalPurchasedCostPerCowPerDay": "Total purchased cost ($/cow/day)", "totalFeedCostPerCowPerDay": "Total feed cost ($/cow/day)", "totalConcentrateCostPerCowPerDay": "Total concentrate cost ($/cow/day)", "feedCostPerKgOfBF": "Feed cost/kg of BF ($/kg of BF)", "feedCostPerLitreOfMilk": "Feed cost/litre of milk ($/litre)", "foragePercentage": "% forage", "currentReturnOverFeedCosts": "Current return over feed costs", "previousReturnOverFeedCosts": "Previous return over feed costs", "returnOverFeedCostPerCowPerDay": "Return over feed ($/cow/day)", "returnOverFeedCostPerKgOfBF": "Return over feed ($/kg of BF)", "returnOverFeedCostPerLitre": "Return over feed ($/litre)", "diabledInfoText": "Report is currently unavailable for this site. Please check back later or contact support for more information.", "selected": "Selected", "pricePerKg": "Price ($/kg)", "pricePerHl": "Price (kg/hl)", "pricePerKgPerCow": "Price (kg/cow)", "kgOfQuotaPerDay": "kg of quota (kg/day)", "incentiveDaysKgPerDay": "Incentive Day(s) (kg/day)", "totalQuotaKgPerDay": "Total Quota (kg/day)", "currentQuotaUtilizationKgPerDay": "Current Quota Utilization (kg/day)"}