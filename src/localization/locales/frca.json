{"cancel": "Annuler", "CloseUpHeifer": "Génisses en préparation", "optimal": "Réponses optimales", "emailValidationError": "Format de l'adresse électronique incorrecte", "HNL": "Honduras (HNL HNL)", "milkSolidNonFat": "Solides non-gras", "asFedIntake": "Consommation TQS", "heatStressSheet": "Feuille de stress thermique", "rumenFillScore": "Santé Ruminale - score de rumen", "score": "Score", "summaryReport": "Rapport Sommaire", "poor": "<PERSON><PERSON><PERSON>", "businessAddress": "<PERSON><PERSON><PERSON> ferme", "timeRemainingForResting": "Temps restant pour le repos (Heures)", "moreThan20": "> 20", "$": "$", "animalInputPen": "Vache type, groupe", "noAccountFound": "Aucun compte trouvé!", "%": "%", "&": "&", "addImage": "Ajouter une photo", "newNotes": "Nouvelle note", "daysInMilk": "Jours en lait (JEL)", "penIsRequired": "L'enclos est requis", "analysis": "Analyse", "addPickup": "Ajouter une collecte", "at3InchesDayOrAt7CmPerDay": "À 3 pouces par jour ou à 7 cm par jour", "BCS": "État corporel", "moreThan13Percent": "> 13%", "exposure": "Exposition", "brandPlaceholder": "Logo Corporatif", "backPenAnalysis": "Retour à l'analyse de l'enclos", ":": ":", "Report.Heatstress.Relative.Humidity=": "Humidité relavite (%)", "unused": "inutilis<PERSON>", "componentEfficiency": "Efficacité des composantes", "robotFreeTimeDeLaval": "Temps en mode veille (%)", "top_19mm": "<PERSON><PERSON>", "feedEfficiency": "Efficacité alimentaire", "animalsInPen": "Nombre d'animaux dans l'enclos", "attachments": "Pièces jointes", "Mcal": "M<PERSON>", "siteInputsSelected": "Entrées de sites sélectionnés", "animalPerPen": "Animaux par groupe", "milkLossKgPerDay": "<PERSON><PERSON> de <PERSON> (kg/jour)", "weekly": "Hebdomadaire", "penPercent": "% du groupe", "averageConcentrateFedGEA": "Moyenne de concentrés servis (kg/vache)", "result": "Résultats", "YourAction": "Votre action", "shareNoteBookEmailError": "Impossible d'envoyer un courriel Veuillez réessayer plus tard.", "BaleageCornSilage": "<PERSON>e en<PERSON> - Ensilage de maïs", "LowForage": "Faible en fourrage", "milkingFrequency": "<PERSON><PERSON><PERSON>", "pileSilageAFDensityGoal": "Objectif > 705", "jpeg": "JPEG", "Report.RumenHealthManureScreening.Middle": "Milieu", "fromSiteSetup": "À partir de la configuration du site", "lessThan40PercentDM": "< 40% MS", "close": "<PERSON><PERSON><PERSON>", "Report.PenTimeBudgetTimeRemaining": "Temps restant", "numberOfChews": "Nombre de mastications", "address": "<PERSON><PERSON><PERSON>", "manureScreening": "<PERSON><PERSON> fumier ", "selectState/province/region": "Sélectionner un État/Province/Région", "robotic_milk_evaluation_cows_per_robot_redRange": "<50 ou >71", "averageMilkYieldOther": "Production Moyen (kg/vache)", "dataLossMessage": "Les changements ne seront pas sauvegardés. Êtes-vous sûr de vouloir continuer?", "DIM": "Jours en lait", "temperatureHumidityIndex": "Indice d'humidité de la température", "totalTimeMilking": "Temps total de la traite (Heures)", "earlyLactation": "Début lact.", "overCrowding": "Surpeuplement (%)", "searchSegment": "Chercher un segment", "days": "Jours", "Imperial": "Impérial", "milkingRefusalsOther": "Refus (#/vache)", "PenTimeBudgetTool": "Outil pour évaluer le temps de repos", "reductionInDMIWeightInkg": "Réduction de la MSI (kg)", "restFeedDeLaval": "% concentrés non consommés hier", "severeStress": "Stress sévère", "notes": "<PERSON><PERSON><PERSON>", "goalMinPercent": "Objectif % (min)", "attachMoreNotes": "Jo<PERSON>re plus de notes pour cet outil", "good": "<PERSON>", "milkProductionKg": "Production de lait", "selectAnimal": "Sélectionner un animal", "silageBagsQuestion1": "Les ag-bags sont placés sur une surface stable et bien gérés toute la saison?", "emailAddress": "Adresse électronique", "lessThan20Percent": "< 20%", "currentCostPerCow": "Coût / vache", "lessThan15cm": "Moins de 60 cm par jour", "at3InchesDay": "À 3 pouces par jour", "12InchesOrMore": "12 pouces ou plus", "silageBagsQuestion7": "Quel est le score de porosité, en rapport a à MS?", "notChewing": "Ne rumine pas", "silageBagsQuestion6": "La façade est-elle propre et bien gérée sans aucune indication de chauffage et de pertes d'entreposage?", "silageBagsQuestion5": "Vitesse d'avancement quotidien de la façade?", "RevenueLossYear": "<PERSON><PERSON> de revenu", "silageBagsQuestion4": "Les ag-bags sont contrôlés régulièrement et les trous sont réparés?", "forageManagement": "Gestion des fourrages", "silageBagsQuestion3": "Une couverture de sécurité est-elle utilisée? (SB3)", "silageBagsQuestion2": "Les déchets, la végétation et les rongeurs sont maitrisés autour des ag-bags?", "add": "Ajouter un inventaire", "top(19mm)": "Haut (19mm) (g)", "imageMaxSizeValidation": "La taille de l'image est trop grande. La limite maximale est de 5 Mo", "animalAll": "Animal (tout)", "lactationStages": "Stades de lactation", "farmProducer": "Producteur", "oktaLogin": "Connexion avec Okta", "searchType": "Chercher un type", "daysInTank": "Jours dans le réservoir", "searchSite": "Trouver un site", "siteDescription": "Description du Site", "markRead": "Marquer comme lu", "densityConverterSecondColumnHeader": "Kg/m^4", "averageMilkYieldGEA": "Production Moyen (kg/vache)", "metritis": "Métrite", "success": "Su<PERSON>ès", "nextStep": "L'étape suivante", "syncErrors": "Erreur de synchronisation", "totalCost": "Coût total", "toolsSelected": "Outils s<PERSON>lectionn<PERSON>", "totalMilkingFailuresGeaInfo": "Cal<PERSON>r les refus ; Nbre moy. de visites/vache - Nbre moy. traites/vache", "Lactation": "Lactation", "restFeedLely": "Reste concentrés (%)", "lbsDMIn1Foot": "Lbs matière sèche sur 1 pied", "lactatingAnimals": "Vaches en lactation", "replacementCowCost": "Coût de remplacement", "remove": "<PERSON><PERSON><PERSON><PERSON>", "emptyList": "La liste est vide", "GoalMIN": "Objectif <PERSON>", "cornSilage": "Ensilage de <PERSON>", "averageMilkProtein": "Protéine du lait moyen (%)", "bunkerPilesQuestion1": "Quelle est la propreté de la zone d'alimentation?", "lessThan1Hour": "Moins de 1 heure", "bunkerPilesQuestion3": "Quelle est la compaction, en respect à la MS?", "HUF": "Hongrie (Ft HUF)", "bunkerPilesQuestion2": "Emballage: Les couches sont-elles 15 cm (6 pouces) ou moins?", "bunkerPilesQuestion5": "Les pneus/divisions se touchent-ils?", "bunkerPilesQuestion4": "La pente du bunker est-elle inférieure à un rapport de 3: 1 ?", "NELDairyUnit": "Mcal/Kg", "bunkerPilesQuestion7": "Les bunkers/piles sont-ils recouverts de 2 couches de plastique et une couche de plastique non perméable?", "bunkerPilesQuestion6": "Les murs latéraux sont-ils scellés avec du plastique?", "createVisitError": "Le site sélectionné n'a pas d'enclos. Pour continuer cette visite, veuillez créer des enclos pour ce site!", "refreshTokenExpiryRelogin": "Votre session actuelle a expiré. Voulez-vous vous connecter à nouveau pour synchroniser vos données ou vous déconnecter?", "6To12Hours": "6 à 12 heures", "bunkerPilesQuestion9": "L'ensilage est-il exempt de signes visibles de contamination du sol?", "bunkerPilesQuestion8": "Couvert immédiatement après la fin du compactage?", "AAEfficiency": "Efficacité des AA", "Lactating": "Lactation", "mastitis": "Mammites ", "robotic_milk_evaluation_average_box_time_redRange": ">9", "noContact": "Aucun contact", "stressThresholdText": "La respiration dépasse 60 bpm | Pertes de repro détectables | La température rectale dépasse 38,5 ° C (101,3 ° F)", "dryMatterIntake(Kg)": "Consommation de matière sèche (kg)", "noPensToShow": "Il n'y  apas de scores d'enclos à afficher…", "errorDownloadingFile": "Erreur lors du téléchargement du fichier", "siteSetup": "Configurer le site", "towerSilosQuestion2": "Le temps de remplissage par silo est-il 3 jours ou moins?", "towerSilosQuestion1": "Le silo est-il couvert pendant un mois après le remplissage s'il n'est pas utilisé immédiatement?", "Metric": "Métrique", "concentratePer100KGMilkGEA": "Conc./ 100kg de lait (kg/100kg de lait)", "editPickup": "Modifier une collecte", "newSite": "Nouveau site", "robotic_milk_evaluation_milking_failures_redRange": ">7", "enterText": "Entrez du texte", "moderateSevereStress": "<PERSON><PERSON> modé<PERSON> - <PERSON><PERSON>", "milkSolidIn(kg)": "Lait vendu (kg)", "towerSilosQuestion3": "Le niveau de reprise est-il supérieur à 10 cm (4 pouces) par jour?", "penNameExist": "Le nom de l'enclos existe déà", "feedOut": "Reprise", "primaryContactInformation": "Coordonnées principales", "baleageQuestion2": "Les déchets, la végétation et les rongeurs sont-ils contrôlés autour des balles?", "baleageQuestion1": "Les balles sont placées sur une surface stable et bien gérée toute la saison?", "baleageQuestion4": "L'eau ne pénètre pas dans les balles ou le silo", "pile": "<PERSON>as", "baleageQuestion3": "Les balles sont-elles inspectées pour les dommages causés par les parts de ravageurs et réparées sur une base hebdomadaire?", "ago": "il y a", "baleageQuestion5": "Est-ce que les balles sont enrubannées avec:", "animalsObserved": "Nombre d'animaux évalués", "syncFailMsg": "Vérifiez à nouveau votre connexion Internet pour éviter la corruption des données…", "milkUrea": "Urée du lait (mg/dL)", "number": "Nombre", "100KGMilkLaval": "Le système affiche 1 kg/lait donc x 100", "currentAppVersion": "Version actuelle de l'application", "top(g)": "Haut (g)", "postal/zipCode": "Code Postal/Zip", "to1": "à 1.0", "pen": "Groupe", "robotsInHerd": "Nombre de robots", "at6InchesDay": "À 6 pouces par jour", "treatmentCost": "Coût du traitement", "toolName": "Nom d'outil", "totalAnimalsInPen": "Nombre d'animaux dans l'enclos", "totalNonRestingTime": "Temps total dans repos (Heures)", "treatmentDefault": "Coût de traitement", "totalMilkingFailuresGEA": "Nb incomplètes (total)", "feetUnit": "ft.", "Report.Heatstress.Intake.Adjustment": "Ajustement de la consommation", "unitOfMeasure": "Unité de mesure", "loginWithOkta": "Connexion avec Okta", "primaryContactFirstName": "Prénom contact ", "penAll": "<PERSON><PERSON><PERSON> (tout)", "Report.Cargill.Report": "Rapport Cargill", "confirmation": "Confirmation", "penSetup": "Configuration du groupe", "restingRequirements": "Temps de repos (Heures)", "mildModerateStress": "<PERSON><PERSON> léger - modé<PERSON>", "walkingTimeToParlor": "Temps de marche jusqu'au salon (Heures)", "Report.RumenHealthManureScreening.MiddleGoalMax": "Object<PERSON> min", "animalInputSite": "Site d'entrée des animaux", "selectMonth": "Sélectionnez le mois", "milkingFailuresGeaInfo": "<PERSON>bre moyen de visites/vache - <PERSON><PERSON> moyen de traites/vache", "BCSAvg": "<PERSON>", "costOfExtraDaysOpen": "Coût par jour ouvert additionnel", "rationCostPerAnimal($)": "Coût d'alimentation/vache ($)", "FreshHeifer": "Génisse fraî<PERSON> vêlée", "robotic_milk_evaluation_robot_free_time_redRange": "<10 ou >21", "middle_goal_max": "Objectif intermédiaire max", "manureScore": "Score de fumier", "other": "Autres ", "starSign": "*", "city": "Ville ", "locomotion": "Locomotion", "save": "<PERSON><PERSON><PERSON><PERSON>", "topLength": "Longeur en haut", "bunkerSlopeGoal": "Objectif > 3.5 à 1.0", "login": "Connexion", "createProspect": "<PERSON><PERSON><PERSON> un prospect", "lastSynced": "Dernière synchronisation", "remainingInSilo": "(Restant dans le silo)", "top": "<PERSON><PERSON>", "lateLactation": "Fin lact.", "bottom_goal_min": "Objectif inférieur min", "somaticCellCountWithUnit": "Cellules somatiques (1,000 cellules/mL)", "share": "Partager", "recentVisit": "Visites récentes", "bodyConditionScoreChange": "Changement de score de l'état corporel (pour 100 jours)", "setReminder": "Définir un rappel", "noOfChewsPerCud": "Nombre de mastications", "topUnloadingSilo": "Silo à déchargement par le haut", "question": "Question", "USD": "États-Unis d'Amérique ($ USD)", "unsyncedVisitReportGenerationError": "Visite non-synchronisée! Le rapport ne peut pas être généré", "Report.RumenHealthManureScreening.TopGoalMax": "Objectif intermédiaire max", "timeRequired": "Temps requis", "cudChewingPercent": "Mastication de bolus (%)", "towerSilos": "Silo tour", "milkingFailuresLavalInfo": "Système statistique : refus totaux / nombre de vaches", "dmDensity": "Densité de l'ensilage MS", "outputs": "Résultats", "earTag": "Étiquette d'oreille", "cowsPerRobot": "Vaches par robot", "deleteVisitAlertDesc": "La visite sera supprimée définitivement", "businessName": "Nom de l'entreprise ", "cow": "<PERSON><PERSON>", "walkingToFindStall": "Temps de marche pour retourner à la stalle (Heures)", "noTMRScores": "Il n'y a pas de scores de RTM à montrer ...", "milkProductionInKg": "Production de lait (kg)", "addPensForHerdData": "Veuillez ajouter des enclos pour voir les données ici", "noEarTagsMessage": "On dirait que c'est la première fois que vous ajoutez des étiquettes d'oreille. Commencez à taper dans la case ci-dessus pour commencer !", "BaleageHaylage": "<PERSON>e en<PERSON> - Ensilage de foin", "number_of_stalls": "Nombre de logettes", "newTagInfoText": "Tapez pour créer une nouvelle étiquette", "lessThan6Inches": "Moins de 6 pouces", "indicateRequiredField": "Indique un champ obligatoire", "all": "<PERSON>ut", "locomotionScore": "Score de locomotion", "top_goal_max": "Object<PERSON> max", "Report.Heatstress.Moderate.Severe.Stress": "<PERSON>ress modé<PERSON> - sévère", "Untitled": "0", "NELDairy(Kg)": "NEL dairy", "milkSolidNonFatkg": "Solides non-gras du lait (kg)", "phoneNumberPlaceholder": "Inscrire le numéro de téléphone ici", "MUN(mg/dL)": "Urée du lait (mg/dL)", "businessNameExist": "Le nom de l'entreprise existe déjà", "otherSilage": "Autre ensilage", "unitOfMeasureDescription": "Les options suivantes peuvent être modifiées plus tard dans les paramètres de l'application", "NA": "NA", "lessThan5": "< 5%", "milkLossKgPerYear": "<PERSON><PERSON> de <PERSON> (kg/année)", "lessThan3": "< 3: 1", "tonsPerDay": "Tonnes par jour", "monthly": "<PERSON><PERSON><PERSON>", "NELDairyIn(Kg)": "NEL dairy (Mcal/kg)", "Heifer": "<PERSON><PERSON><PERSON>", "lbs": "lbs", "locomotionNoInPen": "Locomotion (Nombre dans le groupe)", "averageBoxTimeGEA": "Temps de box moyen (min/vache)", "performanceAndTreatmentCostsContent": "Données de références utilisées pour calculer l'impact économique de chaque incidence métabolique", "Report.RumenHealthManureScreening.MiddleGoalMin": "Objectif inférieur max", "animalsObs": "Nombre d'animaux évalués", "componentYield": "Rendement des composantes", "selectCity": "Selectionner une ville", "inProgress": "En cours", "SRD": "Suriname ($ SRD)", "avgChewsPerCud": "Moyenne de mastication par bolus", "milkLossKGYear": "<PERSON><PERSON>", "silageBags": "Ag-bags", "meters": "mètres", "CalfandHeifer": "Veaux et génisses", "totalMilkingFailuresDeLaval": "Nb incomplètes (total)", "animalsMilkedPerHour": "Nombre d'animaux traient par heure", "dairyFarmConsultant": "Conseiller en alimentation", "today": "<PERSON><PERSON><PERSON><PERSON>", "searchAnimal": "Trouver un animal", "herdGoal": "Objectif pour le troupeau", "Topic": "Sujet", "milkOtherSolids": "Autres solides du Lait", "selectCategory": "Sélectionner une catégorie", "totalFreshCowsEvaluation": "Nombre de vaches fraîches évaluées", "Report.RumenHealthManureScreening.BottomGoalMin": "<PERSON><PERSON><PERSON> m<PERSON> min", "italian": "Italien", "fetchingSyncData": "Récupération et synchronisation des données...", "updateSite": "Mettre à jour le site", "korean": "<PERSON><PERSON><PERSON>", "lessThanFourDays": "Moins de 4 jours", "formulated": "Formulé", "selectTool": "Sélectionner l'outil", "visitReport": "Rapport de visite", "actionCapital": "Action", "needHelpSigningIn": "Besoin d'aide pour vous connectez?", "bacteriaCellCount": "Comptage Bactérien (1,000cfu/mL)", "milkValueLossPerMonth": "<PERSON><PERSON> de valeur de lait (par mois)", "selectVisit": "Sélectionnez la visite", "BRL": "Brésil (R$ BRL)", "milkHdDay": "Lait/tête/jour", "animalInPen": "Animaux dans le groupe", "On": "<PERSON><PERSON>", "uploadFromGallery": "Parcourir la galerie", "CREATE": "<PERSON><PERSON><PERSON>", "emptyCustomerDescription": "Essayez d’actualiser votre écran ou contactez l’administrateur pour obtenir de l’aide...", "legend": "Légende", "Male": "<PERSON><PERSON><PERSON>", "addNew": "Ajouter un nouveau", "potentialMilkDifference": "Diff<PERSON><PERSON><PERSON> potent<PERSON> de <PERSON>", "ZAR": "Afrique du Sud (ZAR ZAR)", "noSitesFoundDescription": "Essayez d’en ajouter un nouveau à l’aide du bouton Créer un site...", "deleteInventoryMessage": "Voulez-vous vraiment supprimer cet article d'inventaire ?", "Report.Heatstress.Mild.Moderate.Stress.Message": "La respiration dépasse 75 bpm | La température rectale dépasse 39°C (102.2°F)", "milkingsOther": "Traites par vache", "ReadyToMilk": "Ready 2 Milk", "noteBookTrailInfo": "La suppression de la piste limitera la recherche au titre et à la date uniquement. Une fois supprimé, ne peut pas être restauré", "password": "Mot de passe", "Consumer": "Consommateurs", "dietInputPen": "Sommaire de la ration, groupe", "lactatingAnimal": "Animaux en lactation", "annual": "<PERSON><PERSON>", "english": "<PERSON><PERSON><PERSON>", "cowsToBeFed": "Nombre d'animaux alimentés", "densityConverter": "Conversion, densité", "PileAndBunker": "Inventaire des fourrages", "revenueLossKgPerDay": "Perte de revenu ($/jour)", "stalls": "Stalles", "deletePickupMessage": "Êtes-vous sûr que vous voulez supprimer cette livraison?", "bottom_goal_max": "Objectif inférieur max", "Productivity": "Productivité", "percentOfPen": "Pourcentage du groupe (%)", "intakeAdjustmentPercent": "Ajustement de la consommation", "milkingSpeedGEA": "Vitesse de traite (kg/min)", "milkingSpeedLely": "Vitesse de traite (kg/min)", "newPen": "Nouveau groupe", "middle(g)": "Milieu (g)", "bunkerPilesQuestion13": "La façade est-elle lisse? (Aucune indication de couches perturbées permettant la pénétration d'oxygène?", "cmPerDay": "cm par jour", "at6InchesPerDay": "à 6 pouces par jour", "completed": "Complété", "bunkerPilesQuestion10": "Les aliments lâches ou «face» sont nourris à l'intérieur", "bunkerPilesQuestion11": "Vitesse d'avancement quotidien de la façade?", "bunkerPilesQuestion12": "À quelle fréquence le plastique de couverture est-il retiré de l'ensilage?", "inchesCmPerDay": "Pouces ou cm par jour", "lessThan12Inches": "Moins de 6 pouces", "compareVisits": "Comparer les visites", "selectGraph": "Sélectionnez un graphique", "dietInputsSiteLactating": "Données de ration, Site (animaux en lactation)", "inchesPerDay": "Pouces par jour", "milkProduction": "Production de lait", "difference": "<PERSON>ff<PERSON><PERSON><PERSON>", "successfullyCreated": "C<PERSON>é avec succès!", "moreThan12Hours": "Plus de 12 heures", "animalsPerPen": "Animaux par groupe", "passwordPlaceholder": "Inscrire le mot de passe ici", "syncAgain": "Synchroniser à nouveau", "siteName": "Nom du Site", "pens": "Groupes", "lessThan1Percent": "< 1%", "metabolicDisorderCostPerCow": "Maladies métaboliques, coût/vache", "Visit.Report.Footer.Patent": "Cargill Incorporated, ses sociétés mères ou affliliées ne jusitifient pas l'exactitude de ces estimations, en raison de nombreux facteurs. Il n'y a pas de garantie de production ou de résultats financiers. ©2023 Cargill, Incorporated. Tous les droit sont réservés", "averageBoxTimeLely": "Temps de box (min/vache)", "energyEquivalentMilkLossWeightInkg": "<PERSON>te de lait équivalente à l'énergie (kg)", "selectDiet": "Sélectionnez la ration", "Report.Heatstress.Dmi.Adjustment": "Ajustement de la MSI", "currency": "<PERSON><PERSON>", "haylage": "Ensilage de foin", "onScreen(%)": "À l'écran (%)", "Report.Heatstress.Temperature": "Température", "ClickToSeeDetails": "Cliquez pour voir plus de détails!", "editNote": "Modifiez la note", "portugese": "Portugais", "drinkingGroomingTime": "Temps d'abreuvement/toilettage (Heures)", "primaryContactPhone": "Téléphone du contact principal", "at15cmPerDay": "À 15 cm par jour", "mid1(18mm)": "Milieu 1 (8mm) (g)", "silageAFDensity": "Densité TQS d'ensilage", "DZD": "Algérie (DA DZD)", "middle_goal_min": "<PERSON><PERSON><PERSON> m<PERSON> min", "Report.RumenHealthManureScreening.TopGoalMin": "Object<PERSON> max", "polish": "Polonais", "milkLoss": "<PERSON><PERSON>", "Other": "Autres ", "averageMilkYieldLely": "Lait/vache/jour (kg/vache)", "milkFat(%)": "Gras du lait (%)", "MilkLoss": "<PERSON><PERSON>", "robotic_milk_evaluation_average_box_time_yellowRange": "7 a 8", "rationCostPerAnimal": "Coût d'alimentation/vache", "dryMatter": "<PERSON><PERSON>", "milkingRefusalsGEA": "Refus (#/vache)", "MilkUrea(mg/dL)": "Urée du lait (mg/dL)", "top_goal_min": "Object<PERSON> min", "view": "Voir", "avgChewsCud": "Mastications moyennes/bolus", "continue": "<PERSON><PERSON><PERSON>", "IDR": "Indonésie (Rp IDR)", "segment": "Segment", "concentrateDistribution": "Distribution des concentrés", "removedOnly": "Seulement enlevés", "results": "Résultats", "animalsSelected": "Animaux sélectionnés", "lessThan6layersPlastic": "Moins de 6 couches", "increasedDaysOpen": "Augmentation des jours ouverts", "namePlaceholder": "Nom", "Report.PercentageOnScreen": "Quantité sur le tamis (%)", "searchCity": "Chercher une ville", "herdLevelInfo": "<PERSON><PERSON><PERSON> du <PERSON>", "robotic_milk_evaluation_milking_refusals_redRange": "<0.8 ou >1.7", "selectAll": "<PERSON><PERSON>", "dmiReductionPercent": "Ajustement de la MSI", "avgRumenFillScore": "<PERSON><PERSON><PERSON><PERSON><PERSON> du rumen, score moyen", "page": "Page", "next": "Suivant", "mid1_18mm": "Milieu 1 ", "oktaEmailPlaceholder": "Inscrire l'adresse électronique ici", "foragePennState": "Pennstate Fourrages", "prospects": "Prospects", "Report.RumenHealthManureScreening.Bottom": "Bas", "searchTool": "Rechercher l'outil", "selectCountry": "Selectionner un pays", "attachmentDetail": "L'attachement ne doit pas dépasser 100 Mo", "totalFreshCowsPerYear": "Nombre de vêlage / année", "goalMin%": "Objectif - max (%)", "basicInformation": "Informations de base", "avg": "<PERSON><PERSON><PERSON>", "metabolicIncidenceCasesContent": "Entrez le nombre de vaches fraîches et le nombre de maladies métaboliques pendant la période d'évaluation. Cela sera converti en coût d'incidence annuel sur l'onglet résultats", "GBP": "Royaume-Uni (GBP GBP)", "chewsPerCud": "Mastications par bolus", "chewsPerRegurgitation": "Nombre de mastications par régurgitation", "mid2": "Milieu 2 (4mm)", "filterToolsText": "Outils", "mid1": "Milieu 1 (8mm)", "selectAnyFollowing": "Sélectionnez l'un des éléments suivants", "footSquare": "m", "emptyCustomerTitle": "Aucun client trouvé!", "hours": "<PERSON><PERSON>", "Report.Pentime.Budget.Hours": "<PERSON><PERSON>", "lactatingCows": "Vaches en lactation", "yes": "O<PERSON>", "goalMidPercent": "Objectif milieu (%)", "milkYields": "Rendement en lait", "animalAnalysis": "Analyse de l'animal", "concentratePer100KGMilkLely": "Conc./ 100kg de lait (kg/100kg de lait)", "midLactation": "Mi-lact.", "robotic_milk_evaluation_milking_refusals_greenRange": "1.1 a 1.4", "prospectDetails": "Détails du client potentiel", "contactDetail": "<PERSON><PERSON><PERSON> du contact", "restFeedGEA": "Aliments permis non consommés (%)  ⃰", "backToolListing": "Retour à la liste des outils", "tmrParticleScoreToastMessage": "Un maximum de 10 Penn State peuvent être ajoutés", "Report.Heatstress.Estimated.Dry.Matter.Intake": "Apport estimé de matière sèche ({0})", "moderateSevereStressText": "La respiration dépasse 85 bpm | La température rectale dépasse 40 ° C (104 ° F)", "tray": "<PERSON><PERSON>", "viewOverAllScore": "Voir le score global", "animalObserved": "Animaux observés", "haylageQuestion10": "Quels sont les niveaux d'acide butyrique?", "averageChews": "Mastic<PERSON> moyennes", "rumenHealth": "Santé Ruminale - rumination", "onboardingTitle5": "Rapports intelligents", "onboardingTitle4": "Historique consolidé", "onboardingTitle3": "<PERSON><PERSON><PERSON> de note intelligent", "onboardingTitle2": "Groupes et outils simultanés", "Report.AvgRumenFillScore": "Moyenne des résultats du score de remplissage du rumen (calculée)", "onboardingTitle1": "Processus plus rapides", "Report.Heatstress.Temperature.In.Farenhiet": "Température °F", "bunkerName": "Nom", "totalMilkingFailuresLely": "Échec de traite (total)", "required": "Requis", "lastUpdated": "Dernière mise à jour:", "logout": "Déconnexion", "fieldsAreEmpty": "Les champs obligatoires sont vides", "addTMRForHerdData": "Veuillez ajouter des scores de RTM pour voir les données ici", "ManureScreener": "Santé ruminale - <PERSON><PERSON> à fumier", "ScreenOld": "Tamis - ancien", "penDetails": "Détails du groupe", "case": "Cas", "dashboard": "Tableau de bord", "noneSelected": "Aucun s<PERSON>", "cowsDayNeeded": "Nombre de vaches nécessaires pour assurer avancement silo", "successfullyUpdated": "Mise à jour réussie!", "dietInputSite": "Données alimentaires, site (animaux en lactation)", "forageMgmtHaylage": "Gestion du fourrage - Ensilage de foin", "densityConverterFirstColumnHeader": "lb/ft^4", "russian": "<PERSON><PERSON>", "noFavoritesFoundDescription": "Essayez de marquer quelques outils fréquents comme favoris pour voir les résultats…", "moreThan5": "> 5%", "MUN(mg/dL)_Milk Urea(mg/dL)": "Urée du lait (mg/dL)", "restFeedGeaInfo": "Pour l'onglet Analyser : calculer les aliments permis non consommés (kg) / total programmé", "moreThan3": "> 3: 1", "Euro": "Pays membres de l'UE (€ EUR)", "noSyncErrors": "Aucune erreur de synchronisation!", "slope": "Pente", "energyChange": "Changement d'énergie (Mcals)", "feedEfficiencyRatio": "Efficacité alimentaire (% MSI)", "noAccountFoundDescription": "Nous ne trouvons pas de compte correspondant à votre recherche.", "Report.Heatstress.Stress.Threshold": "<PERSON><PERSON>", "dystocia": "<PERSON><PERSON><PERSON><PERSON>", "reset": "Réinitialiser", "upcomingActions": "Actions à venir", "siteDetails": "Détails de site", "avgManureScoreCalculated": "Score de fumier moyen (calculé)", "searchCountry": "Chercher un pays", "siteNameExist": "Le nom du site existe déjà", "restingDifference": "<PERSON><PERSON><PERSON><PERSON><PERSON> de repo<PERSON> (Heures)", "bodyConditionScoreScale": "<PERSON><PERSON><PERSON>, CC", "cowEfficiency": "Efficacité des vaches", "moreThan8layersPlastic": "Plus de 8 couches de plastique", "somaticCellCount": "Comptage de cellules somatiques", "forgotPassword": "Mot de passe oublié", "timePerMilking": "Temps par traite (Heures)", "Report.Heatstress.TemperatureHumidityIndex": "Indice d'humidité de la température", "Milking": "Lactation", "pinnedContacts": "Contacts <PERSON>", "older": "Plus vieux", "categories": "Catégories", "dataInput": "Entr<PERSON> de donn<PERSON>", "mid2(4mm)": "Milieu 2 (4mm) (g)", "forageMgmtCornSilage": "Gestion du fourrage - Ensilage de maïs", "animalInformation": "Information sur les animaux", "locomotionNumberInHerd": "Locomotion (Nombre par troupeau)", "somaticCellCountMilkUrea": "Comptage cellulaire et urée du lait", "more": "Pour en savoir plus", "robotic_milk_evaluation_milkings_greenRange": ">2.8", "daysOpen": "Augmentation des jours ouverts", "at15CmPerDay": "À 15 cm par jour", "milkFever": "<PERSON><PERSON><PERSON> de lait", "addInventories": "Ajouter un inventaire", "temperature(C)": "Température (° C)", "silageNameExist": "Le nom d'ensilage existe déjà", "appSettings": "Paramètres", "logsGenerationError": "Impossible de générer les visites d'exportations", "VEF": "Vénézuela (Bs VEF)", "robotic_milk_evaluation_concentrate_per_100_kg_milk_greenRange": "<13", "accounts": "<PERSON><PERSON><PERSON>", "deleteAnimalMessage": "Voulez-vous vraiment supprimer cet animal ?", "incidence": "Incidence", "editCustomer": "Éditer client", "goalMid2Percent": "Objectif milieu 2 (%)", "moreThan30cm": "Plus de 90 cm par jour", "used": "utilisé", "2xWeek": "2x par semaine", "averageBoxTimeOther": "Temps de box moyen (min/vache)", "averageBacteriaCellCount": "Comptage bactérien moyen (1,000 cfu/mL)", "dimUpdateValidation": "Cette valeur sera également modifiée dans la configuration du site/enclos et/ou d'autres outils. Êtes-vous certain(e) de vouloir continuer?", "noActionText": "Essayez d'ajouter une nouvelle note en tant qu'action à partir du carnet de notes et elle commencera à apparaître ici pour vous rappeler vos tâches importantes...", "analysisSection": "Retour à la sélection d'analyse", "visitDate": "Date de visite", "allNotes": "Toutes les notes", "UrinePHTool": "Outil pH urinaire", "concentratePer100KGMilkOther": "Conc./ 100kg de lait (kg/100kg de lait)", "tmrScore": "RTM", "CAD": "Canada (CA$ CAD)", "6To12Inches": "6 à 12 pouces", "Cargill": "Cargill", "milkFat": "Gras du lait  ", "totalStallsInParlor": "Nombre de places dans la salle de traite", "topInfo": "Pour l'enclos vaches fraîches : <20% sur le plateau supérieur est correct", "history": "Historique", "searchPen": "Trouver un groupe", "done": "<PERSON><PERSON><PERSON><PERSON>", "viewAll": "Visualiser Tous", "addComments": "Ajoutez des commentaires", "plusSign": "+", "robotic_milk_evaluation_cows_per_robot_yellowRange": "51 a 55 ou 62 a 70", "logoutConfirm": "Êtes-vous sûr de vouloir vous déconnecter?", "milkingsGEA": "Nb moy. de traites/vache", "cudChews": "Mastic<PERSON> de bolus", "moreThan8Hours": "Supérieur à 8 heures", "timeAvailableForResting": "Temps disponible pour le repos", "addedToTheNote": "Les fichiers n'ont pas été ajoutés à la note.", "tray(g)": "<PERSON><PERSON> (g)", "temperature(F)": "Température (° F)", "selectPen": "Sélectionnez une ration pour le nouveau groupe", "stagesOfLactation": "Stade de lactation", "noToolsInProgress": "Aucun outil en cours", "silageCreatedSuccess": "Ensilage créé avec succès", "potentialMilkLossGain": "Perte / gain de lait potentiel", "energyEquivalentMilkLossWeightInLbs": "Perte de lait équivalente à l'énergie (lb)", "INR": "Inde (INR INR)", "baleage": "<PERSON><PERSON>", "percent": "Pourcentage", "general": "Général", "KRW": "<PERSON><PERSON>e du Sud (₩ KRW)", "maximumConcentrate": "Maximum de concentrés (kg/vache)", "diet": "Ration", "animalInHerd": "Animaux dans le troupeau", "customerDetails": "Détail du client", "heightOfSilageLeftInSilo": "Hauteur de l'ensilage restant dans le silo", "avgConcentrate": "Concentrés moyens (Kg/vache)", "noOfChews": "Nombre de mastications", "milkProductionHeatStress": "Production de lait (kg)", "Corn": "<PERSON><PERSON><PERSON>", "fromPenSetup": "Organisation par groupe", "greaterThanSevenDays": "Plus que 7 jours", "Report.Animal.Analysis": "Analyse animale", "noResourcesAvailable": "Ressource non disponible.", "avatorName": "Nom du compte", "Report.Heatstress.Stress.Threshold.Message": "La respiration dépasse 60 bpm | Pertes de repro détectables | La température rectale dépasse 38,5°C (101.3°F)", "exportLogsAlertText": "Journaux de périphériques téléchargés avec succès", "detailedReport": "Rapport Détaillé", "Pasture": "Pâturage", "visitHistory": "Historique des visites", "DueByTomorrow": "est due pour demain,", "totalMilkingFailuresLavalInfo": "Nombre de traites X % incomplètes", "visit": "Visite", "GoalMAX": "Objectif MA<PERSON>", "HalfPointScale": "<PERSON><PERSON><PERSON> de demi-point (0.5)", "noSitesFound": "Aucun site trouvé!", "RoboticMilkEvaluation": "Évaluation robot de traite", "customer": "Client", "MXN": "Mexique (PESO MXN)", "penName": "Nom du groupe", "shareVia": "Partager via", "bag": "Ag-Bag", "videos": "Vid<PERSON><PERSON>", "visitNotSynced": "La visite n'est pas synchronisée", "milkUreaMeasure": "Urée du lait (mg/dL)", "searchToolByName": "Trouver un outil par son nom…", "averageBoxTimeDeLaval": "Durée moyenne de production par traite (min/vache)", "robot": "Robot", "totalAnimals": "Total des animaux", "averageMilkProduction": "Production laitière moyenne, kg", "twoNumberPlaceholder": "0", "NELDairy": "NEL Lait", "BCSByStageOfLactation": "État corporel par stade de lactation", "Report.Heatstress.Severe.Stress.Message": "La respiration dépasse 120-140 bpm | La température rectale dépasse 41°C (106°F)", "bunker": "Bunker", "rumenFill": "Remplissage du rumen", "milkPriceKG": "Prix du lait ($/kg)", "MYR": "Malaisie (MYR MYR)", "milk": "Production laitière", "goalCudChewingPercent": "Objectif % de mastication de bolus", "dateOfVisit": "Date de visite", "pileName": "Nom de l'amas", "noNotification": "il n'y a pas de notifications dans l'application", "bottomUnloadingSilo": "Silo à déchargement par le bas", "dairyEnteligen": "<PERSON><PERSON>", "otherNonRestTime": "Autre temps hors repos (Heures)", "average": "<PERSON><PERSON><PERSON>", "updatePen": "Mettre à jour le groupe", "compare": "Comparer", "milkingRefusalsDeLaval": "Refus (#/vache)", "downloadedSuccessfully": "Téléchargement réussi", "ketosis": "Acétonomie", "goalTopPercent": "Objectif top (%)", "lactationRange6": "121 à 200", "lactationRange7": "plus de 201", "noTMRAdded": "Pas de RTM ajouté!", "visitComparison": "Comparaison de visite", "lactationRange8": "", "lactationRange9": "", "lactationRange2": "-21 à -1", "lactationRange3": "0 à 15", "lactationRange4": "16 à 60", "lactationRange5": "61 à 120", "CloseUp": "<PERSON><PERSON><PERSON>", "animalClass": "Classe animale", "lactationRange1": "moins de -21", "THB": "Thaïlande (THB THB)", "search": "<PERSON><PERSON><PERSON>", "logsUploadedSuccessfully": "Visites téléchargées avec succès", "20To40Percent": "20-40%", "locomotionPercentPerPen": "Locomotion (% par groupe)", "customers": "Clients", "updateTool": "Mettre à jour l'outil", "asFedIntake(Kg)": "Consommation TQS (kg)", "dryMatterIntake": "Consommation de matière sèche", "averageSomaticCellCount": "Cellules somatiques moyennes (1000 cellules/ml)", "selectToolToSwitch": "Sélectionnez n'importe quel outil pour changer", "milkFatYieldkg": "Rendement en gras du lait (kg)", "timeRequiredForResting": "Temps requis pour le repos (Heures)", "toolsUsed": "Outils utilisés", "notMeasured": "Non mesuré", "sitePenValueChange": "La valeur sera également modifiée dans la configuration du site/enclos et/ou d'autres outils", "lbsDMinFoot": "lbs MS dans 1 pied ou kg MS dans 1m", "silageUpdatedSuccess": "Ensilage mis à jour avec succès", "CHF": "Suisse (CHF CHF)", "pileDimension": "Dimension de l'amas", "syncAgainMsg": "Le Sync a échoué! Veuillez vous synchroniser à nouveau pour poursuivre la saisie des données ...", "topWidth": "Largeur en haut", "15To30cm": "15 à 30 cm (6 à 12 pouces par jour)", "sites": "Sites", "robotic_milk_evaluation_average_box_time_greenRange": "<7", "VND": "Vietnam (₫ VND)", "selectDateRange": "Sélectionner la plage de dates", "cargillLogin": "Connectez-vous avec <PERSON>gill", "chinese": "<PERSON><PERSON>", "semiAnnual": "Semi-annuel", "alreadyUsedEarTagMsg": "Cette étiquette est déjà utilisée dans la visite en cours.", "earTagPlaceholder": "Ta<PERSON>z pour rechercher ou ajouter une nouvelle étiquette d'oreille...", "warning": "Avertissement", "1xWeek": "1x par semaine", "forageQualityRationQuestion3": "Le mélange de RTM est-il froid au toucher?", "forageQualityRationQuestion2": "Est-ce que le mélange de la RTM a une odeur agréable quand vous le sentez?", "displacedAbomasum": "<PERSON><PERSON><PERSON>", "forageQualityRationQuestion4": "Les refus sont-ils retirés et mesurés quotidiennement?", "syncFailed": "La synchronisation n'a pas pu être complétée en ce moment, r<PERSON><PERSON><PERSON>.", "robotic_milk_evaluation_cows_per_robot_greenRange": "56 a 62", "minimumConcentrate": "Minimum de concentrés servis (kg/vache)", "generateReport": "Générer un rapport ", "searchByVisitName": "Rechercher par nom de visite", "at7CmDay": "À 7 cm par jour", "graph": "Graphique", "HasBeenAutoPublished": "a été publié automatique!", "Report.Heatstress.Milk.Value.Loss.PerMonth": "<PERSON><PERSON> de valeur de lait (par mois) ({0})", "lessThan50MB": "chaque vidéo < 50MB", "meterUnit": "m", "Report.Chewing": "Animaux qui ruminent - Rumination", "currentMilkPrice": "Prix actuel du lait", "cornSilageQuestion11": "Quelle est la longueur de coupe de l'ensilage de maïs? (score de particule OptiLac)", "cornSilageQuestion10": "La longueur de coupe lors du chantier d'ensilage a-t-elle été contrôlée avec le penn state?", "ScreenTwo": "É<PERSON>ran", "cornSilageQuestion15": "Quels sont les niveaux de cendres?", "cargillReport": "Rapport Cargill", "cornSilageQuestion14": "Des additifs d'ensilage, des inoculants ou des stabilisateurs aérobies sont-ils utilisés? (H7)", "estimatedDryMatterIntakeWeightInkg": "Consommation estimée de la matière sèche (kg)", "milkProductionDim": "Production et jour en lait", "cornSilageQuestion13": "Quel est le score de dureté du grain dans l'ensilage de maïs?", "cornSilageQuestion12": "Combien de grains entiers y a-t-il dans 1 L (32 onces) ?", "rationCostPerAnimalWithCurrency($)": "Coût d'alimentation/vache", "cornSilageQuestion16": "Quel est le ratio d'acide Lactique : acide Acétique?", "herdLevelInformation": "<PERSON><PERSON>", "selectCustomerProspect": "Sélectionner un Client / Prospect", "RumenFill": "Remplissage du rumen", "alert": "<PERSON><PERSON><PERSON>", "housingSystem": "Type de logement", "averageMilkYield": "Gras du lait", "lactationRange10": "", "bodyWeightChange": "Changement de poids vif", "inputPlaceholder": "Entrer du texte ici", "lactationRange11": "", "addPSPS": "Ajouter un penn state", "forageQualityRationQuestion1": "Un stabilisant/inhibiteur de moisissure est-il utilisé dans la RTM par temps chaud / humide?", "Nutrition": "Nutrition", "deleteVisitAlertTitle": "Supprimer la visite", "length": "<PERSON><PERSON>", "enterTagNumber": "Entrer le numéro d'étiquette", "chewing": "Rumine", "currentMilkPriceHeatStress": "Prix du lait ($/kg)", "markAllRead": "Tout marquer comme lu", "home": "Accueil", "milkPerRobot": "Lait par robot (Kg)", "locomotionScoreAnalysis": "Analyse du score de locomotion", "markedAsFavoriteSuccess": "<PERSON>qué comme favori avec succès", "10To13Percent": "10-13%", "publish": "Publier", "feedingSystem": "Système d'alimentation", "PEN": "Groupe", "fresh": "Fra<PERSON><PERSON>", "addAnimal": "Ajouter un animal", "Fresh": "Fra<PERSON><PERSON>", "scoreStageLactation": "Score par stade de lactation", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "GTQ": "<PERSON><PERSON><PERSON>mal<PERSON> (Q GTQ)", "CLP": "Chili ($ CLP)", "answers": "Réponses", "inventoryList": "Liste d'inventaires", "farOffDry": "Taries", "feedOutSurfaceArea": "Surface alimentée", "LocomotionScore": "Score de locomotion", "invalidNumber": "Numéro invalide", "action": "Action", "state/province/region": "État/Province/Région", "tmrParticleScore": "Pennstate RTM", "animals": "<PERSON><PERSON><PERSON>", "cowFlowDesign": "Type de stabulation", "french": "Français", "silageDMDensity": "Densité MS d'ensilage", "inContactDetail": "Dans les coordonnées", "metabolicIncidenceCases": "Cas de maladies métaboliques", "1To6Hours": "1 à 6 heures", "BaleageOther": "<PERSON>e en<PERSON> - Autre ensilage", "in": "<PERSON><PERSON>", "Report.Heatstress.Severe.Stress": "Stress sévère", "tomorrow": "<PERSON><PERSON><PERSON>", "forageQualityRation": "Qualité des fourrages dans la ration", "requiredNoteField": "Remarque requise", "Bull": "<PERSON><PERSON><PERSON>", "forageMgmtOther": "Gestion du fourrage - Autre ensilage", "milkKg": "Lait (Kg)", "robotic_milk_evaluation_concentrate_per_100_kg_milk_yellowRange": "13 a 14", "appSetting": "Paramètres", "somaticCellCountUnit": "1,000 cellules/mL", "1To3Percent": "1 à 3%", "cudChewing": "Mastic<PERSON> de bolus", "hello": "Bonjour", "milkPrice": "Prix du lait", "Report.Heatstress.Legends": "Légendes", "visitReportFooterPatent": "Cargill incorporated, ses sociétés mères et affiliées ne garantissent pas l'exactitude de ces estimations, en raison de nombreux facteurs. Il n'y a aucune garantie de production ou de résultats financiers. ©2025 cargill, incorporated. Tous droits réservés.", "removeTool": "Supprimer l'outil", "timeRemaining": "Temps restant pour le repos", "skip": "Passer", "quarterly": "<PERSON><PERSON><PERSON><PERSON>", "BunkersAndPileCornSilage": "Bunkers et piles - Ensilage de maïs", "CNY": "Chine (CNY CNY)", "revenueLossKgPerYear": "Perte de revenu ($/année)", "parlorTurnsPerHour": "Nombre de tour de salon par heure", "PHP": "Philippines ($ PHP)", "locomotionPercentPerHerd": "Locomotion (% par troupeau)", "enterName": "Entrez le nom", "kg": "kg", "referenceTable": "Tableau de référence", "bottomLength": "Longueur en bas", "concentratePer100KGMilkDeLaval": "Ratio Concentrés/Lait", "emptyProspectTitle": "Aucun prospect trouvé!", "bottomWidth": "Largeur en bas", "max": "MAX", "Report.Tool.Details": "Dé<PERSON> de l'outil", "bottomUnloadingSiloName": "Nom du silo", "BunkersAndPileOther": "Bunkers et piles - Autre ensilage", "FreshCow": "<PERSON><PERSON>", "favourite": "<PERSON><PERSON><PERSON>", "menu": "<PERSON><PERSON>", "metricTonsDM": "Tonnes métriques MS", "prospect": "Prospects", "dryMatterPercent": "MS %", "toolHeaderWarningMessage": "Pour des raisons techniques, certains outils ne fonctionnent pas correctement. Veuillez resynchroniser votre application et réessayez!", "robotic_milk_evaluation_robot_free_time_greenRange": "13 a 18", "logoutConfirmation": "Êtes-vous sûr de vouloir vous déconnecter?", "Steer": "<PERSON><PERSON><PERSON>", "closeUpDry": "<PERSON><PERSON><PERSON>", "robotic_milk_evaluation_concentrate_per_100_kg_milk_redRange": ">15", "peakMilk": "Pic lact.", "addNewCow": "Ajouter une vache", "analysisSelection": "Sélection d'analyse", "improvements": "Améliorations", "milkYield": "Production de lait", "feet": "pieds", "MilkSoldEvaluation": "Évaluation du lait vendu", "notRemoved": "Non enlevés", "visitNamePlaceholder": "Entrer le nom de la visite...", "usd": "USD", "selectScale": "Sélectionnez l'échelle", "Dry": "<PERSON><PERSON>", "chooseLoginOptions": "Choisir une option de connection", "siteSelectorInfoText": "Seulement un site peut être sélectionné", "photos": "Photos", "milkFatMilkProtein": "Gras du lait (%) et Protéine du lait (%)", "download": "Télécharger", "revenue": "<PERSON><PERSON><PERSON>", "particleScorePercentOnScreen": "Score de particules (% plateau)", "SilageBagsHaylage": "Ag-bags - Ensilage de foin", "tryAddingNewOne": "Essayez d'en ajouter un nouveau", "feedoutRateInformation": "Consommation", "milkingsPerRobot": "Traites par robot", "moreThan3Percent": "> 3%", "cudChewingGoals": "Objectifs pour la mastication de bolus", "mediaLimitExceeds": "La limite des médias dépasse", "noFavoritesFound": "Aucun favori trouvé !", "requiredTitleField": "Le titre est requis", "stressThreshold": "<PERSON><PERSON>", "reductionInDMIWeightInLbs": "Réduction de la MSI (LBS)", "somaticCellCountMilkMun": "Cellules somatiques et Urée du lait", "robotFreeTimeLely": "Temps Libre (%)", "moreThan40PercentDM": "> 40% MS", "topUnloadingSiloDimension": "Dimensions", "SilageBagsCornSilage": "Ag-bags - Ensilage de maïs", "primaryContactLastName": "Nom contact ", "downloadVia": "Télécharger via", "milkingsDeLaval": "Moyenne de traite par animal (#traite/animal)", "creationDate": "Date de création", "steps": "Pas", "accountsSelected": "Comptes sélectionnés", "numberPlaceholder": "0", "createNewProspect": "C<PERSON>er un nouveau prospect", "cudChewingPen%": "Mastication (% du groupe)", "goalChews": "Objectif de mastications", "herdLocomotionScore": "Score de locomotion du troupeau", "averageMilkYieldDeLaval": "Production Moyen (kg/vache)", "robotFreeGeaInfoUtilizationTime": "Temps d'utilisation : 100 - systèmes", "cargillEmailPlaceholder": "Inscrire l'adresse électronique de Cargill ici", "toolDetails": "Dé<PERSON> de l'outil", "no": "Non", "scorer": "<PERSON><PERSON>le de point", "Report.Visit.Report": "Rapport de visite", "mildModerateStressText": "La respiration dépasse 75 bpm | La température rectale dépasse 39 ° C (102,2 ° F)", "Report.PenTimeBudget.TimeAvailableForResting.CategoryLabel": "Temps disponible pour le repos", "SilageBagsOther": "Ag-bags - Autre ensilage", "amsUtilization": "Utilisation de l'AMS", "bagName": "Nom de l'ensilage", "imperialDensityUnit": "lbs/ft^4", "selectFollowing": "Sélectionnez les éléments suivants", "meterSquare": "ft.", "asFedIntakeIn(Kg)": "Consommation TQS (kg)", "editProspect": "Éditer Prospect", "PLN": "Pologne (zł PLN)", "bottom(g)": "Bas (g)", "createPen": "Créer un groupe", "requiredFieldMsg": "Indique un champ obligatoire", "of": "de ", "searchState/province/region": "Chercher un État/Province/Région", "metricTonsAF": "Tonnes métriques TQS", "ok": "Ok", "attachmentLimitExceed": "Limite de pièces jointes dépassée! ", "between15And20": "Entre 15 et 20", "or": "Ou ", "walkingTimeFromParlors": "Temps de marche jusqu'au salon (Heures)", "fourToSevenDays": "5 à 7 jours", "max12Questions": "sélectionnez au maximum 12 questions ci-dessous pour continuer", "tonsAF": "Tonnes TQS", "customerProspect": "Client / Prospect", "ProvimiUS": "Provimi US", "Report.General.Comments": "Commentaires généraux", "totalMilkingFailuresOther": "Nb incomplètes (total)", "TransitionCow": "Vache en transition", "BunkersAndPileHaylage": "Bunkers et piles - Ensilage de foin", "3xWeek": "3x par semaine", "nonFatSolid": "% Solides non gras", "emptyProspectDescription": "Essayez d’en ajouter un nouveau à l’aide du bouton Créer un prospect...", "NIO": "Nicaragua (NIO NIO)", "forageAuditManualSelection": "L’audit fourrage nécessite une sélection manuelle pour continuer", "robotType": "Type de robot", "goalTrayPercent": "Objectif plateau (%)", "addTmrScore": "Ajouter un score de RTM", "MUNMilkUrea": "Urée du lait (mg/dl)", "DueByToday": "est due aujourd'hui,", "Competitor": "Concurrent", "removeTrail": "Supprimer le parcours", "visitSelected": "Visites sélectionnées", "FarOff": "Taries", "RevenueLossDay": "<PERSON><PERSON> de revenu", "removeAll": "<PERSON><PERSON>", "profilePhoto": "Photo de profil ", "selectYear": "Sélectionnez l'année", "Report.PenTimeBudgetTimeRequired": "Temps requis", "syncNow": "Synchroniser maintenant", "manureScreenerName": "Nom du tamis à fumier", "selectSiteDetails": "Sélectionner les détails du site", "Report.Heatstress.Mild.Moderate.Stress": "<PERSON><PERSON> léger - modé<PERSON>", "recent": "<PERSON><PERSON><PERSON>", "requiredActionNoteDateTime": "Définir la date et l'heure du rappel est requis.", "goals": "Objectifs", "height": "<PERSON><PERSON>", "newVisit": "Nouvelle visite", "TWD": "Taïwan (NT$ TWD)", "std": "Standard", "switchTo": "Passer à", "robotFreeTimeGEA": "Temps Libre (%)", "deathLoss": "Pertes pour mortalité", "pensSelected": "Enclos sélectionnés", "at6InchesDayOrAt15CmPerDay": "À 6 pouces par jour OU à 15 cm par jour", "currentVisitTMRComparison": "Visite actuelle - Comparaison RTM", "PON": "Roumanie (lei PON)", "penAnalysis": "Analyse du groupe", "milkProteinYield": "<PERSON><PERSON><PERSON>", "milkLossKGDay": "<PERSON><PERSON>", "milkValueLossPerDay": "<PERSON><PERSON> de valeur de lait (par jour)", "lessThan10MB": "chaque photo <10MB", "robotic_milk_evaluation_milking_failures_yellowRange": "4 a 6", "sitesSelected": "Sites sélectionnés", "timeInLockUp": "Temps confinés (headlocks/carcant) (Heures)", "animal": "Animal", "bottomUnloadingSiloDimension": "Dimensions", "MetabolicIncidence": "Maladies métaboliques", "6To8layersPlastic": "6 à 8 couches", "pileSlopeGoal": "Objectif > 3.5 à 1.0", "milkingSystem": "Système de traite", "averageMilkFat": "Gras du lait moyen (%)", "startDate": "Date de début", "at3InchesPerDay": "à 3 pouces par jour", "ARS": "Argentine ($ ARS)", "updateDate": "Date de mise à jour", "avgManureScore": "Score moyen de fumier", "privacyStatement": "Déclatation de confidentialité", "timeInParlor": "Temps dans le salon de traite (Heures)", "dateRange": "Plage de dates", "metricDensityUnit": "kg/m^3", "addContact": "Ajouter un contact", "Report.Heatstress.Legend": "Légende", "tonsDM": "Tonnes MS", "lessThan5Kernels": "Moins de 5 grains entiers", "retainedPlacenta": "Rétention Placentaire", "withIn8Hours": "Dans les 8 heures", "current": "Actuel", "addTool": "Ajouter l'outil", "min": "Min", "selectSegment": "Sélectionner un segment", "lessThan10Percent": "< 10%", "silageName": "Nom de l'ensilage", "robotic_milk_evaluation_average_concentrated_fed_greenRange": "5 a 6", "lossOfEnergyConsumedInMcal": "Perte d'énergie consommée (Mcal)", "bagDimension": "Dimensions", "email": "<PERSON><PERSON><PERSON>", "RUB": "<PERSON><PERSON> (₽‎ RUB)", "silage": "Ensilage", "noPinnedContacts": "Aucun contact <PERSON>", "middle": "Milieu", "Calf": "<PERSON><PERSON>", "analyzed": "<PERSON><PERSON><PERSON><PERSON>", "milkLossValue": "Valeur de la perte de lait", "maxSelectionLimitReached": "Limite de sélection maximale atteinte!", "confirm": "Confirmer", "moreThan5Kernels": "Supérieur à 5", "visitName": "Nom de la visite", "selectOne": "Sélectionnez-en un…", "Comfort": "<PERSON><PERSON>", "estimatedDryMatterIntakeWeightInLbs": "Consommation estimée de la matière sèche (lb)", "lossCow": "Perte / vache", "country": "Pays", "endUserLicenseAgreement": " Contrat de licence de l'utilisateur ", "avgBoxTime": "Durée moyenne de box (min/vache)", "ShortDryPeriod": "Tarissement court", "DryCow": "<PERSON><PERSON> tarie", "robotic_milk_evaluation_robot_free_time_yellowRange": "11 a 12 ou 18 a 20", "somethingWentWrongError": "Quelque chose n'a pas fonctionné! Veuillez réessayer plus tard.", "evaluationDays": "Jours d'évaluation", "milkProtein(%)": "Pro<PERSON>ine du lait (%)", "BodyCondition": "État corporel", "AUD": "Australie ($ AUD)", "topUnloadingSiloName": "Nom du silo", "diameter": "<PERSON><PERSON><PERSON><PERSON>", "refreshTokenExpiry": "Votre session actuelle a expiré. Veuillez vous connecter à nouveau pour continuer!", "severeStressText": "La respiration dépasse 120-140 bpm | La température rectale dépasse 41 ° C (106 ° F)", "kgs": "Kgs", "ensureEmailConfigured": "<PERSON><PERSON><PERSON><PERSON> vous assurer que votre courriel est configuré avant le partage.", "restFeedOther": "Refus (#/vache)", "fillHeight": "Hauteur de remplissage du silo", "details": "Détails", "Mastitis(#/Month)": "Mammites (#/mois)", "toolsCap": "Outils  ", "foragePenToastMessage": "Un maximum de 10 Penn State peuvent être ajoutés", "restFeedLavalInfo": "Reste aliment : 100 - % concentrés non consommés", "sharepointReportError": "Impossible de télécharger le fichier. Veuillez contacter votre gestionnaire de données pour vérifier le mappage.", "MilkLossKg": "<PERSON><PERSON> de <PERSON> (kg)", "leastObserveAnimals": "Un minimum de 10 animaux doivent être observés", "milkProtein": "Protéine du lait", "allDetails": "Tous les détails", "animalsInTank": "Animaux dans le réservoir", "dryMatterIntakeEfficiency": "Matière sèche ingérée et efficacité alimentaire", "totalAnnualLosses": "<PERSON><PERSON> annu<PERSON> - Total", "PSP": "Penn State", "visitDetails": "<PERSON><PERSON><PERSON> de visite", "Haylage": "Ensilage de foin", "milkingSpeedDeLaval": "Débit moyen de lait quotidien par animal ({0}/min)", "lessThan15": "< 15 (dureté du grain)", "CZK": "République Tchèque (CZK CZK)", "General": "Général", "bacteriaCellCountUnit": "1,000cfu/mL", "pinned": "<PERSON><PERSON><PERSON>", "milkingFailures": "Échecs de traite (#/robot)", "herdAverage": "Moyenne du troupeau", "update": "Mise à jours", "averageMilkProductionKg": "Production laitière moyenne (Kg)", "attachedFileSuccessfully": " Fichier joint avec succès", "Straw": "Pa<PERSON>", "annualEconomicImpactTotal": "Impact économique annuel - Total", "Report.Heatstress.Energy.Equivalent.Milk.Loss": "Perte de lait équivalente en énergie ({0})", "milkingsLely": "Traites/vache/jour", "Report.RumenHealthManureScreening.BottomGoalMax": "Objectif inférieur min", "emptyAccountTitle": "Aucun compte trouvé!", "summary": "<PERSON><PERSON><PERSON>", "cases": "Cas", "scorerChangeMessage": "La modification de la méthode d'évaluation va réinitialiser toutes les données entrées", "apply": "Appliquer", "Health": "<PERSON><PERSON>", "Purina": "<PERSON><PERSON><PERSON>", "robotic_milk_evaluation_average_concentrated_fed_yellowRange": "4 a 4 ou 6 a 8", "noPensFound": "Aucun groupe trouvé!", "fullName": "Nom Co<PERSON>t", "filters": "Filtres", "bunkerDimension": "Dimensions", "averageConcentrateFedLely": "Moyenne de concentrés servis (kg/vache)", "milkFatProteinYieldKg": "Rendement en gras et protéines du lait (kg)", "createdBy": "créé par", "avgTmrParticleScore": "Score moy. de particules RTM", "disagree": "Être en désaccord", "Report.Not.Chewing": "Ne ruminent pas", "Report.PenTimeBudget.TimeAvailableForResting.Label": "<PERSON><PERSON>", "averageMilkProductionAnimalsInTank": "Prod. laitière moyenne - animaux au réservoir", "otherSilageQuestion8": "La longueur de coupes lors du chantier d'ensilage a-t-elle été contrôlée avec le penn state?", "deleteVisit": "Supprimer la visite", "standardDeviation": "Écart - Type", "robotic_milk_evaluation_milking_refusals_yellowRange": "0.8 a 1.1 ou 1.3 a 1.7", "endDate": "Date vide", "goalBottomPercent": "Objectif bas (%)", "Report.Heatstress.Loss.Of.Energy.Consumed": "Perte d'énergie consommée (Mcal)", "noUpcomingActions": "Aucune action à venir", "type": "Taper", "robotic_milk_evaluation_milking_failures_greenRange": "<4", "averageMilkProductionAnimalsInTankKg": "Prod. laitière moyenne - animaux au réservoir", "bcsDataTransferMessage": "L'analyse Animale (individuelle) est facultative et les données seront transférées à l'analyse de groupe", "robotFreeTimeOther": "Temps Libre (%)", "weather": "<PERSON><PERSON><PERSON><PERSON>", "Report.ForagePennState": "Pennstate Fourrages", "selectType": "Selectionner un type", "animalInputs": "Entrées d'animaux", "mid2_4mm": "Milieu 2 ", "goal": "Objectifs", "Report.RumenHealthManureScreening.Top": "<PERSON><PERSON>", "metabolicIncidencePercent": "Maladies métaboliques %", "selectSite": "Sélectionnez un site", "lastVisit": "<PERSON><PERSON><PERSON> visite", "agree": "Accepter", "toolsInProgress": "Outil(s) en cours", "Revenue": "<PERSON><PERSON><PERSON>", "Thirdparty": "Tiers", "UAH": "Ukraine (UAH UAH)", "notifications": "<PERSON><PERSON>", "Report.Heatstress.Moderate.Severe.Stress.Message": "La respiration dépasse 85 bpm | La température rectale dépasse 40°C (104°F)", "searchResult": "Résultat de la recherche", "onboardingSubTitle5": "Choisir ce que vous devez ajouter au rapport de visite", "customerCode": "Code du client", "componentYieldEfficiency": "Rendement et efficacité alimentaire", "onboardingSubTitle3": "Pre<PERSON>re des notes sur le pouce et définir des rappels pour les prochaines visites", "herdAnalysis": "<PERSON><PERSON><PERSON>", "onboardingSubTitle4": "Filtrer les informations précédentes ou récentes avec vos critères préférés", "tools": "Outils", "capacity": "Capacité", "noPensFoundDescription": "Essayez d’en ajouter un nouveau à l’aide du bouton Créer un groupe...", "noRecentVisits": "Aucune visite récente", "Provimi": "<PERSON><PERSON><PERSON>", "selectAnimalClass": "Sélectionner une classe d'animaux", "milkFatProteinYield": "Rendement en protéine + gras", "decimalNumberPlaceholder": "0,00", "moreThan40Percent": "> 40%", "imperialNELDairyUnit": "Mcal/lbs", "milkingRefusalsLely": "<PERSON><PERSON><PERSON>", "noResultShow": "Aucun résultat trouvé! ", "HeatStress": "Évaluation du stress thermique", "observation": "Observations", "pickup": "Livraison {0}", "performanceAndTreatmentCosts": "Coûts associés aux traitements et à la perte de performances", "primaryContactInfo": "Coordonnées principales", "CalfHeiferScorecard": "Carte de pointage des génisses et veaux", "forageAudit": "Tableau de bord de l'audit fourrage", "excel": "Excel", "milkingSpeedOther": "Vitesse de traite (kg/min)", "site": "Site", "unauthorizedUser": "Le courriel ou le nom d'utilisateur n'existe pas. Veuillez contacter l'administrateur du système et réessayer!", "feedOutRateInformation": "Consommation", "frenchCanada": "<PERSON><PERSON><PERSON> (Canada)", "onboardingSubTitle1": "Commencez une nouvelle visite directement à partir de l’écran d’accueil", "ForagePennState": "Pennstate Fourrages", "milkProcessorInfo": "Info Production/Laiterie", "onboardingSubTitle2": "Notez les données de plusieurs outils et groupes sur le même écran", "maximumScoresAllowed": "Un maximum de 10 Penn State peuvent être ajoutés", "bunkerAndPiles": "Bunkers et piles", "Report.Heatstress.Milk.Value.Loss.Perday": "<PERSON><PERSON> de valeur de lait (par jour) ({0})", "responses": "Réponses", "noRecordFoundDescription": "Essayez de rafraîchir votre écran ou de synchroniser l'application pour afficher les résultats…", "biWeekly": "Toutes les 2 semaines", "ManureScorePercentPerPen": "Score de fumier (% par groupe)", "QuarterPointScale": "<PERSON>chelle quart de point (0.25)", "cornSilageQuestion4": "Tous les fourrages sont-ils inspectés pour la détérioration et la moisissure? S'il est moisi, est-ce que le fourrage est jeté?", "cornSilageQuestion3": "Les unités de stockage du fourrage sont-elles dimensionnées pour la capacité par rapport aux besoins du troupeau laitier? (pas de trop rempli)", "goalMid1Percent": "Objectif milieu 1 (%)", "cornSilageQuestion6": "L’humidité des plants est déterminée pour chaque parcelle", "cornSilageQuestion5": "Conditions des récoltes d'ensilage, champs et emplacements documentées?", "robotic_milk_evaluation_milkings_redRange": "<2.6", "cornSilageQuestion2": "Le nombre de vaches et de fourrage doivent-ils être planifiés chaque année?", "cornSilageQuestion1": "À quelle fréquence les inventaires de fourrage sont-ils surveillés?", "feedingRate": "Consommation (TQS/Vache)", "generalCustomerSiteSetup": "Configuration générale du site", "eatingTime": "Temps d'alimentation (Heures)", "milking_frequency": "<PERSON><PERSON><PERSON>", "primaryContactFullName": "Nom complet du contact principal", "ScreenNew": "tamis - nouveau", "lastVisited": "<PERSON><PERSON><PERSON> visite", "TMRParticleScore": "Pennstate RTM", "ManureScreening": "<PERSON><PERSON> fumier ", "humidity(%)": "<PERSON><PERSON><PERSON><PERSON> (%)", "haylageQuestion6": "Fourrages récoltés au bon stade de maturité et d'humidité pour le type de récolte et d'installation d'entreposage.", "haylageQuestion8": "La longueur de coupe est-elle surveillée avec un Penn State?", "haylageQuestion7": "Combien de temps faut-il pour terminer la récolte? L'équipement et le travail sont-ils adéquats?", "RumenHealth": "Santé ruminale - Rumination", "milkPickUp": "<PERSON><PERSON><PERSON> de <PERSON>", "goalMaxPercent": "Objectif % (max)", "hoursOfSun": "Heures de soleil", "bacteriaCellCountWithUnit": "Comptage bactérien (1,000 cfu/mL)", "changeImage": "Changer la photo", "YourVisit": "Ta visite", "cudChewingScoreAnalysis": "Analyse du résultat des ruminations", "haylageQuestion9": "Des additifs d'ensilage, des inoculants ou des stabilisateurs aérobies sont-ils utilisés?", "parlor": "Salle de traite", "bottom": "Bas", "emptyNoteDescription": "Essayez d'actualiser votre écran ou contactez l'administrateur pour obtenir de l'aide…", "emptyNotesTitle": "Aucune note trouvée !", "noPensAdded": "Pas d'enclos a<PERSON>té<PERSON>!", "penTimeBudget": "Budget de temps d'enclos", "addProspect": "Ajouter un prospect", "kgsDMIn1M": "Kg matière sèche sur 1 pied", "videoMaxSizeValidation": "La taille de la vidéo est trop grande. La limite maximale est de 100 Mo", "noInternetConnection": "Aucune connection internet", "at7cmPerDay": "à 7 cm par jour", "published": "<PERSON><PERSON><PERSON>", "useCamera": "Utiliser la caméra", "scaleChangeMessage": "La modification de l'échelle peut entraîner une perte de données. Êtes-vous certain(e) de vouloir continuer?", "noWholeKernels": "Pas de grains entiers", "unsyncedDataLogoutMsg": "There is some unsynced data that you may want to sync before logout. Are you sure you want to continue?", "scoreAnalysis": "Analyse des scores", "removedAndMeasured": "Enlevés et mesurés", "RumenHealthManureScore": "Santé ruminale - Évaluation du fumier", "cornSilageQuestion8": "Les fourrages sont-ils récoltés à une maturité et à l'humidité appropriés pour le type de culture et l'installation d'entreposage?", "cornSilageQuestion7": "Les fourrages sont-ils récoltés à une maturité et à l'humidité appropriés pour le type de culture et l'installation d'entreposage?", "Dryhay": "Foin sec", "Report.Heatstress.Reduction.In.Dmi": "Réduction de la MSI ({0})", "cornSilageQuestion9": "Combien de temps faut-il pour terminer la récolte? (Équipement et travail adéquats)", "imageSizeError": "L'image excède la limite de 20MB. S'il vous plaît réessayer.", "SAR": "<PERSON><PERSON> (﷼ SAR)", "Screen": "tamis", "min|max": "Min | Max", "mun": "Urée du lait (mg/dL)", "privacyStatementEn": "Déclaration de confidentialité", "annualEconomicImpact": "Impact économique annuel", "selecOne": "Sélectionnez-en un…", "error": "<PERSON><PERSON><PERSON>", "tmrScoreName": "Nom du Score RTM", "barnName": "Nom de la ferme", "averageConcentrateFedDeLaval": "Moyenne de concentrés servis (kg/vache)", "noRecordFound": "Aucun enregistrement trouvé! ", "cat": "<PERSON><PERSON><PERSON><PERSON>", "averageConcentrateFedOther": "Moyenne de concentrés servis (kg/vache)", "robotic_milk_evaluation_average_concentrated_fed_redRange": "<3 ou >9", "searchVisit": "Rechercher la visite", "dryMatterIntakeIn(Kg)": "Consommation matière sèche (kg)", "milkProteinYieldKg": "Rendement en protéines du lait (kg)", "primaryContactEmail": "<PERSON><PERSON><PERSON> principal", "herd": "T<PERSON>eau", "forageAuditCategories": "Catégories d'audit des fourrages", "ForageAuditScorecard": "Évaluation des fourrages", "robotic_milk_evaluation_milkings_yellowRange": "2.5 a 2.8", "resources": "Ressources ", "dietInputLactatingAnimals": "<PERSON><PERSON><PERSON> deration (Animaux en lactation)", "componentEfficiencyOfDMI": "Efficacité des composantes (% de MSI)", "milkPerCow": "Perte de lait par lactation", "onScreen": "% sur les tamis", "Report.Heatstress.Temperature.In.Celcius": "Température °C", "emailPlaceholder": "Inscrire l'adresse électronique ici", "titleExists": "Le titre existe déjà", "overallForageScore": "Score de fourrage global", "cannotLogoutWhileSyncing": "Impossible de se déconnecter lors de la synchronisation", "exportLogs": "Journaux d'exportation", "unAvailable": "Indisponible", "unsyncedItems": "Éléments non synchronisés", "robotFreeGeaInfoUtilizationFreeTime": "% d'utilisation = temps libre", "avgBCSCalculated": "Condition de chair moyenne (calculé)", "category": "<PERSON><PERSON><PERSON><PERSON>", "milkSolid(kg)": "Lait vendu", "goalMax%": "Objectif - min (%)", "noResultShowDescription": "Nous n’avons trouvé aucun résultat. Modifiez les filtres sélectionnés ou essayez-les ultérieurement.", "syncMessage": "Vous n'avez pas synchronisé l'application au cours des dernières 24 heures. Veuillez le synchroniser dès que possible pour éviter toute perte de données !", "createSite": "Créer un site", "generalComments": "Observations générales", "day": "Jour", "year": "<PERSON><PERSON>", "updateAvailable": "Une nouvelle version de l'application est disponible", "forceUpdateAvailable": "Une nouvelle version de l'application est disponible et sa mise à jour est obligatoire.", "uploadingVisitReport": "Téléchargement des rapports de visite ! Attendez", "deleteNoteMessage": "Êtes-vous sûr de vouloir supprimer cette note ?", "delete": "<PERSON><PERSON><PERSON><PERSON>", "creator": "<PERSON><PERSON><PERSON><PERSON>", "noteCharacterLimitForVisitReport": "La limite de caractères a atteint 250 !", "charactersAllowedInReport": "Seulement 250 caractères seront inclus dans le rapport de visite de cette note", "addTrail": "A<PERSON>ter le parcours", "editTrail": "Modifier le parcours", "at": "at", "unsavedNotes": "Notes non enregistrées", "discardNotes": "Êtes-vous sûr de vouloir jeter vos notes non enregistrées ?", "discard": "<PERSON><PERSON>", "1": "1", "2": "2", "selectCreator": "Sé<PERSON><PERSON><PERSON> le créateur", "creatorSelected": "Créateur sélectionné", "errorCreatingNote": "erreur de création de note", "addGeneralNotes": "Ajouter des notes générales", "attachMoreNotesForThisVisit": "Joignez d’autres notes pour cette visite", "selectNotes": "Sélectionner des notes", "addNotes": "Ajouter des notes", "generalNotes": "Notes générales", "commentLimitExceeds": "La limite de commentaires dépasse", "penSelected": "Groupe(s) sélectionné(s)", "selectPens": "Sélectionner le groupe", "updateRequired": "Update Required", "updateRequiredDescription": "Une nouvelle version est prête avec des mises à jour ! Installez-le maintenant.", "buildReadyDescription": "Une nouvelle version est prête avec des mises à jour ! Accédez au menu et téléchargez-le maintenant pour profiter des dernières fonctionnalités et améliorations", "installUpdate": "Install Update", "postWeaned": "Post-Sevrage", "preWeaned": "Pre-Sevrage", "colostrum": "Colostrum", "Colostrum_AmountOfColostrumOrFed": "Quantité de colostrum servi ", "Colostrum_BrixPercentOfColostrumFed": "Brix (%) du colostrum servi ", "Colostrum_CleanAndDryCalvingArea": "Zone de parturition propre et sèche", "Colostrum_CleanAndSanitizeCalfFeedingEquipment": "Net<PERSON>yer et désinfecter le matériel d'alimentation des veaux entre les repas", "Colostrum_CleanCalfCartToTransportCalf": "Remorque à veau propre pour le transport", "Colostrum_HoursTillCalfIsRemovedFromMother": "He<PERSON>(s) avant que le veau soit séparer de la mère ", "Colostrum_HoursTillCalfReceivesColostrum": "Heure(s) avant que le veau reçoive le colostrum ", "Colostrum_NumberOfCowsInCalvingArea": "Nombre de vache dans la zone de parturition", "Colostrum_PasteurizeColostrumBeforeFeeding": "Du colostrum pasterisé est servi ", "Colostrum_PercentageOfNavelsDippedInSevenPercent": "% de nombrils trempé dans 7% d'iode dans la première heure", "Colostrum_RefrigeratedColostrumStoredLess": "Colostrum réfrigéré et est entreposé moins de 24 heures ", "GrowerPubertyPregnancyCloseup_CleanAndDryPen": "Enclos propre et sèche ", "GrowerPubertyPregnancyCloseup_DesiredBCSIsAchieved": "EC désirée pour atteindre le stade de maturité", "GrowerPubertyPregnancyCloseup_EvidenceOfLooseManure": "<PERSON>e de fumier lousse ", "GrowerPubertyPregnancyCloseup_FeedBunkIsCleanedDaily": "Mangeoire est nétoyée à chaque jour et les refus sont enlevés", "Preweaned_FreeChoiceCleanWaterIsAvailable": "Eau propre et  à volonté est acessible ", "GrowerPubertyPregnancyCloseup_GroupWithUniformHeiferSize": "Groupe de génisses avec une taille uniforme ", "GrowerPubertyPregnancyCloseup_PercentageOfOverCrowding": "% de surpeuplement", "GrowerPubertyPregnancyCloseup_RationsBalanceForGrowth": "Ration balancée pour les cibles de croissances et revue fréquement ", "GrowerPubertyPregnancyCloseup_SizeOfBunkSpace": "Espace à la mangeoir par génisses est adéquate ", "Preweaned_SizeOfPenAdequatePerHeifer": "Espace dans l'enclos par génisses est adéquate ", "KeyBenchmarks_AgeInMonthAtFirstCalving": "Âge (en mois) au premier vêlage", "KeyBenchmarks_CalvingAndHeiferRecord": "Registre des vêlages et des génisses utilisées", "KeyBenchmarks_FifteenPercentOfMatureBodyWeight": "Atteint 15% du poids mature à 90 jours", "KeyBenchmarks_FiftyFivePercentOfMatureBodyWeight": "Atteint 55% du poids mature à la gestation", "KeyBenchmarks_HeiferPeakProduce": "Pics de lait des génisses en % de la moyenne du troupeau ", "KeyBenchmarks_NintyDaysMorbidity": "Morbidité à 90 jours", "KeyBenchmarks_NintyDaysMortality": "Mortalité à 90 jours ", "KeyBenchmarks_NintyFourPercentOfMatureBodyWeight": "Atteint 94% du poids mature au vêlage", "KeyBenchmarks_PercentOfHeifersPregnant": "% des génisse pleinne à 15 mois", "KeyBenchmarks_SerumlgG": "Serum IgG (g/L) à 48 heurs", "Preweaned_CleanAndDryPen": "Enclos propre et sèche ", "Postweaned_EvidenceOfAcidosisInManure": "Signe d'acidose dans le fumier", "Preweaned_EvidenceOfScoursOrPneumonia": "Signe de diarrhée ou de pneumonie ", "Postweaned_FeedBunkIsCleanedDaily": "Mangeoire est nétoyée à chaque jour et les refus enlevés", "Preweaned_ForageAvailability": "Disponibilité des fourrage", "Postweaned_FreshQualityStarterAvailable": "Moulée de départ/croissance est disponible ", "Postweaned_SizeOfBunkSpace": "Espace à la mangeoire par génisses est adéquate ", "Postweaned_SizeOfPenAdequate": "Espace dans l'enclos par génisses est adéquate ", "Preweaned_WellVentilatedPenWithNoDraftOnCalf": "Enclos bien ventillé et sans courant d'aire directement sur les veaux ", "Preweaned_CleanAndSanitizeCalfFeedingEquipment": "Corectement nettoyé et désinfecter l'equipement et la mangeoire des veau entre les repas ", "Preweaned_CMRIsProperlyMixedAndAdequatelyFed": "LR est bien mélangé et servie adéquatement", "Preweaned_ConsistentFeedingTimesAndProtocols": "Les protocoles et température d'alimentation sont consistants", "Preweaned_FreeChoiceFreshCalfStarterIsAvailable": "Moulée de départ/croissance est disponible ", "Preweaned_WeaningAtIntakeOfOnekgStarterPerDay": "Sevrage faite lorques la consommation de la  moulée départ atteint 1 kg par jour ", "CalfHeiferColostrum": "Colostrum", "CalfHeiferPreweaned": "Pré sevrage", "CalfHeiferPostweaned": "Post-sevrage", "CalfHeiferGrowerPuberty": "Croissance, Puberté, Gestation et Préparation", "CalfHeiferKeyBenchmarks": "comparative", "grower": "Croissance", "puberty": "<PERSON><PERSON><PERSON>", "pregnancy": "Gestation", "closeUp": "Vaches en préparation", "overallCalfHeiferScore": "Score Global de l'outil Veaux et Génisses", "phase": "Phase", "pasteurizedMilkFed": "Lait pasteurisé servi", "surveyCategories": "Choix de l'audit", "textureFeed": "Alimentation Texturée", "lessThen20OrNotTested": "<20% ou ne testez pas", "diabledInfoText": "Le rapport est actuellement indisponible pour ce site. Veuillez revenir plus tard ou contacter le support pour plus d'informations.", "invalidName": "Nom invalide", "tmr": "Rtm", "individualCow": "Par vache", "herdProfile": "Information sur le troupeau", "feeding": "Alimentation", "milkProductionOutputs": "Production laitière", "breed": "Race", "numberOfTmrGroups": "Nombre de groupes rtm", "coolAid": "Cool aid", "fortissaFit": "Fortissa fit", "milkingPerDay": "Traite/jour", "enterValue": "Entrez la valeur", "homeGrownForages": "Fourrages produits sur la ferme", "addHomeGrownForages": "Ajouter fourrages produits sur la ferme", "homeGrownGrains": "Grains produits à la ferme", "addHomeGrownGrains": "Ajouter grains produits à la ferme", "purchaseBulkFeed": "Intrant en vrac", "addPurchaseBulkFeed": "Ajouter intrant en vrac", "purchaseBagsFeed": "Intrant en sacc", "addPurchaseBagsFeed": "Ajouter intrant en sacc", "forageName": "Nom de fourrages", "grainName": "Nom de grains", "feedName": "Nom de alimentation", "incentiveDays": "Journ<PERSON>", "totalQuota": "Quota total", "perDay": "/Jour", "pricePerKg": "Prix ($/kg)", "pricePerHl": "Prix (kg/hl)", "pricePerKgPerCow": "Prix (kg/vache)", "kgOfQuotaPerDay": "kg de Quota (kg/Jour)", "incentiveDaysKgPerDay": "<PERSON><PERSON><PERSON> (kg/Jour)", "currentQuotaUtilizationKgPerDay": "Quota produit par jour", "butterfat": "<PERSON><PERSON> grasse", "protein": "<PERSON><PERSON><PERSON>", "lactoseAndOtherSolids": "Lactose et autres solides", "class2Protein": "Protéine (classe 2)", "class2LactoseAndOtherSolids": "Lactose et autres solides (classe 2)", "deductions": "Déductions", "totalHerdPerDay": "Total/va/jr (T.Q.S)", "totalDryMatter": "kg total de M.S.", "pricePerTon": "Prix/tonne ($)", "ratioSNFPerButterfat": "Ratio S.N.G./M.G", "maxAllowed": "Max permis", "totalFatProtein": "Total kg de gras et protéine (kg/v/jour)", "dairyEfficiency": "Ff. alimentaire (lait corrigé énergie)", "totalRevenuePerLiter": "Revenu total par litre", "feedCostPerLiter": "Coût alimentaire par litre", "purchasedFeedCostPerLiter": "Cout des aliments achetés par litre", "concentrateCostPerLiter": "Coût de concentré par litre", "concentrateCostPerKgBF": "Coût de conc. par kg gras", "bfRevenue": "Revenu ($/va/jr) gras", "proteinRevenue": "Revenu ($/va/jr) prot", "otherSolidsRevenue": "Revenu ($/va/jr) os", "deductionsPricePerCowPerDay": "Déductions ($/va/jr)", "snfNonPayment": "SNG non-payés ($/va/jr)", "totalRevenuePricePerKgFat": "Revenu total ($/kg de gras)", "totalRevenueCowDay": "Revenu total ($/vache/jr)", "underQuotaLostRevenuePerMonth": "Revenu perdu par mois si quota non-rempli", "rofPerKgButterFat": "Rsa par kg de gras", "rof": "Retour sur l'alimentation", "ReturnOverFeed": "Retour sur l'alimentation", "herdBaseline": "Information sur le troupeau", "quota": "<PERSON><PERSON><PERSON>", "kgOfQuota": "Quota à la ferme (kg/jr)", "noOfCowsToFillQuota": "Nb de vache pour produire le quota", "noOfCowsToFill50Kg": "Nb de vache pour produire 50 kg quota", "averageMilkProductionLitresPerCowPerDay": "Prod. laitière moyenne par vache/jr", "subtotal": "Sous total", "totalRevenuePricePerKgButterFat": "Revenue matière grasse", "feedCosts": "Coût des aliment", "forageFeedCostPerCowPerDay": "Coûts fourrage ($/v/jour)", "grainsCostPerCowPerDay": "Coûts grain produit à la ferme ($/v/jour)", "totalOnFarmFeedCostPerCowPerDay": "Coûts totaux des produits fait à la ferme ($/v/jour)", "purchasedBulkFeedPerCowPerDay": "Coûts des intrant en vrac  ($/v/jour)", "purchasedBagsFeedPerCowPerDay": "Coûts des intrant en sacs ($/v/jour)", "totalPurchasedCostPerCowPerDay": "Coûts des intrant total ($/v/jour)", "totalFeedCostPerCowPerDay": "Coûts des intrant total ($/v/jour)", "totalConcentrateCostPerCowPerDay": "Coûts des concentrate total ($/v/jour)", "feedCostPerKgOfBF": "Coût des aliment/kg de M.G.", "feedCostPerLitreOfMilk": "Coût des aliment / litre de lait", "foragePercentage": "% fourrage", "currentReturnOverFeedCosts": "Retour sur l'alimentation actuel", "previousReturnOverFeedCosts": "Retour sur l'alimentation précédent", "returnOverFeedCostPerCowPerDay": "Retour sur l'alimentation ($/vache/jour)", "returnOverFeedCostPerKgOfBF": "Retour sur l'alimentation ($/kg de gras)", "returnOverFeedCostPerLitre": "Retour sur l'alimentation ($/litre)", "totalQuotaKgPerDay": "Quota total (kg/Jour)", "selected": "Sélectionnées"}