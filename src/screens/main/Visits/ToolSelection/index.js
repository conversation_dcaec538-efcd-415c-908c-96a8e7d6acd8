// modules
import React, { useCallback, useState, useEffect } from 'react';
import { useFocusEffect, useIsFocused } from '@react-navigation/core';
import { useDispatch, useSelector } from 'react-redux';
import {
  TouchableOpacity,
  View,
  ScrollView,
  Alert,
  BackHandler,
  Text,
} from 'react-native';
import DeviceInfo from 'react-native-device-info';

// styles
import styles from './styles';

// localization
import i18n from '../../../../localization/i18n';

// reusable components
import TopBar from '../../../../components/TopBar';
import TabSelector from '../../../../components/common/TabSelector';
import SearchBar from '../../../../components/common/SearchBar';
import VisitFilterBottomSheet from '../../../../components/Visits/FilterBottomSheet';
import PrimaryButton from '../../../../components/common/FormButton/PrimaryButton';
import ToolSelectionToolsList from '../../../../components/Visits/ToolSelection/ToolsList';

//helper
import { setOrSpliceFromArray } from '../../../../helpers';
import {
  getEnabledDisabledCategoriesCategoryByTools,
  inProgressToolCount,
  isToolInProgress,
} from '../../../../helpers/visitHelper';
import { stringIsEmpty } from '../../../../helpers/alphaNumericHelper';
import {
  getCurrencyKey,
  getUnitOfMeasure,
} from '../../../../helpers/appSettingsHelper';

// constants
import {
  TOOL_SELECTION_TAB_LABELS,
  VISIT_STATUS,
} from '../../../../constants/AppConstants';
import ROUTE_CONSTANTS from '../../../../constants/RouteConstants';
import { VISIT_TYPES } from '../../../../constants/FormConstants';

// actions
import {
  resetCreateVisitRequest,
  resetVisitDataRequest,
  deleteVisitRequest,
  publishVisitRequest,
  resetVisitHistoryRequest,
  resetDeleteVisitRequest,
  updateCurrency,
  getSelectedVisitRequest,
  resetUpdateCurrencyRequest,
  publishVisitSuccess,
} from '../../../../store/actions/visit';
import { clearVisitReportPersistedData } from '../../../../store/actions/visitReport';

import withLogging from '../../../../components/common/ScreenWithLogging';
import {
  resetSaveActivePenRequest,
  resetSaveActiveScreenRequest,
} from '../../../../store/actions/toolSwitching';

import { noteBookFilterModel } from '../../../../models/noteBook';
import {
  getNotebookTrailDataRequest,
  notesListResetRequest,
} from '../../../../store/actions/notebook';
import { getMultilingualEnumRequest } from '../../../../store/actions/enum';

const ToolSelection = props => {
  const dispatch = useDispatch();
  const isFocused = useIsFocused();

  const toolSwitchingState = useSelector(state => state.toolSwitching);
  const visitState = useSelector(state => state.visit) || {};
  const siteName = useSelector(state => state.tool.siteData.siteName);
  const visit = useSelector(state => state?.visit?.visit) || {};
  const enums = useSelector(state => state.enums);
  const isNewVisit = useSelector(state => state?.visit?.isNewVisit) || false;
  const countryTools =
    useSelector(state => state?.userPreferences?.countryTools) || [];
  const favourites = useSelector(state => state.userPreferences.favourites);
  const milkSoldState = useSelector(state => state.milkSoldEvaluation);
  const metabolicIncidence = useSelector(state => state.metabolicIncidence);
  const visitedDeletedSuccessfully = useSelector(
    state => state.visit.visitedDeletedSuccessfully,
  );
  const userData = useSelector(
    state => state?.userPreferences?.userPreferences,
  );
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [filteredCountryTools, setFilteredCountryTools] =
    useState(countryTools);
  const [isBottomSheetVisible, setIsBottomSheetVisible] = useState(false);

  useEffect(() => {
    if (visitState.updateCurrency) {
      dispatch(
        getSelectedVisitRequest({
          id: visit?.sv_id,
          localId: visit?.id,
        }),
      );
      dispatch(resetUpdateCurrencyRequest());
    }
  }, [visitState.updateCurrency]);

  useEffect(() => {
    // Update currency in Visit if it is in progress
    if (visit?.visitStatus === VISIT_STATUS.IN_PROGRESS) {
      const currencyKey = getCurrencyKey(userData);
      const unitOfMeasure = getUnitOfMeasure(userData);
      if (currencyKey || unitOfMeasure) {
        dispatch(
          updateCurrency({
            currency: currencyKey,
            unitOfMeasure: unitOfMeasure,
            visitId: visit.id,
          }),
        );
      }
    }

    dispatch(getMultilingualEnumRequest());

    return () => {
      dispatch(resetVisitDataRequest());
      dispatch(resetCreateVisitRequest());
      dispatch(clearVisitReportPersistedData());
    };
  }, []);

  //CDEA-1566
  useEffect(() => {
    if (visitedDeletedSuccessfully === true) {
      goBackOrNavigateToDashboard();
      dispatch(resetDeleteVisitRequest());
    }
  }, [visitedDeletedSuccessfully]);

  //clear selected health tool pen and active category tool from reducer
  useEffect(() => {
    if (isFocused && toolSwitchingState?.healthCurrentActivePen) {
      dispatch(resetSaveActivePenRequest());
    }
    if (isFocused && toolSwitchingState?.healthCurrentActiveScreen) {
      dispatch(resetSaveActiveScreenRequest());
    }
  }, [isFocused]);

  const handleBackButtonClick = () => {
    navigateToDashboard();
    return true;
  };

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  useFocusEffect(
    useCallback(() => {
      filterTools();
    }, [
      searchTerm,
      selectedIndex,
      favourites?.tools,
      milkSoldState?.milkSoldLoading,
      metabolicIncidence?.saveMetabolicIncidenceLoading,
    ]),
  );

  // CDEA-2962- Fixes in redirection
  useEffect(() => {
    if (visitState.visitPublished) {
      dispatch(resetVisitDataRequest());
      goBackOrNavigateToDashboard();
      dispatch(publishVisitSuccess(false));
    }
  }, [visitState.visitPublished]);

  const goBack = () => {
    dispatch(resetVisitDataRequest());
    props.navigation.goBack();
  };

  const navigateToDashboard = () => {
    dispatch(resetVisitDataRequest());
    dispatch(resetCreateVisitRequest());
    props.navigation.reset({
      routes: [{ name: ROUTE_CONSTANTS.MAIN }],
    });
  };

  const goBackOrNavigateToDashboard = () => {
    if (isNewVisit) {
      navigateToDashboard();
    } else {
      goBack();
    }
  };

  const getHeaderTitle = () => {
    // TODO: Display Visit Name
    return visit?.visitName || i18n.t('selectTool');
  };

  const filterTools = () => {
    let filteredToolList = countryTools.filter(tool => {
      //search condition
      let f = tool.name.toLowerCase().includes(searchTerm.toLowerCase());

      //categories condition
      if (f && selectedCategories.length > 0) {
        f = selectedCategories.indexOf(tool.toolGroupId) != -1;
      }

      //tabs condition
      if (f && selectedIndex == 1) {
        f = tool.isFavourite == true;
      } else if (f && selectedIndex == 2) {
        //show tools that are in progress
        f = isToolInProgress(visit, tool.toolId);
      }

      return f;
    });
    setFilteredCountryTools(filteredToolList);
    if (DeviceInfo.isTablet()) {
      if ((filteredToolList.length + 1) % 3 === 0) {
        setFilteredCountryTools([...filteredToolList, {}]);
      }
    }
  };

  const onTabChange = index => {
    setSelectedIndex(index);
  };

  const searchTermChangeHandler = val => {
    setSearchTerm(val);
  };

  const onCategorySelection = val => {
    let categories = selectedCategories;
    categories = setOrSpliceFromArray(categories, val);

    setSelectedCategories(categories);
    filterTools();
  };

  const onClearSearchPress = () => {
    setSearchTerm('');
  };

  const displayCategoryList = () => {
    let enabledDisabledCategories =
      getEnabledDisabledCategoriesCategoryByTools(filteredCountryTools);

    return enabledDisabledCategories.map((category, index) => {
      let isSelected = selectedCategories.indexOf(category.name) != -1;
      return (
        <TouchableOpacity
          key={`${category}_${index}`}
          onPress={() => {
            onCategorySelection(category.name);
          }}
          style={
            !category.enabled
              ? styles.disabledCategoryContainer
              : isSelected
              ? styles.selectedCategoryContainer
              : styles.categoryContainer
          }>
          <Text
            style={
              !category.enabled
                ? styles.disabledCategoryText
                : isSelected
                ? styles.selectedCategoryText
                : styles.categoryText
            }>
            {i18n.t(category.name)}
          </Text>
        </TouchableOpacity>
      );
    });
  };

  const closeBottomSheet = () => {
    setIsBottomSheetVisible(false);
  };

  const navigateToVisitReport = () => {
    closeBottomSheet();
    const params = {
      visitLocalId: visit.id,
      visitServerId: visit.sv_id,
    };
    props.navigation.navigate(ROUTE_CONSTANTS.VISIT_REPORT_STACK, {
      screen: ROUTE_CONSTANTS.VISIT_REPORT,
      params,
    });
  };

  const navigateToNoteBook = async () => {
    let _noteBookFilterModel = noteBookFilterModel(
      null,
      1,
      null,
      null,
      null,
      null,
      [
        {
          businessName: visitState?.visit?.businessName || '',
          accountId: visitState?.visit?.customerId,
          localAccountId: visitState?.visit?.localCustomerId,
          localId: '',
        },
      ],
      [
        {
          siteId: visitState?.visit?.siteId,
          localSiteId: visitState?.visit?.localSiteId,
          name: siteName,
          localId: '',
          accountId: visitState?.visit?.customerId,
          localAccountId: visitState?.visit?.localCustomerId,
        },
      ],
      [
        {
          sv_id: visitState?.visit?.sv_id,
          id: visitState?.visit?.id,
          name: visitState?.visit?.visitName,
          accountId: visitState?.visit?.customerId,
          localAccountId: visitState?.visit?.localCustomerId,
        },
      ],
      [],
      null,
    );

    dispatch(notesListResetRequest());
    dispatch(
      getNotebookTrailDataRequest({
        model: _noteBookFilterModel,
        showTrail: true,
        requestList: true,
      }),
    );

    // props.navigation.navigate(
    //   ROUTE_CONSTANTS.NOTE_BOOK_LIST,
    //   _noteBookFilterModel,
    // );
  };

  return (
    <>
      <TopBar
        backButton={!isNewVisit}
        backButtonClick={goBack}
        crossIcon={isNewVisit}
        crossClick={goBackOrNavigateToDashboard}
        editToolIcon
        notesIcon
        onNotesClick={navigateToNoteBook}
        verticalDotIcon
        verticalDotClick={() => {
          setIsBottomSheetVisible(true);
        }}
        titleStyles={styles.titleText}
        title={getHeaderTitle()}
        warningView={Object.keys(enums?.enum).length == 0}
        warningViewText={i18n.t('toolHeaderWarningMessage')}
      />
      <View style={styles.container}>
        <View>
          <TabSelector
            onTabChange={onTabChange}
            selectedIndex={selectedIndex}
            labels={TOOL_SELECTION_TAB_LABELS}
          />
        </View>

        <View style={styles.flexOne}>
          <View style={styles.searchFilterContainer}>
            <View style={styles.flex1}>
              <SearchBar
                searchTerm={searchTerm}
                onChange={searchTermChangeHandler}
                placeholder={i18n.t('searchToolByName')}
                onClearSearchPress={onClearSearchPress}
              />
            </View>
          </View>
          <View style={styles.categoriesRow}>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              {displayCategoryList()}
            </ScrollView>
          </View>

          <ToolSelectionToolsList
            filteredCountryTools={filteredCountryTools}
            selectedIndex={selectedIndex}
            searchTerm={searchTerm}
            selectedCategories={selectedCategories}
          />
        </View>
      </View>

      {inProgressToolCount(visit) > 0 &&
        visit?.visitStatus == VISIT_TYPES.IN_PROGRESS &&
        visit?.isEditable && (
          <View style={styles.bottomButtonContainer}>
            <PrimaryButton
              label={i18n.t('publish')}
              onPress={() => {
                dispatch(
                  publishVisitRequest({
                    visitId: visit.id,
                  }),
                );
                dispatch(resetVisitHistoryRequest());
                // goBackOrNavigateToDashboard();
              }}
            />
          </View>
        )}
      {/* TODO: Wasi Code refactoring */}
      <VisitFilterBottomSheet
        isVisible={isBottomSheetVisible}
        setIsVisible={() => setIsBottomSheetVisible(false)}
        modalTitle={i18n.t('visitDetails')}
        shouldShowDeleteVisit={
          stringIsEmpty(visit?.sv_id ? visit?.sv_id : '') &&
          visit?.visitStatus != VISIT_TYPES.PUBLISHED &&
          visit?.isEditable
        }
        // shouldDisableVisitReport={stringIsEmpty(
        //   visit?.sv_id ? visit?.sv_id : '',
        // )}
        onDeleteVisitClick={() => {
          setIsBottomSheetVisible(false);
          Alert.alert(
            i18n.t('deleteVisitAlertTitle'),
            i18n.t('deleteVisitAlertDesc'),
            [
              {
                text: i18n.t('no'),
              },
              {
                text: i18n.t('yes'),
                onPress: () => {
                  dispatch(
                    deleteVisitRequest({
                      visitId: visit.id,
                    }),
                  );
                  // goBackOrNavigateToDashboard();
                },
              },
            ],
          );
        }}
        onVisitReportDownloadClick={navigateToVisitReport}
      />
    </>
  );
};

export default withLogging(ToolSelection, 'Tool Selection');
