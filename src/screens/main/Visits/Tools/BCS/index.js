// modules
import React, { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigation } from '@react-navigation/native';

// components
import ToolAnalysis from '../../../../../components/Visits/Tools/common/ToolAnalysisTypes';
import ToolsInProgress from '../../../../../components/Visits/Tools/common/ToolsInProgress';
import StepsFooter from '../../../../../components/Visits/Tools/common/ToolStepsFooter';
import AnimalAnalysis from '../../../../../components/Visits/Tools/BCS/AnimalAnalysis';
import PenAnalysis from '../../../../../components/Visits/Tools/BCS/PenAnalysis';
import HerdAnalysis from '../../../../../components/Visits/Tools/BCS/HerdAnalysis';
import withLogging from '../../../../../components/common/ScreenWithLogging';
import { showToast } from '../../../../../components/common/CustomToast';

// constants
import {
  BCS_STEPS,
  ENUM_CONSTANTS,
  PEN_HERD_ANALYSIS_STEPS,
  TOOL_ANALYSIS_TYPES,
  VISIT_TABLE_FIELDS,
} from '../../../../../constants/AppConstants';
import { TOAST_TYPE } from '../../../../../constants/FormConstants';

// localization
import i18n from '../../../../../localization/i18n';

// actions
import {
  clearActiveCategoryTool,
  setActiveCategoryToolRequest,
} from '../../../../../store/actions/tool';
import { getSitePensRequest } from '../../../../../store/actions/tool';
import {
  getEnumRequest,
  getMultilingualEnumRequest,
} from '../../../../../store/actions/enum';
import { resetSaveActiveScreenRequest } from '../../../../../store/actions/toolSwitching';
import {
  saveBCSGoalsRequest,
  getBCSPenAnalysisRequest,
  resetSaveBCSGoalsRequest,
} from '../../../../../store/actions/tools/bcs';

// helpers
import {
  getFormattedGoalsDataForBCS,
  initializeBCSGoals,
  convertBCSGoals,
  unformatGoalsDataForBCS,
} from '../../../../../helpers/toolHelper';
import { healthToolScreenAutoSelect } from '../../../../../helpers/visitHelper';

const BCS = ({
  selectedCategoryTool,
  currentActiveTool,
  healthCurrentActivePen = null,
  healthCurrentActiveScreen = null,
}) => {
  const initialStep = 1;
  const totalBCSSteps = 3;
  const backToolListingStep = 0;

  const dispatch = useDispatch();
  const navigation = useNavigation();
  const visitState = useSelector(state => state.visit);
  const toolState = useSelector(state => state.tool);
  const enumState = useSelector(state => state.enums);
  const bcsState = useSelector(state => state.bcs);

  const [currentStep, setCurrentStep] = useState(initialStep);
  const [scaleList, setScaleList] = useState([]);
  const [selectedVisits, setSelectedVisits] = useState([]);
  const [goalsData, setGoalsData] = useState([]);
  const [isDirty, setIsDirty] = useState(false);
  const [enableResults, setEnableResults] = useState(false);
  const [penAnalysis, setPenAnalysis] = useState(null); //contains pen data for pen analysis
  const penDataForHerdSum = useRef([]); //contains pen data for herd analysis
  const animalDataForAnimalAnalysis = useRef([]); //contains animal data for animal analysis

  const { isEditable = false } = visitState?.visit || {};

  const fetchSitePensData = () => {
    const { siteId, localSiteId, bodyCondition } = visitState.visit || {};
    const payload = {
      bodyCondition,
      siteId,
      localSiteId,
      toolType: VISIT_TABLE_FIELDS.BODY_CONDITION,
    };
    dispatch(getSitePensRequest(payload));
  };

  const fetchGoalsData = () => {
    const { id } = visitState.visit || {};
    dispatch(
      getBCSPenAnalysisRequest({
        localId: id,
        enumState: enumState,
        isEditable: isEditable,
      }),
    );
  };

  useEffect(() => {
    const { id } = visitState.visit || {};
    fetchSitePensData();
    fetchGoalsData();
    dispatch(getEnumRequest());
    dispatch(getMultilingualEnumRequest());
    setSelectedVisits([id]);
  }, []);

  useEffect(() => {
    if (enumState?.enum?.[ENUM_CONSTANTS.BCS_POINT_SCALES]) {
      let scales = enumState?.enum?.[ENUM_CONSTANTS.BCS_POINT_SCALES] || [];
      if (!!scales?.length) {
        scales = scales.map(scale => ({ ...scale, name: scale.value }));
      }
      setScaleList(scales);
    }
  }, [enumState]);

  useEffect(() => {
    if (!toolState.recentVisitsLoading) {
      const recentVisits = toolState.recentVisits || [];
      const visitIds = recentVisits.map(visit => visit.id);
      setSelectedVisits(visitIds);
    }
  }, [toolState.recentVisitsLoading]);

  useEffect(() => {
    if (!bcsState.bcsPenAnalysisLoading) {
      let goals = [];
      if (bcsState.bcsPenAnalysis && bcsState.bcsPenAnalysis.goals) {
        goals = convertBCSGoals(bcsState.bcsPenAnalysis.goals);
      } else {
        goals = initializeBCSGoals(enumState, true);
      }

      setGoalsData(goals);
    }
  }, [bcsState.bcsPenAnalysisLoading]);

  useEffect(() => {
    // When save operation is successful, reset save reducer and fetch goals data again
    if (bcsState.saveBCSGoalsSuccess) {
      dispatch(resetSaveBCSGoalsRequest());
      fetchGoalsData();
    }
  }, [bcsState.saveBCSGoalsSuccess]);

  useEffect(() => {
    if (bcsState.saveBCSGoalsError) {
      showToast(
        TOAST_TYPE.ERROR,
        bcsState.saveBCSGoalsErrorMsg || i18n.t('somethingWentWrongError'),
      );
    }
  }, [bcsState.saveBCSGoalsError]);

  useEffect(() => {
    if (healthCurrentActiveScreen && currentStep === initialStep) {
      healthToolScreenAutoSelect(
        dispatch,
        healthCurrentActiveScreen,
        currentStep,
        setCurrentStep,
      );
    }
  }, [healthCurrentActiveScreen]);

  const onNextStepClick = () => {
    if (currentStep === totalBCSSteps) {
      dispatch(resetSaveActiveScreenRequest()); //pressing 'done' button should clear reducer
      dispatch(clearActiveCategoryTool());
      setCurrentStep(initialStep);
    } else {
      const localSum = shouldEnableResultsButton(
        selectedCategoryTool?.toolType,
        selectedCategoryTool?.toolType === TOOL_ANALYSIS_TYPES.PEN_ANALYSIS
          ? penAnalysis
          : penDataForHerdSum.current,
      );
      localSum && setCurrentStep(prevState => prevState + 1);
    }
  };

  const onPrevStepClick = () => {
    if (currentStep === totalBCSSteps) {
      setCurrentStep(prevState => prevState - 1);
    } else if (currentStep === 2) {
      dispatch(resetSaveActiveScreenRequest());
      dispatch(clearActiveCategoryTool());
      setCurrentStep(prevState => prevState - 1);
    }
  };

  const onToolListingClick = selectedStep => {
    if (selectedStep?.step == backToolListingStep) {
      navigation.goBack();
    } else if (selectedStep?.step === initialStep) {
      dispatch(resetSaveActiveScreenRequest()); //pressing 'done' button should clear reducer
      dispatch(clearActiveCategoryTool());
      setCurrentStep(initialStep);
    }
  };

  const onDropdownStepSelect = selectedStep => {
    if (currentStep === selectedStep?.step) return;
    else if (selectedStep?.step === initialStep) {
      dispatch(clearActiveCategoryTool());
      setCurrentStep(prevState => (prevState = selectedStep?.step));
    } else if (selectedStep?.step === 2) {
      setCurrentStep(prevState => (prevState = selectedStep?.step));
    } else if (selectedStep?.step === 3) {
      if (
        selectedCategoryTool?.toolType === TOOL_ANALYSIS_TYPES.ANIMAL_ANALYSIS
      ) {
        if (animalDataForAnimalAnalysis.current == true) {
          setCurrentStep(prevState => (prevState = selectedStep?.step));
        }
        return;
      }
      const localSum = shouldEnableResultsButton(
        selectedCategoryTool?.toolType,
        selectedCategoryTool?.toolType === TOOL_ANALYSIS_TYPES.PEN_ANALYSIS
          ? penAnalysis
          : penDataForHerdSum.current,
      );
      localSum && setCurrentStep(prevState => (prevState = selectedStep?.step));
    }
  };

  // @description pen analysis select handler, setup pen analysis data in reducer
  const handlePenAnalysisSelect = () => {
    const selectedTool = {
      toolType: TOOL_ANALYSIS_TYPES.PEN_ANALYSIS,
      toolName: i18n.t('penAnalysis'),
    };
    dispatch(setActiveCategoryToolRequest(selectedTool));
    onNextStepClick();
  };

  const calculatePensSum = penAnalysisArray => {
    let sum = 0;
    penAnalysisArray?.bodyConditionScores?.map(item => {
      sum += item.animalsObserved;
    });
    setEnableResults(sum > 0);
    return sum > 0;
  };

  const calculateHerdSum = penAnalysisArray => {
    let sum = false;
    if (penAnalysisArray?.length > 0) {
      penAnalysisArray?.map(item => {
        if (item?.daysInMilk > 0 || item?.milk > 0) {
          sum = true;
          return;
        }
      });
    }
    setEnableResults(penAnalysisArray?.length > 0);
    return penAnalysisArray?.length > 0;
  };

  const shouldEnableResultsButton = (toolType, penAnalysisArray) => {
    switch (toolType) {
      case TOOL_ANALYSIS_TYPES.PEN_ANALYSIS:
        return calculatePensSum(penAnalysisArray);
      case TOOL_ANALYSIS_TYPES.HERD_ANALYSIS:
        return calculateHerdSum(penAnalysisArray);
      default:
        return true;
    }
  };

  // @DESC render component function for selected tools OR tool type selection
  const renderSelectedToolComponent = () => {
    switch (selectedCategoryTool?.toolType || '') {
      case TOOL_ANALYSIS_TYPES.ANIMAL_ANALYSIS:
        return (
          <AnimalAnalysis
            currentStep={currentStep}
            totalSteps={totalBCSSteps}
            penList={toolState.pensList || []}
            scaleList={scaleList}
            screenDisabled={!isEditable}
            isBCS={true}
            selectedVisits={selectedVisits}
            healthCurrentActivePen={healthCurrentActivePen}
            setEnableResults={setEnableResults}
            animalDataForAnimalAnalysis={animalDataForAnimalAnalysis}
          />
        );

      case TOOL_ANALYSIS_TYPES.PEN_ANALYSIS:
        return (
          <PenAnalysis
            currentStep={currentStep}
            totalSteps={totalBCSSteps}
            penList={toolState.pensList || []}
            scaleList={scaleList}
            screenDisabled={!isEditable}
            selectedVisits={selectedVisits}
            healthCurrentActivePen={healthCurrentActivePen}
            penAnalysis={penAnalysis}
            setPenAnalysis={setPenAnalysis}
            setEnableResults={setEnableResults}
            shouldEnableResultsButton={shouldEnableResultsButton}
            setIsDirty={setIsDirty}
          />
        );

      case TOOL_ANALYSIS_TYPES.HERD_ANALYSIS:
        return (
          <HerdAnalysis
            currentStep={currentStep}
            totalSteps={totalBCSSteps}
            penList={toolState.pensList || []}
            screenDisabled={!isEditable}
            scaleList={scaleList}
            goalsData={goalsData}
            refreshPensData={fetchSitePensData}
            enableResults={enableResults}
            shouldEnableResultsButton={shouldEnableResultsButton}
            penDataForHerdSum={penDataForHerdSum}
            isDirty={isDirty}
            setIsDirty={setIsDirty}
          />
        );

      default:
        return (
          <ToolAnalysis
            showAnimalAnalysis
            onStepChange={onNextStepClick}
            onPenAnalysisSelect={handlePenAnalysisSelect}
          />
        );
    }
  };

  const onConfirmCompareVisits = selectedVisits => {
    setSelectedVisits(selectedVisits);
  };

  const updateGoalsInDB = goals => {
    const { id } = visitState.visit || {};
    const unMappedGoals = unformatGoalsDataForBCS(goals);
    dispatch(
      saveBCSGoalsRequest({
        goals: unMappedGoals,
        localVisitId: id,
        isEditable: isEditable,
      }),
    );
  };

  return (
    <>
      <ToolsInProgress
        currentStep={currentStep}
        showGoalsModal={
          selectedCategoryTool?.toolType === TOOL_ANALYSIS_TYPES.HERD_ANALYSIS
        }
        showCompareGraph={
          currentStep === totalBCSSteps &&
          selectedCategoryTool?.toolType !== TOOL_ANALYSIS_TYPES.HERD_ANALYSIS
        }
        compareModalData={toolState?.recentVisits || []}
        selectedVisits={selectedVisits}
        currentVisit={visitState?.visit?.id}
        onConfirmCompareModal={onConfirmCompareVisits}
        goalsModalTitle={i18n.t('BCSByStageOfLactation')}
        goalsHeaderLabels={[i18n.t('GoalMIN'), i18n.t('GoalMAX')]}
        goalsModalData={getFormattedGoalsDataForBCS(goalsData, enumState)}
        updateGoalsInDB={updateGoalsInDB}
        hasDecimalInput={true}
        isEditable={isEditable}
      />

      {renderSelectedToolComponent()}

      <StepsFooter
        totalSteps={totalBCSSteps}
        currentStep={currentStep}
        onLeftArrowClick={onPrevStepClick}
        onResultPress={onNextStepClick}
        currentActiveTool={currentActiveTool}
        onDropdownStepSelect={onDropdownStepSelect}
        onToolListingClick={step => onToolListingClick(step)}
        bottomSheetSteps={BCS_STEPS}
        backToolListingStep={PEN_HERD_ANALYSIS_STEPS}
        disableBottomSheet={currentStep === initialStep ? true : false}
        isValid={enableResults}
      />
    </>
  );
};

export default withLogging(BCS, 'BCS');
