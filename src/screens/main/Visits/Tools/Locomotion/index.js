// modules
import React, { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useIsFocused, useNavigation } from '@react-navigation/native';
// components
import ToolAnalysis from '../../../../../components/Visits/Tools/common/ToolAnalysisTypes';
// import StepsFooter from '../common/ToolStepsFooter';
import AnimalAnalysis from '../../../../../components/Visits/Tools/BCS/AnimalAnalysis';
import PenAnalysis from '../../../../../components/Visits/Tools/Locomotion/PenAnalysis';
import ToolsInProgress from '../../../../../components/Visits/Tools/common/ToolsInProgress';
import HerdAnalysis from '../../../../../components/Visits/Tools/Locomotion/HerdAnalysis';
import StepsFooter from '../../../../../components/Visits/Tools/common/ToolStepsFooter';
import { showToast } from '../../../../../components/common/CustomToast';
import withLogging from '../../../../../components/common/ScreenWithLogging';
// constants
import {
  BCS_STEPS,
  ENUM_CONSTANTS,
  EXPORT_REPORT_TYPES,
  GRAPH_EXPORT_OPTIONS,
  GRAPH_HEADER_OPTIONS,
  PEN_HERD_ANALYSIS_STEPS,
  TOOL_ANALYSIS_TYPES,
  UNIT_OF_MEASURE,
  VISIT_TABLE_FIELDS,
} from '../../../../../constants/AppConstants';
import { TOAST_TYPE } from '../../../../../constants/FormConstants';

// localization
import i18n from '../../../../../localization/i18n';

import {
  clearActiveCategoryTool,
  downloadToolExcelRequest,
  downloadToolImageRequest,
  getSitePensRequest,
  emailToolExcelRequest,
  emailToolImageRequest,
} from '../../../../../store/actions/tool';
import { resetSaveActiveScreenRequest } from '../../../../../store/actions/toolSwitching';

import {
  allPenAnalysis,
  herdDataInitialized,
  onUpdateHerdObj,
  mapGraphDataForPenAnalysisExport,
  mapGraphDataForHerdAnalysisExport,
  shouldEnableResultsButton,
} from '../../../../../helpers/locomotionHelper';
import {
  convertNumberToString,
  convertStringToNumber,
  stringIsEmpty,
} from '../../../../../helpers/alphaNumericHelper';
import { dateHelper } from '../../../../../helpers/dateHelper';
import {
  convertWeightToImperial,
  convertWeightToMetric,
} from '../../../../../helpers/appSettingsHelper';
import { healthToolScreenAutoSelect } from '../../../../../helpers/visitHelper';
import { logEvent } from '../../../../../helpers/logHelper';

import { isOnline } from '../../../../../services/netInfoService';
import {
  initializeLocomotionScoreTool,
  initializeLocomotionScoreToolRequest,
  saveLocomotionPenAnalysisRequest,
} from '../../../../../store/actions/tools/locomotionScore';
import { getSiteByVisitRequest } from '../../../../../store/actions/site';

const Locomotion = ({
  selectedCategoryTool,
  currentActiveTool,
  openToolSheet,
  healthCurrentActivePen = null,
  healthCurrentActiveScreen = null,
}) => {
  const totalToolSteps = 3;
  const initialStep = 1;
  const backToolListingStep = 0;

  const dispatch = useDispatch();

  const isFocus = useIsFocused();
  const navigation = useNavigation();

  const visitState = useSelector(state => state.visit);
  const currentVisit = useSelector(state => state.visit.visit);
  const toolState = useSelector(state => state.tool);
  const pensList = useSelector(state => state.tool.pensList);
  const enumState = useSelector(state => state.enums);
  const siteState = useSelector(state => state.site);
  const locomotionState = useSelector(
    state => state.locomotionScore.locomotionToolData,
  );
  const locomotionHerdData = useSelector(
    state => state.locomotionScore.locomotionToolData?.herd,
  );
  const selectedPen = useSelector(state => state.locomotionScore.selectedPen);

  const locomotionScore = visitState.visit?.locomotionScore || {};
  const { isEditable = false } = visitState?.visit || false;
  const unitOfMeasure = visitState?.visit?.unitOfMeasure || {};

  const [currentStep, setCurrentStep] = useState(1);
  const [scaleList, setScaleList] = useState([]);
  const [selectedVisits, setSelectedVisits] = useState([]);
  const [animalsInHerd, setAnimalsInHerd] = useState('');
  const [daysInMilk, setDaysInMilk] = useState('');
  const [milkProduction, setMilkProduction] = useState('');
  const [penAnalysis, setPenAnalysis] = useState(null);
  const [siteData, setSiteData] = useState({});
  const [herdData, setHerdData] = useState(
    herdDataInitialized(locomotionScore),
  );
  const [isDirty, setIsDirty] = useState(false);
  const [isDirtyHerd, setIsDirtyHerd] = useState(false);
  const [enableResults, setEnableResults] = useState(false);
  const [activePenAnalysis, setActivePenAnalysis] = useState({});

  const herdDataForHerdSum = useRef([]);
  const animalDataForAnimalAnalysis = useRef([]); //contains animal data for animal analysis

  useEffect(() => {
    getSitePens();
    setPenAnalysis(allPenAnalysis(visitState?.visit));
    setSelectedVisits([currentVisit?.id]);

    const { siteId, localSiteId } = visitState?.visit;
    dispatch(getSiteByVisitRequest({ siteId, localSiteId }));
  }, []);

  useEffect(() => {
    if (
      pensList?.length > 0 &&
      siteState?.visitSite &&
      stringIsEmpty(visitState?.locomotionScore)
    ) {
      // Initialize locomotion tool with static model data
      dispatch(initializeLocomotionScoreToolRequest());
    }
  }, [pensList, siteState?.visitSite]);

  useEffect(() => {
    if (enumState?.enum[ENUM_CONSTANTS.BCS_POINT_SCALES]) {
      let scales = enumState?.enum[ENUM_CONSTANTS.BCS_POINT_SCALES];
      scales = scales.map(scale => ({ ...scale, name: scale.value }));
      setScaleList(scales);
    }
  }, [enumState]);

  // useEffect(() => {
  //   if (!stringIsEmpty(visitState?.visit?.locomotionScore)) {
  //     setHerdData(
  //       herdDataInitialized(visitState?.visit?.locomotionScore || {}),
  //     );
  //   }
  // }, [visitState?.visit?.locomotionScore]);

  useEffect(() => {
    if (!toolState.recentVisitsLoading) {
      const recentVisits = toolState.recentVisits;
      const visitIds = recentVisits.map(visit => visit.id);
      setSelectedVisits(visitIds);
    }
  }, [toolState.recentVisitsLoading]);

  useEffect(() => {
    if (currentStep === initialStep) {
      // saveLocomotionHerdAnalysis();
      dispatch(clearActiveCategoryTool());
    }
  }, [currentStep]);

  useEffect(() => {
    return () => saveHerdAnalysis();
  }, [isFocus]);

  // useEffect(() => {
  //   if (
  //     !stringIsEmpty(siteState.visitSite) &&
  //     Object.keys(siteState.visitSite).length > 0
  //   ) {
  //     let site = siteState.visitSite;
  //     setSiteData(siteState.visitSite);
  //     if (!isEditable && !stringIsEmpty(herdData)) {
  //       setAnimalsInHerd(
  //         stringIsEmpty(herdData?.totalAnimalsInHerd)
  //           ? ''
  //           : Number(herdData?.totalAnimalsInHerd).toFixed(0),
  //       );
  //       setDaysInMilk(
  //         stringIsEmpty(herdData?.daysInMilk)
  //           ? ''
  //           : Number(herdData?.daysInMilk).toFixed(0),
  //       );
  //       let milk = herdData?.milkProductionInKg;
  //       if (unitOfMeasure === UNIT_OF_MEASURE.IMPERIAL) {
  //         if (milk) {
  //           milk = convertWeightToImperial(milk, 1) + '';
  //         }
  //       }
  //       if (milk == null || milk == NaN) {
  //         setMilkProduction('');
  //       } else {
  //         setMilkProduction(
  //           stringIsEmpty(milk)
  //             ? ''
  //             : convertNumberToString(Number(milk).toFixed(0)),
  //         );
  //       }

  //       let siteDetailData = { ...siteState.visitSite };
  //       siteDetailData.milk = Number(herdData?.milkProductionInKg);
  //       siteDetailData.lactatingAnimal = Number(herdData?.totalAnimalsInHerd);
  //       siteDetailData.daysInMilk = Number(herdData?.daysInMilk);
  //       setSiteData(siteDetailData);
  //     } else if (isEditable) {
  //       console.log('site?.lactatingAnimal', site?.lactatingAnimal);
  //       setAnimalsInHerd(Number(site?.lactatingAnimal).toFixed(0));
  //       setDaysInMilk(
  //         stringIsEmpty(site?.daysInMilk)
  //           ? ''
  //           : Number(site?.daysInMilk).toFixed(0),
  //       );
  //       let milk = site?.milk;
  //       if (unitOfMeasure === UNIT_OF_MEASURE.IMPERIAL) {
  //         if (milk) {
  //           milk = convertWeightToImperial(milk, 1) + '';
  //         }
  //       }
  //       if (milk == null || milk == NaN) {
  //         setMilkProduction('');
  //       } else {
  //         setMilkProduction(convertNumberToString(Number(milk).toFixed(1)));
  //       }
  //     }
  //   }
  //   saveLocomotionHerdAnalysis();
  // }, [siteState.visitSite, openToolSheet]);

  useEffect(() => {
    if (healthCurrentActiveScreen && currentStep === initialStep) {
      healthToolScreenAutoSelect(
        dispatch,
        healthCurrentActiveScreen,
        currentStep,
        setCurrentStep,
      );
    }
  }, [healthCurrentActiveScreen]);

  const getSitePens = () => {
    if (currentVisit?.siteId || currentVisit?.localSiteId) {
      const { siteId = '', localSiteId = '', locomotionScore } = currentVisit;
      const payload = {
        locomotionScore,
        siteId,
        localSiteId,
        toolType: VISIT_TABLE_FIELDS.LOCOMOTION_SCORE,
      };
      dispatch(getSitePensRequest(payload));
    }
  };

  const saveHerdAnalysis = () => {
    if (!isFocus) {
      saveLocomotionHerdAnalysis();
    }
  };

  const saveLocomotionHerdAnalysis = async () => {
    try {
      if (!isEditable) {
        return;
      }

      const visitId = visitState?.visit?.id;
      let milkProdKg = convertStringToNumber(
        locomotionState?.herd?.milkProductionInKg,
      );
      if (unitOfMeasure === UNIT_OF_MEASURE.IMPERIAL) {
        milkProdKg = convertWeightToMetric(
          convertStringToNumber(locomotionState?.herd?.milkProductionInKg),
        );
      }

      let milkPrice = siteState.visitSite.currentMilkPrice;
      const herdAnalysisObj = onUpdateHerdObj(
        locomotionState?.herd,
        milkProdKg,
        locomotionState?.herd?.daysInMilk,
        locomotionState?.herd?.totalAnimalsInHerd,
        visitState.visit,
        locomotionState?.herd?.milkPriceAtSiteLevel || milkPrice,
      );

      console.log('herdAnalysisObj', herdAnalysisObj);

      if (
        selectedCategoryTool?.toolType === TOOL_ANALYSIS_TYPES.HERD_ANALYSIS
        // (isDirtyHerd || !stringIsEmpty(visitState.visit?.locomotionScore)) //save herd if either herd is changed or pen analysis made the tool in-progress
      ) {
        dispatch(
          saveLocomotionPenAnalysisRequest({
            locomotionScoreData: herdAnalysisObj,
            updated_at: dateHelper.getUnixTimestamp(new Date()),
            localVisitId: visitId,
            mobileLastUpdatedTime: dateHelper.getUnixTimestamp(new Date()),
          }),
        );
      }

      // const { id } = visitState?.visit;
      // if (isEditable) {
      //   let milkProdKg = convertStringToNumber(milkProduction);
      //   if (unitOfMeasure === UNIT_OF_MEASURE.IMPERIAL) {
      //     milkProdKg = convertWeightToMetric(
      //       convertStringToNumber(milkProduction),
      //     );
      //   }
      //   let milkPrice = siteState.visitSite.currentMilkPrice;
      //   const herdAnalysisObj = onUpdateHerdObj(
      //     herdData,
      //     milkProdKg,
      //     daysInMilk,
      //     animalsInHerd,
      //     visitState.visit,
      //     milkPrice,
      //   );
      //   console.log(
      //     'herdAnalysisObj saveLocomotionHerdAnalysis',
      //     herdAnalysisObj,
      //   );
      //   //CDEA-2680: This prevents execution on pen analysis screen which was nulling all the pen data
      //   if (
      //     selectedCategoryTool?.toolType ===
      //       TOOL_ANALYSIS_TYPES.HERD_ANALYSIS &&
      //     (isDirtyHerd || !stringIsEmpty(visitState.visit?.locomotionScore)) //save herd if either herd is changed or pen analysis made the tool in-progress
      //   ) {
      //     dispatch(
      //       saveLocomotionPenAnalysisRequest({
      //         locomotionScoreData: herdAnalysisObj,
      //         updated_at: dateHelper.getUnixTimestamp(new Date()),
      //         localVisitId: id,
      //         mobileLastUpdatedTime: dateHelper.getUnixTimestamp(new Date()),
      //       }),
      //     );
      //   }
      // }
    } catch (error) {
      logEvent(
        'screens -> Locomotion -> saveLocomotionHerdAnalysis Exception',
        error,
      );
      console.log('saveLocomotionHerdAnalysis Exception', error);
    }
  };

  const onNextStepClick = () => {
    if (currentStep === totalToolSteps) {
      dispatch(resetSaveActiveScreenRequest()); //pressing 'done' button should clear reducer
      dispatch(clearActiveCategoryTool());
      setCurrentStep(1);
    } else {
      const localSum = shouldEnableResultsButton(
        selectedCategoryTool?.toolType,
        selectedCategoryTool?.toolType === TOOL_ANALYSIS_TYPES.PEN_ANALYSIS
          ? selectedPen
          : locomotionHerdData,
      );
      console.log('localSum', localSum);
      localSum && setCurrentStep(prevState => prevState + 1);
    }
    saveLocomotionHerdAnalysis();
  };

  const onPrevStepClick = () => {
    if (currentStep === totalToolSteps) {
      setCurrentStep(prevState => prevState - 1);
    } else if (currentStep === 2) {
      dispatch(resetSaveActiveScreenRequest());
      setCurrentStep(prevState => prevState - 1);
    }
  };

  const onToolListingClick = selectedStep => {
    if (selectedStep?.step == backToolListingStep) {
      navigation.goBack();
    } else if (selectedStep?.step === initialStep) {
      dispatch(resetSaveActiveScreenRequest()); //pressing 'done' button should clear reducer
      dispatch(clearActiveCategoryTool());
      setCurrentStep(1);
    }
  };

  const onConfirmCompareVisits = selectedVisits => {
    setSelectedVisits(selectedVisits);
  };

  const onDropdownStepSelect = selectedStep => {
    saveLocomotionHerdAnalysis();
    if (currentStep === selectedStep?.step) return;
    else if (selectedStep?.step === initialStep) {
      dispatch(clearActiveCategoryTool());
      setCurrentStep(prevState => (prevState = selectedStep?.step));
    } else if (selectedStep?.step === 2) {
      setCurrentStep(prevState => (prevState = selectedStep?.step));
    } else if (selectedStep?.step === 3) {
      if (
        selectedCategoryTool?.toolType === TOOL_ANALYSIS_TYPES.ANIMAL_ANALYSIS
      ) {
        if (animalDataForAnimalAnalysis.current == true) {
          setCurrentStep(prevState => (prevState = selectedStep?.step));
        }
        return;
      }
      const localSum = shouldEnableResultsButton(
        selectedCategoryTool?.toolType,
        selectedCategoryTool?.toolType === TOOL_ANALYSIS_TYPES.PEN_ANALYSIS
          ? selectedPen
          : locomotionHerdData,
      );
      localSum && setCurrentStep(prevState => (prevState = selectedStep?.step));
    }
  };

  const onChangeHerdData = value => {
    // setIsDirtyHerd(true);
    // setHerdData(value);
  };

  const downloadHerdAnalysisData = async (graphData, type, herdData) => {
    if (await isOnline()) {
      const model = mapGraphDataForHerdAnalysisExport(
        visitState.visit,
        graphData,
        herdData,
      );
      if (type == GRAPH_EXPORT_OPTIONS.EXCEL) {
        dispatch(
          downloadToolExcelRequest({
            exportType:
              EXPORT_REPORT_TYPES.LOCOMOTION_SCORE_HERD_ANALYSIS_REPORT,
            model,
          }),
        );
      } else {
        dispatch(
          downloadToolImageRequest({
            exportType:
              EXPORT_REPORT_TYPES.LOCOMOTION_SCORE_HERD_ANALYSIS_REPORT,
            model,
          }),
        );
      }
    } else {
      showToast(TOAST_TYPE.ERROR, i18n.t('noInternetConnection'));
    }
  };

  const onShareHerdAnalysisData = async (
    graphData,
    type,
    herdData,
    exportMethod,
  ) => {
    if (await isOnline()) {
      const model = mapGraphDataForHerdAnalysisExport(
        visitState.visit,
        graphData,
        herdData,
      );

      //share excel
      if (
        type === GRAPH_EXPORT_OPTIONS.EXCEL &&
        exportMethod == GRAPH_HEADER_OPTIONS.SHARE
      ) {
        dispatch(
          emailToolExcelRequest({
            exportType:
              EXPORT_REPORT_TYPES.LOCOMOTION_SCORE_HERD_ANALYSIS_REPORT,
            model,
          }),
        );
        return;
      }
      // share image
      dispatch(
        emailToolImageRequest({
          exportType: EXPORT_REPORT_TYPES.LOCOMOTION_SCORE_HERD_ANALYSIS_REPORT,
          model,
        }),
      );
    } else {
      showToast(TOAST_TYPE.ERROR, i18n.t('noInternetConnection'));
    }
  };

  const renderSelectedToolComponent = () => {
    switch (selectedCategoryTool?.toolType || '') {
      case TOOL_ANALYSIS_TYPES.ANIMAL_ANALYSIS:
        return (
          <AnimalAnalysis
            currentStep={currentStep}
            totalSteps={totalToolSteps}
            penList={pensList}
            scaleList={scaleList}
            isLocomotion={true}
            screenDisabled={!isEditable}
            isBCS={false}
            selectedVisits={selectedVisits}
            healthCurrentActivePen={healthCurrentActivePen}
            setEnableResults={setEnableResults}
            animalDataForAnimalAnalysis={animalDataForAnimalAnalysis}
          />
        );

      case TOOL_ANALYSIS_TYPES.PEN_ANALYSIS:
        return (
          <PenAnalysis
            currentStep={currentStep}
            totalToolSteps={totalToolSteps}
            selectedVisits={selectedVisits}
            openToolSheet={openToolSheet}
            healthCurrentActivePen={healthCurrentActivePen}
            isDirty={isDirty}
            setIsDirty={setIsDirty}
            setEnableResults={setEnableResults}
            setActivePenAnalysis={setActivePenAnalysis}
          />
        );

      case TOOL_ANALYSIS_TYPES.HERD_ANALYSIS:
        return (
          <HerdAnalysis
            herdData={herdData}
            animalsInHerd={animalsInHerd}
            milkProduction={milkProduction}
            daysInMilk={daysInMilk}
            setHerdData={onChangeHerdData}
            setAnimalsInHerd={setAnimalsInHerd}
            setDaysInMilk={setDaysInMilk}
            setMilkProduction={setMilkProduction}
            openToolSheet={openToolSheet}
            currentStep={currentStep}
            totalToolSteps={totalToolSteps}
            selectedVisits={selectedVisits}
            onShareHerdAnalysisData={onShareHerdAnalysisData}
            onDownloadPress={downloadHerdAnalysisData}
            isDirty={isDirtyHerd}
            setIsDirty={setIsDirtyHerd}
            setEnableResults={setEnableResults}
            herdDataForHerdSum={herdDataForHerdSum}
          />
        );

      default:
        return (
          <ToolAnalysis showAnimalAnalysis onStepChange={onNextStepClick} />
        );
    }
  };
  return (
    <>
      <ToolsInProgress
        currentStep={currentStep}
        showCompareGraph={
          currentStep == totalToolSteps &&
          selectedCategoryTool?.toolType !== TOOL_ANALYSIS_TYPES.HERD_ANALYSIS
        }
        compareModalData={toolState?.recentVisits}
        selectedVisits={selectedVisits}
        currentVisit={visitState?.visit?.id}
        onConfirmCompareModal={onConfirmCompareVisits}
        herdData={herdData}
        siteData={siteData}
        showRevenueButton={
          currentStep != 1 &&
          selectedCategoryTool?.toolType === TOOL_ANALYSIS_TYPES.HERD_ANALYSIS
        }
      />
      {renderSelectedToolComponent()}

      <StepsFooter
        totalSteps={totalToolSteps}
        currentStep={currentStep}
        onLeftArrowClick={onPrevStepClick}
        onResultPress={onNextStepClick}
        currentActiveTool={currentActiveTool}
        onDropdownStepSelect={onDropdownStepSelect}
        bottomSheetSteps={BCS_STEPS}
        backToolListingStep={PEN_HERD_ANALYSIS_STEPS}
        onToolListingClick={step => onToolListingClick(step)}
        disableBottomSheet={currentStep === initialStep ? true : false}
        isValid={enableResults}
      />
    </>
  );
};

export default withLogging(Locomotion, 'Locomotion');
