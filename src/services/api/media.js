import { Platform } from 'react-native';
import { Buffer } from 'buffer';
import axios from 'axios';
import Share from 'react-native-share';
import ReactNativeBlobUtil from 'react-native-blob-util';
import RNFS from 'react-native-fs';
import BackgroundDownloader from '@kesha-antonov/react-native-background-downloader';
import Config from 'react-native-config';

import AccessToken, { apiClient } from './index';
import {
  getErrorMessage,
  responseErrorInterceptor,
  successResponseInterceptor,
} from './interceptor';
import { refreshAccessToken } from './authentication';

import { showAlertMsg } from '../../components/common/Alerts';

// localization
import i18n, { getLanguage } from '../../localization/i18n';

// helpers
import { getMediaNoteBookAbsolutePath } from '../../helpers/genericHelper';
import {
  getExportLogFileAppInfoHeaders,
  logEvent,
  clearLogFolder,
  writeToUserLogFile,
} from '../../helpers/logHelper';

// constants
import APIConstants from '../../constants/APIConstants';
import {
  AUTH_CLIENTS,
  DEV_LOG_FILE_NAME,
  LOG_FOLDER_PATH,
  MAX_API_CALL_RETRIES,
  MEDIA_TYPES,
  MIME_TYPE,
} from '../../constants/AppConstants';

export const getPresignedUrls = async payload => {
  try {
    let url = APIConstants.GET_PRESIGNED_URLS;
    let response = await apiClient()
      .post(url, payload)
      .then(successResponseInterceptor)
      .catch(responseErrorInterceptor);
    return response;
  } catch (error) {
    logEvent('services -> mediaService -> getPresignedUrls error', error);
    console.log('getPresignedUrls error', error);
  }
};

export const saveBase64FromURLWithoutToken = async (
  url,
  filename,
  ext = '',
) => {
  try {
    let dirs = ReactNativeBlobUtil.fs.dirs;
    let response = await ReactNativeBlobUtil.config({
      // add this option that makes response data to be stored as a file,
      // this is much more performant.
      fileCache: true,
      //cache file at this path
      path:
        Platform.OS === 'ios'
          ? dirs.DocumentDir + '/' + filename + ext
          : dirs.DownloadDir + '/' + filename,
      timeout: 60000,
    }).fetch('GET', url);

    let status = response.info().status;
    let destination = null;

    if (status == 200 || status == 201) {
      destination =
        Platform.OS === 'android'
          ? 'file://' + response.path()
          : '' + response.path();
    }
    return destination;
  } catch (errorMessage) {
    throw errorMessage;
  }
};

export const saveFileFromURLWithPOSTMethod = async (
  url,
  filename,
  body,
  ext,
  mimeType,
  retryApiCallCount = 0,
) => {
  try {
    let dirs = ReactNativeBlobUtil.fs.dirs;
    filename = Platform.OS === 'ios' ? filename + ext : filename;

    let response = await ReactNativeBlobUtil.config({
      fileCache: true,
      path:
        Platform.OS === 'ios'
          ? dirs.DocumentDir + '/' + filename
          : dirs.DownloadDir + '/' + filename,
      timeout: 120000,
      notification: true,
      appendExt: ext?.replace('.', ''),
    })
      .fetch(
        'POST',
        url,
        {
          authorization: 'Bearer ' + AccessToken.token,
          'Content-Type': 'application/json',
          'User-Agent': null,
          'Accept-Language':
            getLanguage()?.toUpperCase() || i18n.defaultLocale?.toUpperCase(),
          'x-auth-issuer':
            AccessToken.authServer === AUTH_CLIENTS.AZURE
              ? AUTH_CLIENTS.AZURE
              : AUTH_CLIENTS.OKTA,
        },
        JSON.stringify(body),
      )
      .catch(responseErrorInterceptor);

    let status = response.info().status;
    let destination = null;
    if (status == 200 || status == 201) {
      destination =
        Platform.OS === 'android'
          ? 'file://' + response.path()
          : '' + response.path();

      if (Platform.OS === 'android') {
        await ReactNativeBlobUtil.MediaCollection.copyToMediaStore(
          {
            name: filename, // name of the file
            parentFolder: '', // subdirectory in the Media Store, e.g. HawkIntech/Files to create a folder HawkIntech with a subfolder Files and save the image within this folder
            mimeType, // MIME type of the file
          },
          'Download', // Media Collection to store the file in ("Audio" | "Image" | "Video" | "Download")
          response.path(), // Path to the file being copied in the apps own storage
        );
      } else {
        ReactNativeBlobUtil.ios.openDocument(response.path());
      }

      return destination;
    } else if (status == 401) {
      const { hasError, data } = await refreshAccessToken();

      retryApiCallCount += 1;

      if (hasError || retryApiCallCount === MAX_API_CALL_RETRIES) {
        throw new Error(getErrorMessage(data));
      } else {
        console.log('saveFileFromURLWithPOSTMethod fail 401');

        return saveFileFromURLWithPOSTMethod(
          url,
          filename,
          body,
          ext,
          mimeType,
          retryApiCallCount,
        );
      }
    } else {
      console.log('saveFileFromURLWithPOSTMethod fail else', response.info());
      return destination;
    }
  } catch (errorMessage) {
    logEvent(
      'services -> mediaService -> saveFileFromURLWithPOSTMethod catch',
      errorMessage,
    );
    console.log('saveFileFromURLWithPOSTMethod fail catch', errorMessage);
    throw errorMessage;
  }
};

// TODO: Need to merge these two methods (GET, POST)
export const saveFileFromURLWithGETMethod = async (
  url,
  filename,
  body,
  ext,
  mimeType,
) => {
  try {
    let dirs = ReactNativeBlobUtil.fs.dirs;
    filename = Platform.OS === 'ios' ? filename + ext : filename;

    let response = await ReactNativeBlobUtil.config({
      fileCache: true,
      path:
        Platform.OS === 'ios'
          ? dirs.DocumentDir + '/' + filename
          : dirs.DownloadDir + '/' + filename,
      timeout: 120000,
      notification: true,
      appendExt: ext.replace('.', ''),
    })
      .fetch('GET', url, {
        authorization: 'Bearer ' + AccessToken.token,
        'Content-Type': 'application/json',
        'User-Agent': null,
        'Accept-Language':
          getLanguage()?.toUpperCase() || i18n.defaultLocale?.toUpperCase(),
        'x-auth-issuer':
          AccessToken.authServer === AUTH_CLIENTS.AZURE
            ? AUTH_CLIENTS.AZURE
            : AUTH_CLIENTS.OKTA,
      })
      .catch(responseErrorInterceptor);

    let status = response.info().status;
    let destination = null;
    if (status == 200 || status == 201) {
      destination =
        Platform.OS === 'android'
          ? 'file://' + response.path()
          : '' + response.path();

      if (Platform.OS === 'android') {
        await ReactNativeBlobUtil.MediaCollection.copyToMediaStore(
          {
            name: filename, // name of the file
            parentFolder: '', // subdirectory in the Media Store, e.g. HawkIntech/Files to create a folder HawkIntech with a subfolder Files and save the image within this folder
            mimeType, // MIME type of the file
          },
          'Download', // Media Collection to store the file in ("Audio" | "Image" | "Video" | "Download")
          response.path(), // Path to the file being copied in the apps own storage
        );
      } else {
        ReactNativeBlobUtil.ios.openDocument(response.path());
      }
      return true;
    } else if (status == 401) {
      const { hasError, data } = await refreshAccessToken();
      if (hasError) {
        throw new Error(getErrorMessage(data));
      }
      saveFileFromURLWithGETMethod(url, filename, body, ext, mimeType);
    }
    // return false;
  } catch (errorMessage) {
    logEvent(
      'services -> mediaService -> saveFileFromURLWithGETMethod fail',
      errorMessage,
    );
    console.log('saveFileFromURLWithGETMethod fail', errorMessage);
    throw errorMessage;
  }
};

export const saveBase64ToFileSystem = async (base64Image, filename) => {
  try {
    let Base64Code = base64Image.split('data:image/png;base64,'); //base64Image is my image base64 string

    const dirs = ReactNativeBlobUtil.fs.dirs;

    let path =
      Platform.OS === 'ios'
        ? dirs.DocumentDir + '/' + filename
        : dirs.DownloadDir + '/' + filename;

    let size = await ReactNativeBlobUtil.fs.writeFile(
      path,
      Base64Code[1],
      'base64',
    );

    if (size) {
      return {
        path: Platform.OS === 'ios' ? path : 'file://' + path,
        size,
        type: MEDIA_TYPES.IMAGES,
        mime: MIME_TYPE.PNG,
      };
    } else {
      return null;
    }
  } catch (errorMessage) {
    logEvent(
      'services -> mediaService -> saveBase64ToFileSystem fail',
      errorMessage,
    );
    console.log('saveBase64ToFileSystem fail', errorMessage);
    throw errorMessage;
  }
};

export const shareFileFromURLWithPOSTMethod = async (
  url,
  filename,
  body,
  ext,
  mimeType,
  retryApiCallCount = 0,
) => {
  try {
    let dirs = ReactNativeBlobUtil.fs.dirs;
    filename = Platform.OS === 'ios' ? filename + ext : filename + ext;

    let res = await ReactNativeBlobUtil.config({
      fileCache: true,
      path:
        Platform.OS === 'ios'
          ? dirs.DocumentDir + '/' + filename
          : dirs.DownloadDir + '/' + filename,
      timeout: 120000,
      notification: true,
      appendExt: ext.replace('.', ''),
    })
      .fetch(
        'POST',
        url,
        {
          authorization: 'Bearer ' + AccessToken.token,
          'Content-Type': 'application/json',
          'User-Agent': null,
          'Accept-Language':
            getLanguage()?.toUpperCase() || i18n.defaultLocale?.toUpperCase(),
          'x-auth-issuer':
            AccessToken.authServer === AUTH_CLIENTS.AZURE
              ? AUTH_CLIENTS.AZURE
              : AUTH_CLIENTS.OKTA,
        },
        JSON.stringify(body),
      )
      .then(async response => {
        let status = response.info().status;

        if (status == 200 || status == 201) {
          if (Platform.OS === 'android') {
            ReactNativeBlobUtil.MediaCollection.copyToMediaStore(
              {
                name: filename, // name of the file
                parentFolder: '', // subdirectory in the Media Store, e.g. HawkIntech/Files to create a folder HawkIntech with a subfolder Files and save the image within this folder
                mimeType, // MIME type of the file
              },
              'Download', // Media Collection to store the file in ("Audio" | "Image" | "Video" | "Download")
              response.path(), // Path to the file being copied in the apps own storage
            );
          } else {
            // ReactNativeBlobUtil.ios.openDocument(res.path());
          }

          return response;
        } else if (status == 401) {
          const { hasError, data } = await refreshAccessToken();

          retryApiCallCount += 1;

          if (hasError || retryApiCallCount === MAX_API_CALL_RETRIES) {
            throw new Error(getErrorMessage(data));
          } else {
            return shareFileFromURLWithPOSTMethod(
              url,
              filename,
              body,
              ext,
              mimeType,
              retryApiCallCount,
            );
          }
        } else {
          console.log(
            'shareFileFromURLWithPOSTMethod fail on download',
            response.info(),
          );

          return false;
        }
      })
      .then(savedFile => {
        if (savedFile) {
          const emailSubject = `${body?.accountName || ''} - ${
            body?.visitName
          } - ${body?.visitDate}`;

          const shareOptions = {
            type: mimeType,
            title: i18n.t('share'),
            message: '',
            subject: emailSubject,
            social: Share.Social.EMAIL,
            failOnCancel: false,
            url:
              Platform.OS === 'android'
                ? `file://${savedFile.path()}`
                : savedFile.path(),
            filename: filename || '',
            excludedActivityTypes: [
              'airDrop',
              'assignToContact',
              'copyToPasteBoard',
              'markupAsPDF',
              'message',
              'saveToCameraRoll',
              'print',
              'postToWeibo',
              'postToVimeo',
              'postToTwitter',
              'postToTencentWeibo',
              'postToFlickr',
              'postToFacebook',
              'openInIBooks',
            ],
          };

          Share.shareSingle(shareOptions)
            .then(res => {
              // console.log(res);
            })
            .catch(err => {
              showAlertMsg(i18n.t('error'), i18n.t('ensureEmailConfigured'));
            });
        }

        return savedFile;
      })
      .catch(responseErrorInterceptor);

    return res;
  } catch (errorMessage) {
    logEvent(
      'services -> mediaService -> shareFileFromURLWithPOSTMethod catch',
      errorMessage,
    );
    console.log('shareFileFromURLWithPOSTMethod catch', errorMessage);
    throw errorMessage;
  }
};

export const uploadMediaToS3UsingPreSignedUrl = async (
  url,
  id,
  binaryDataInBase64,
) => {
  try {
    const buffer = Buffer.from(
      binaryDataInBase64.replace(/^data:image\/\w+;base64,/, ''),
      'base64',
    );
    const result = await axios.put(url, buffer, {
      headers: {
        'Content-Type': 'image/png',
        'Content-Encoding': 'base64',
      },
    });

    if (result.status == 200) {
      return true;
    } else {
      return false;
    }
  } catch (e) {
    if (e.data) {
      e.data = null;
    }
    logEvent(
      'services -> mediaService -> uploadMediaToS3UsingPreSignedUrl fail',
      e,
    );
    console.log('uploadMediaToS3UsingPreSignedUrl fail', e);
    throw e;
  }
};

export const uploadExportLogsToS3UsingPreSignedUrl = async (
  url,
  id,
  userInfo,
  binaryDataInBase64,
) => {
  try {
    let isUploadedSuccessfully = false;
    const filePath = `${RNFS.DocumentDirectoryPath}/${LOG_FOLDER_PATH}/${DEV_LOG_FILE_NAME}.txt`;

    let meta = await getExportLogFileAppInfoHeaders(userInfo);
    await writeToUserLogFile(filePath, meta);

    const fileData = await RNFS.readFile(`${filePath}`, 'utf8');

    const formData = [{ name: 'file', filename: id, data: fileData }];

    await axios
      .put(url, formData, {
        timeout: 90000,
        headers: {
          'Content-Type': 'text/plain',
        },
      })
      .then(async result => {
        if (result.status == 200) {
          await emailLogs(userInfo);
          if (Platform.OS == 'android') {
            setTimeout(async () => {
              await clearLogFolder();
            }, 3000);
          }
          isUploadedSuccessfully = true;
        }
      })
      .catch(error => {
        throw error;
      });
    // .finally(() => deleteFile(userLogFile));
    // }
    return isUploadedSuccessfully;
  } catch (errorMessage) {
    logEvent(
      'services -> mediaService -> uploadTextToS3UsingPreSignedUrl fail',
      errorMessage,
    );
    console.log('uploadTextToS3UsingPreSignedUrl fail', errorMessage);
    return false;
  }
};

export const emailLogs = async userInfo => {
  let isEmailSentSuccessfully = false;
  try {
    const filePath = `${RNFS.DocumentDirectoryPath}/${LOG_FOLDER_PATH}/${DEV_LOG_FILE_NAME}.txt`;

    const recipientEmail = Config.EXPORT_LOG_EMAIL;
    const fileUri = `file://${filePath}`;

    await Share.shareSingle({
      title: i18n.t('shareLogsFile'),
      message: i18n.t('pleaseFindAttachedLogsFile'),
      url: fileUri, // Path to the file
      email: recipientEmail, // Fixed or dynamic recipient email
      subject: '', // Predefined email subject
      social: Share.Social.EMAIL,
      failOnCancel: false, // Prevent errors on user cancel
    });

    isEmailSentSuccessfully = true;
  } catch (errorMessage) {
    logEvent('services -> mediaService -> emailLogs fail', errorMessage);
    console.error('emailLogs fail', errorMessage);
  } finally {
    return isEmailSentSuccessfully;
  }
};

export const uploadNoteBookMediaToS3UsingPreSignedUrl = async (
  url,
  id,
  data,
) => {
  try {
    let filename = '';
    let filePath = '';
    if (data.mediaType == 'VoiceNotes') {
      filename = data?.mediaId;
      filename = filename + '.mp4';
      filePath = getMediaNoteBookAbsolutePath(filename);
    } else {
      filePath = getMediaNoteBookAbsolutePath(data?.path);
    }
    const result = await ReactNativeBlobUtil.fetch(
      'PUT',
      url,
      {
        'Content-Type': data?.mime,
      },
      ReactNativeBlobUtil.wrap(decodeURI(filePath)),
    );
    if (result?.respInfo?.status == 200) {
      return true;
    } else {
      return false;
    }
  } catch (errorMessage) {
    console.log('uploadMediaToS3UsingPreSignedUrl fail', errorMessage);
    throw errorMessage;
  }
};

export const downloadNoteBookMediaToS3UsingPreSignedUrl = async (
  url,
  filename,
) => {
  try {
    if (AccessToken.token) {
      filename = filename.replace('mpeg', 'mp4');
      let dirs = ReactNativeBlobUtil.fs.dirs;
      let response = await ReactNativeBlobUtil.config({
        // add this option that makes response data to be stored as a file,
        // this is much more performant.
        fileCache: true,
        //cache file at this path
        path:
          Platform.OS === 'ios'
            ? dirs.DocumentDir + '/notes/' + filename
            : dirs.DocumentDir + '/notes/' + filename,
        timeout: 60000,
      }).fetch('GET', url);
      let status = response.info().status;
      let destination = null;
      if (status == 200 || status == 201 || response?.data !== '') {
        // if (!destinationType) {
        destination =
          Platform.OS === 'android'
            ? 'file://' + response.path()
            : '' + response.path();
      }
      // }
      return destination;
    }
  } catch (errorMessage) {
    console.log(
      'downloadNoteBookMediaToS3UsingPreSignedUrl Exception errorMessage',
      errorMessage,
    );
    throw errorMessage;
  }
};

export const downloadMediaFromS3UsingPreSignedUrlBackground = async (
  url,
  destination,
  id,
) => {
  return new Promise((resolve, reject) => {
    BackgroundDownloader.download({
      id: `download_${id}`,
      url,
      destination,
    })
      .begin(expectedBytes => {})
      .progress(percent => {})
      .done(() => {
        resolve(destination);
      })
      .error(error => {
        if (error.errorCode == -1 || error.errorCode === 404) {
          resolve(null);
        }
        // console.log(`Download of note with id ${id} failed:`, error);
        reject(error); // Reject promise on error
      });
  });
};

export const uploadVisitReportFileRequest = async (
  url,
  payload,
  uploadProgressCallback,
  retryApiCallCount = 0,
) => {
  try {
    const formData = new FormData();
    formData.append('visitReportFile', {
      uri: payload?.filePath,
      name: 'visitReportFile',
      type: 'application/pdf',
    });

    const axiosResponse = await axios
      .post(url, formData, {
        timeout: 300000,
        headers: {
          Accept: 'application/json, text/plain, */*',
          'User-Agent': null,
          'Content-Type': 'multipart/form-data',
          authorization: 'Bearer ' + AccessToken.token,
          'Accept-Language':
            getLanguage()?.toUpperCase() || i18n.defaultLocale?.toUpperCase(),
          'x-auth-issuer':
            AccessToken.authServer === AUTH_CLIENTS.AZURE
              ? AUTH_CLIENTS.AZURE
              : AUTH_CLIENTS.OKTA,
        },
        onUploadProgress: p => uploadProgressCallback(p),
      })
      .then(async data => {
        if (data.status === 202) return true;
      })
      .catch(async error => {
        if (error?.response.status === 401) {
          const { hasError, data } = await refreshAccessToken();

          retryApiCallCount += 1;

          if (hasError || retryApiCallCount === MAX_API_CALL_RETRIES) {
            throw new Error(getErrorMessage(data));
          } else {
            return uploadVisitReportFileRequest(
              url,
              payload,
              uploadProgressCallback,
              retryApiCallCount,
            );
          }
        } else {
          return false;
        }
      });

    return axiosResponse;

    // await ReactNativeBlobUtil.fetch(
    //   'POST',
    //   apiUrl,
    //   {
    //     authorization: 'Bearer ' + AccessToken.token,
    //     'Content-Type': 'multipart/form-data',
    //     'Accept-Language':
    //       getLanguage()?.toUpperCase() || i18n.defaultLocale?.toUpperCase(),
    //     'x-auth-issuer':
    //       AccessToken.authServer === AUTH_CLIENTS.AZURE
    //         ? AUTH_CLIENTS.AZURE
    //         : AUTH_CLIENTS.OKTA,
    //   },
    //   formData,
    // )
    //   // listen to upload progress event
    //   // .uploadProgress((written, total) => {
    //   //   console.log('uploaded', written / total);
    //   // })
    //   .then(async resp => {
    //   })
  } catch (error) {
    return false;
  }
};
