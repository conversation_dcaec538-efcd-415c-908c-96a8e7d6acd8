import { failure, request, success } from '..';
import LOCOMOTION_ACTIONS from '../../../constants/actionConstants/tools/locomotionScore';

//#region save locomotion pen analysis
export const saveLocomotionPenAnalysisRequest = model => {
  return request(
    LOCOMOTION_ACTIONS.SAVE_LOCOMOTION_PEN_ANALYSIS_REQUEST,
    model,
  );
};

export const saveLocomotionPenAnalysisSuccess = data => {
  return success(LOCOMOTION_ACTIONS.SAVE_LOCOMOTION_PEN_ANALYSIS_SUCCESS, data);
};

export const saveLocomotionPenAnalysisFailure = error => {
  return failure(
    LOCOMOTION_ACTIONS.SAVE_LOCOMOTION_PEN_ANALYSIS_FAILURE,
    error,
  );
};
//#end region
