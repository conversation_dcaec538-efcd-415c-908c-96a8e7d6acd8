import { failure, request, success } from '..';
import LOCOMOTION_ACTIONS from '../../../constants/actionConstants/tools/locomotionScore';

//#region save locomotion pen analysis
export const saveLocomotionPenAnalysisRequest = model => {
  return request(
    LOCOMOTION_ACTIONS.SAVE_LOCOMOTION_PEN_ANALYSIS_REQUEST,
    model,
  );
};

export const saveLocomotionPenAnalysisSuccess = data => {
  return success(LOCOMOTION_ACTIONS.SAVE_LOCOMOTION_PEN_ANALYSIS_SUCCESS, data);
};

export const saveLocomotionPenAnalysisFailure = error => {
  return failure(
    LOCOMOTION_ACTIONS.SAVE_LOCOMOTION_PEN_ANALYSIS_FAILURE,
    error,
  );
};
//#end region

// #region initialize locomotion score tool
export const initializeLocomotionScoreToolRequest = payload =>
  request(LOCOMOTION_ACTIONS.INITIALIZE_LOCOMOTION_SCORE_TOOL_REQUEST, payload);

export const initializeLocomotionScoreToolSuccess = data =>
  success(LOCOMOTION_ACTIONS.INITIALIZE_LOCOMOTION_SCORE_TOOL_SUCCESS, data);

export const initializeLocomotionScoreToolFailure = error =>
  failure(LOCOMOTION_ACTIONS.INITIALIZE_LOCOMOTION_SCORE_TOOL_FAILURE, error);
// #end region

// #region selected pen actions
export const setSelectedLocomotionPenRequest = payload =>
  request(LOCOMOTION_ACTIONS.SET_SELECTED_LOCOMOTION_PEN_REQUEST, payload);

export const updateSelectedPenData = payload =>
  request(LOCOMOTION_ACTIONS.UPDATE_SELECTED_PEN_DATA, payload);

export const updateSelectedPenFormData = payload =>
  request(LOCOMOTION_ACTIONS.UPDATE_SELECTED_PEN_FORM_DATA, payload);

export const changeLocomotionPen = payload =>
  request(LOCOMOTION_ACTIONS.CHANGE_LOCOMOTION_PEN, payload);

// # region update locomotion tool data
export const updateLocomotionToolDataRequest = payload =>
  request(LOCOMOTION_ACTIONS.UPDATE_LOCOMOTION_TOOL_DATA_REQUEST, payload);

// #region herd analysis
export const updateLocomotionHerdAnalysisGoalData = payload =>
  request(LOCOMOTION_ACTIONS.UPDATE_LOCOMOTION_HERD_GOAL_DATA, payload);
