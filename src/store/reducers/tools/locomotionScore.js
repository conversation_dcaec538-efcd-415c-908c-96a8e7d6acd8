import LOCOMOTION_ACTIONS from '../../../constants/actionConstants/tools/locomotionScore';

const initialState = {
  locomotionPenAnalysisLoading: false,
  locomotionPenAnalysis: null,
  saveLocomotionPenAnalysisLoading: false,
  saveLocomotionPenAnalysisSuccess: false,
  saveLocomotionPenAnalysisError: false,
  saveLocomotionPenAnalysisErrorMsg: null,
};

const locomotionScore = (state = initialState, action) => {
  switch (action.type) {
    //#region SAVE_LOCOMOTION_PEN_ANALYSIS
    case LOCOMOTION_ACTIONS.SAVE_LOCOMOTION_PEN_ANALYSIS_REQUEST:
      return {
        ...state,
        saveLocomotionPenAnalysisLoading: true,
        saveLocomotionPenAnalysisSuccess: false,
        saveLocomotionPenAnalysisError: false,
        saveLocomotionPenAnalysisErrorMsg: null,
      };

    case LOCOMOTION_ACTIONS.SAVE_LOCOMOTION_PEN_ANALYSIS_SUCCESS:
      return {
        ...state,
        saveLocomotionPenAnalysisLoading: false,
        saveLocomotionPenAnalysisSuccess: true,
        saveLocomotionPenAnalysisError: false,
        saveLocomotionPenAnalysisErrorMsg: null,
      };

    case LOCOMOTION_ACTIONS.SAVE_LOCOMOTION_PEN_ANALYSIS_FAILURE:
      return {
        ...state,
        saveLocomotionPenAnalysisLoading: false,
        saveLocomotionPenAnalysisSuccess: false,
        saveLocomotionPenAnalysisError: true,
        saveLocomotionPenAnalysisErrorMsg: action.payload.error,
      };
    //#end region

    default:
      return state;
  }
};

export default locomotionScore;
