import LOCOMOTION_ACTIONS from '../../../constants/actionConstants/tools/locomotionScore';

const initialState = {
  locomotionPenAnalysisLoading: false,
  locomotionPenAnalysis: null,
  saveLocomotionPenAnalysisLoading: false,
  saveLocomotionPenAnalysisSuccess: false,
  saveLocomotionPenAnalysisError: false,
  saveLocomotionPenAnalysisErrorMsg: null,

  // Initialize locomotion tool state
  locomotionToolData: null,

  selectedPen: null,
};

const locomotionScore = (state = initialState, action) => {
  switch (action.type) {
    //#region SAVE_LOCOMOTION_PEN_ANALYSIS
    case LOCOMOTION_ACTIONS.SAVE_LOCOMOTION_PEN_ANALYSIS_REQUEST:
      return {
        ...state,
        saveLocomotionPenAnalysisLoading: true,
        saveLocomotionPenAnalysisSuccess: false,
        saveLocomotionPenAnalysisError: false,
        saveLocomotionPenAnalysisErrorMsg: null,
      };

    case LOCOMOTION_ACTIONS.SAVE_LOCOMOTION_PEN_ANALYSIS_SUCCESS:
      return {
        ...state,
        saveLocomotionPenAnalysisLoading: false,
        saveLocomotionPenAnalysisSuccess: true,
        saveLocomotionPenAnalysisError: false,
        saveLocomotionPenAnalysisErrorMsg: null,
      };

    case LOCOMOTION_ACTIONS.SAVE_LOCOMOTION_PEN_ANALYSIS_FAILURE:
      return {
        ...state,
        saveLocomotionPenAnalysisLoading: false,
        saveLocomotionPenAnalysisSuccess: false,
        saveLocomotionPenAnalysisError: true,
        saveLocomotionPenAnalysisErrorMsg: action.payload.error,
      };
    //#end region

    //#region INITIALIZE_LOCOMOTION_SCORE_TOOL
    case LOCOMOTION_ACTIONS.INITIALIZE_LOCOMOTION_SCORE_TOOL_REQUEST:
      return state;

    case LOCOMOTION_ACTIONS.INITIALIZE_LOCOMOTION_SCORE_TOOL_SUCCESS:
      return {
        ...state,
        locomotionToolData: action.payload,
      };

    case LOCOMOTION_ACTIONS.INITIALIZE_LOCOMOTION_SCORE_TOOL_FAILURE:
      return state;
    //#end region

    // # region selected pen changes
    case LOCOMOTION_ACTIONS.SET_SELECTED_LOCOMOTION_PEN_REQUEST:
      return {
        ...state,
        selectedPen: action.payload,
      };

    case LOCOMOTION_ACTIONS.UPDATE_SELECTED_PEN_DATA:
      return {
        ...state,
        selectedPen: {
          ...state.selectedPen,
          categories: action.payload,
        },
      };

    case LOCOMOTION_ACTIONS.UPDATE_SELECTED_PEN_FORM_DATA:
      return {
        ...state,
        selectedPen: action.payload,
      };

    case LOCOMOTION_ACTIONS.UPDATE_LOCOMOTION_TOOL_DATA_REQUEST:
      return {
        ...state,
        locomotionToolData: action.payload,
      };

    // #region update herd analysis goal data
    case LOCOMOTION_ACTIONS.UPDATE_LOCOMOTION_HERD_GOAL_DATA:
      return {
        ...state,
        locomotionToolData: {
          ...state.locomotionToolData,
          herd: action.payload,
        },
      };

    default:
      return state;
  }
};

export default locomotionScore;
