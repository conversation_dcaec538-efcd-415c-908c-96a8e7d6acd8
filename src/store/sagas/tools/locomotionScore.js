// modules
import { call, put, select, takeLatest } from 'redux-saga/effects';

// constants
import LOCOMOTION_ACTIONS from '../../../constants/actionConstants/tools/locomotionScore';

// helpers
import {
  extractUsedPensFromLocomotionScoreTool,
  initializeLocomotionToolData,
  initializeNewLocomotionPen,
  insertPenInLocomotionToolData,
  isLocomotionPenUsed,
} from '../../../helpers/locomotionHelper';
import { logEvent } from '../../../helpers/logHelper';
import { extendsUsedPensInVisitWithToolPens } from '../../../helpers/visitHelper';

// data manager
import { saveLocomotionPenAnalysisByVisit } from '../../../database/dataManager/VisitManager';

// actions
import {
  saveLocomotionPenAnalysisFailure,
  saveLocomotionPenAnalysisSuccess,
  initializeLocomotionScoreToolSuccess,
  initializeLocomotionScoreToolFailure,
  setSelectedLocomotionPenRequest,
  updateLocomotionToolDataRequest,
} from '../../actions/tools/locomotionScore';
import { updateVisitInProgressStatus } from '../../actions/visit';
import { getParsedToolData } from '../../../helpers/genericHelper';

// selectors
const pensListSelector = state => state.tool.pensList;
const getSelectedVisitSelector = state => state.visit.visit;
const animalAnalysisSelector = state => state.visit.visit?.animalAnalysis;
const locomotionToolDataSelector = state =>
  state.locomotionScore.locomotionToolData;
const currentSiteSelector = state => state.site.visitSite;

function* saveLocomotionPenAnalysisSaga(action) {
  try {
    let model = action.payload;
    const locomotionUsedPens = yield call(
      extractUsedPensFromLocomotionScoreTool,
      model?.locomotionScoreData,
    );

    if (locomotionUsedPens) {
      const visit = yield select(getSelectedVisitSelector);

      const updatedVisitUsedPens = yield call(
        extendsUsedPensInVisitWithToolPens,
        visit,
        locomotionUsedPens,
      );

      updatedVisitUsedPens ? (model.usedPens = updatedVisitUsedPens) : null;
    }

    console.log('modelll--', model);
    let response = yield call(saveLocomotionPenAnalysisByVisit, model);
    if (response) {
      yield put(saveLocomotionPenAnalysisSuccess({}));
      yield put(updateVisitInProgressStatus());
    }
  } catch (error) {
    yield put(saveLocomotionPenAnalysisFailure({ error: error.message }));
  }
}

function* initializeLocomotionToolSaga() {
  try {
    const pensList = yield select(pensListSelector);
    const visit = yield select(getSelectedVisitSelector);
    const animalAnalysis = yield select(animalAnalysisSelector);
    const site = yield select(currentSiteSelector);

    const parsedLocomotionData = yield call(
      getParsedToolData,
      visit?.locomotionScore,
    );

    // loading already saved locomotion tool data
    if (parsedLocomotionData && Object.keys(parsedLocomotionData)?.length > 0) {
      yield put(initializeLocomotionScoreToolSuccess(parsedLocomotionData));

      const selectedPen = yield call(
        isLocomotionPenUsed,
        pensList[0],
        parsedLocomotionData,
      );
      yield put(setSelectedLocomotionPenRequest(selectedPen));
      return;
    }

    // Initialize locomotion tool data using the helper function
    // The function now generates static model data
    const initializedData = yield call(
      initializeLocomotionToolData,
      pensList,
      animalAnalysis,
      site,
    );

    yield put(initializeLocomotionScoreToolSuccess(initializedData));

    if (initializedData?.pens?.length > 0) {
      yield put(setSelectedLocomotionPenRequest(initializedData?.pens[0]));
    }
  } catch (error) {
    console.log('initializeLocomotionToolSaga error:', error);
    yield put(initializeLocomotionScoreToolFailure({ error: error.message }));
    logEvent(
      'sagas -> locomotionScoreSaga -> initializeLocomotionToolSaga Error:',
      error,
    );
  }
}

function* changeLocomotionPenSaga(action) {
  try {
    const pen = action.payload;

    const animalAnalysis = yield select(animalAnalysisSelector);
    const locomotionData = yield select(locomotionToolDataSelector);

    const isPenUsed = yield call(isLocomotionPenUsed, pen, locomotionData);
    if (isPenUsed) {
      yield put(setSelectedLocomotionPenRequest(isPenUsed));
      return;
    }

    const newSelectedPen = yield call(
      initializeNewLocomotionPen,
      pen,
      animalAnalysis,
    );
    yield put(setSelectedLocomotionPenRequest(newSelectedPen));

    const updatedLocomotionData = yield call(
      insertPenInLocomotionToolData,
      newSelectedPen,
      locomotionData,
    );
    yield put(updateLocomotionToolDataRequest(updatedLocomotionData));
  } catch (error) {
    console.log('initializeLocomotionToolSaga error:', error);
    logEvent(
      'sagas -> locomotionScoreSaga -> changeLocomotionPenSaga Error:',
      error,
    );
  }
}

function* locomotionScoreSaga() {
  yield takeLatest(
    LOCOMOTION_ACTIONS.INITIALIZE_LOCOMOTION_SCORE_TOOL_REQUEST,
    initializeLocomotionToolSaga,
  );

  yield takeLatest(
    LOCOMOTION_ACTIONS.SAVE_LOCOMOTION_PEN_ANALYSIS_REQUEST,
    saveLocomotionPenAnalysisSaga,
  );

  yield takeLatest(
    LOCOMOTION_ACTIONS.CHANGE_LOCOMOTION_PEN,
    changeLocomotionPenSaga,
  );
}

export default locomotionScoreSaga;
