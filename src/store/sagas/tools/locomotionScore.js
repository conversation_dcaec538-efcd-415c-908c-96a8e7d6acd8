// modules
import { call, put, select, takeLatest } from 'redux-saga/effects';

// constants
import LOCOMOTION_ACTIONS from '../../../constants/actionConstants/tools/locomotionScore';

// helpers
import { extendsUsedPensInVisitWithToolPens } from '../../../helpers/visitHelper';
import { extractUsedPensFromLocomotionScoreTool } from '../../../helpers/locomotionHelper';

// data manager
import { saveLocomotionPenAnalysisByVisit } from '../../../database/dataManager/VisitManager';

// actions
import {
  saveLocomotionPenAnalysisFailure,
  saveLocomotionPenAnalysisSuccess,
} from '../../actions/tools/locomotionScore';
import { updateVisitInProgressStatus } from '../../actions/visit';

const getSelectedVisit = state => state.visit.visit;

function* saveLocomotionPenAnalysisSaga(action) {
  try {
    let model = action.payload;
    const locomotionUsedPens = yield call(
      extractUsedPensFromLocomotionScoreTool,
      model?.locomotionScoreData,
    );

    if (locomotionUsedPens) {
      const visit = yield select(getSelectedVisit);

      const updatedVisitUsedPens = yield call(
        extendsUsedPensInVisitWithToolPens,
        visit,
        locomotionUsedPens,
      );

      updatedVisitUsedPens ? (model.usedPens = updatedVisitUsedPens) : null;
    }

    let response = yield call(saveLocomotionPenAnalysisByVisit, model);
    if (response) {
      yield put(saveLocomotionPenAnalysisSuccess({}));
      yield put(updateVisitInProgressStatus());
    }
  } catch (error) {
    yield put(saveLocomotionPenAnalysisFailure({ error: error.message }));
  }
}

function* locomotionScoreSaga() {
  yield takeLatest(
    LOCOMOTION_ACTIONS.SAVE_LOCOMOTION_PEN_ANALYSIS_REQUEST,
    saveLocomotionPenAnalysisSaga,
  );
}

export default locomotionScoreSaga;
