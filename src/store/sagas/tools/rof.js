import { takeLatest, call, put, select, delay, all } from 'redux-saga/effects';

// actions
import ROF_ACTIONS from '../../../constants/actionConstants/tools/rof';

import {
  getRofPriceListSuccess,
  getRofPriceListFailure,
  syncRofPriceListSuccess,
  syncRofPriceListFailure,
  saveROFToolDataFailure,
  saveROFToolDataSuccess,
  getROFSummaryFailure,
  getROFSummarySuccess,
  getPreviousRofVisitDataFailure,
  getPreviousRofVisitDataSuccess,
} from '../../actions/tools/rof';
import {
  downloadToolExcelRequest,
  downloadToolImageRequest,
  emailToolExcelRequest,
  emailToolImageRequest,
} from '../../actions/tool';
import { updateToolSiteRequest } from '../../actions/site';

//services
import * as RofService from '../../../services/api/rofPriceList';
import { isOnline } from '../../../services/netInfoService';

//helpers
import { logEvent } from '../../../helpers/logHelper';
import { stringIsEmpty } from '../../../helpers/alphaNumericHelper';
import {
  getROFToolDataForDB,
  downloadShareRofGraphDataModel,
} from '../../../helpers/rofHelper';
import { getParsedToolData } from '../../../helpers/genericHelper';

//managers
import * as RofManager from '../../../database/dataManager/tools/RofManager';

//constants
import {
  GRAPH_EXPORT_OPTIONS,
  GRAPH_HEADER_OPTIONS,
  VISIT_TABLE_FIELDS,
} from '../../../constants/AppConstants';
import { ROF_FIELDS, TOAST_TYPE } from '../../../constants/FormConstants';
import { showToast } from '../../../components/common/CustomToast';

// localization
import i18n from '../../../localization/i18n';
import {
  getCurrencyForTools,
  getWeightUnitByMeasure,
} from '../../../helpers/appSettingsHelper';

const currentVisit = state => state.visit.visit;
const isSiteDataUpdated = state => state.rof.isSiteDataUpdated;
const siteData = state => state.tool.siteData;
const currentRofToolData = state => state.rof.rofToolData;
const authenticatedUser = state => state.authentication.user;
const comparingRofVisitsIds = state => state.rof.comparingRofVisits;
const recentVisits = state => state.tool.recentVisits;
const unitOfMeasure = state => state.visit.visit?.unitOfMeasure;
const currencies = state => state.enums.enum?.currencies;
const selectedCurrency = state => state.visit.visit?.selectedCurrency;

function* getRofPriceList() {
  try {
    let data = yield call(RofManager.getAllRofPriceList);
    yield put(getRofPriceListSuccess(data));
  } catch (e) {
    logEvent('getRofPriceListFailure fail', e);
    yield put(getRofPriceListFailure({ error: e.message }));
  }
}

/**
 * Gets all the rofPriceList from server through API calls and store in offline db
 */
export function* syncRofPriceList() {
  try {
    let online = yield call(isOnline);
    if (online) {
      const response = yield call(RofService.getRofPriceList);
      if (!stringIsEmpty(response)) {
        // add rofPriceList to local db
        yield call(RofManager.createAllRofPriceList, response?.data);
      }
    }
    // Fetching all from server is successful.
    yield put(syncRofPriceListSuccess());
  } catch (e) {
    logEvent('syncRofPriceList fail', e);
    yield put(syncRofPriceListFailure({ error: e.message }));
  }
}

/**
 * save rof tool data to DB after calculations and create model
 * input form values + DB calculated values + summary values to a single saveable model
 */
export function* saveROFToolData(action) {
  try {
    let { values, formType, localVisitId, unitOfMeasure } = action.payload;
    let returnOverFeed = {};
    const visit = yield select(currentVisit);

    if (!stringIsEmpty(visit?.[VISIT_TABLE_FIELDS.ROF])) {
      returnOverFeed = yield call(
        getParsedToolData,
        visit?.[VISIT_TABLE_FIELDS.ROF],
      );
    }
    //parse tool's form data
    let formData = yield call(
      getROFToolDataForDB,
      values,
      unitOfMeasure,
      formType,
    );
    returnOverFeed[formType] = formData;

    yield call(RofManager.updateROFDataByVisitId, {
      returnOverFeed,
      localVisitId,
    });

    //save site data if edited
    const _isSiteDataUpdated = yield select(isSiteDataUpdated);
    if (_isSiteDataUpdated) {
      let _siteData = yield select(siteData);

      const siteUpdateModel = {
        ..._siteData,
        daysInMilk: formData?.[ROF_FIELDS.FEEDING]?.[ROF_FIELDS.DAYS_IN_MILK],
        lactatingAnimal:
          formData?.[ROF_FIELDS.FEEDING]?.[ROF_FIELDS.LACTATING_COWS],
      };

      yield put(updateToolSiteRequest(siteUpdateModel));
      yield delay(1000);
    }
    yield put(saveROFToolDataSuccess(returnOverFeed));
  } catch (e) {
    logEvent('saveROFToolData fail', e);
    console.log('saveROFToolData fail', e);
    yield put(saveROFToolDataFailure({ error: e.message }));
  }
}

export function* getROFSummary(action) {
  try {
    let { values, formType, unitOfMeasure } = action.payload;

    let formData = yield call(
      getROFToolDataForDB,
      values,
      unitOfMeasure,
      formType,
    );
    let returnOverFeed = {
      [formType]: formData,
    };

    yield put(getROFSummarySuccess(returnOverFeed));
  } catch (e) {
    logEvent('getROFSummary fail', e);
    yield put(getROFSummaryFailure({ error: e.message }));
  }
}

function* downloadRofExcelOrImageSaga(action) {
  try {
    if (yield call(isOnline)) {
      const [
        visit,
        rofToolData,
        rofRecentVisits,
        compareRofVisitsIds,
        UOM,
        _currencies,
        _selectedCurrency,
      ] = yield all([
        yield select(currentVisit),
        yield select(currentRofToolData),
        yield select(recentVisits),
        yield select(comparingRofVisitsIds),
        yield select(unitOfMeasure),
        yield select(currencies),
        yield select(selectedCurrency),
      ]);

      const weightUnit = getWeightUnitByMeasure(UOM);
      const currencySymbol = getCurrencyForTools(
        _currencies,
        _selectedCurrency,
      );

      const model = yield call(
        downloadShareRofGraphDataModel,
        rofToolData,
        visit,
        compareRofVisitsIds,
        rofRecentVisits,
        action.payload.formType,
        weightUnit,
        currencySymbol,
      );

      if (model) {
        if (action.payload?.exportType == GRAPH_EXPORT_OPTIONS.EXCEL) {
          yield put(
            downloadToolExcelRequest({
              exportType: action.payload?.selectedGraph,
              model,
            }),
          );
        } else {
          yield put(
            downloadToolImageRequest({
              exportType: action.payload?.selectedGraph,
              model,
            }),
          );
        }
      }
    } else {
      yield call(showToast, TOAST_TYPE.ERROR, i18n.t('noInternetConnection'));
    }
  } catch (error) {
    console.log('downloadExcelOrGraphSaga error', error);
    logEvent('sagas -> rof -> downloadRofExcelOrImageSaga error', error);
  }
}

function* shareRofExcelOrImageSaga(action) {
  try {
    if (yield call(isOnline)) {
      const [
        visit,
        rofToolData,
        rofRecentVisits,
        compareRofVisitsIds,
        UOM,
        _currencies,
        _selectedCurrency,
      ] = yield all([
        yield select(currentVisit),
        yield select(currentRofToolData),
        yield select(recentVisits),
        yield select(comparingRofVisitsIds),
        yield select(unitOfMeasure),
        yield select(currencies),
        yield select(selectedCurrency),
      ]);

      const weightUnit = getWeightUnitByMeasure(UOM);
      const currencySymbol = getCurrencyForTools(
        _currencies,
        _selectedCurrency,
      );

      const model = yield call(
        downloadShareRofGraphDataModel,
        rofToolData,
        visit,
        compareRofVisitsIds,
        rofRecentVisits,
        action.payload.formType,
        weightUnit,
        currencySymbol,
      );

      if (model) {
        if (
          action.payload?.exportType == GRAPH_EXPORT_OPTIONS.EXCEL &&
          action.payload?.exportMethod == GRAPH_HEADER_OPTIONS.SHARE
        ) {
          yield put(
            emailToolExcelRequest({
              exportType: action.payload?.selectedGraph,
              model,
            }),
          );

          return;
        }

        yield put(
          emailToolImageRequest({
            exportType: action.payload?.selectedGraph,
            model,
          }),
        );
      }
    } else {
      yield call(showToast, TOAST_TYPE.ERROR, i18n.t('noInternetConnection'));
    }
  } catch (error) {
    console.log('shareExcelOrImageSaga error', error);
    logEvent('sagas -> rof -> shareRofExcelOrImageSaga error', error);
  }
}

function* getPreviousROFVisitData() {
  try {
    let _visitData = yield select(currentVisit);
    const authUser = yield select(authenticatedUser);

    let payload = {
      siteId: _visitData.siteId,
      localSiteId: _visitData.localSiteId,
      visitId: _visitData.id,
    };

    let prevRofVisit = yield call(
      RofManager.getPreviousVisitROFData,
      payload,
      authUser,
    );

    yield put(getPreviousRofVisitDataSuccess(prevRofVisit));
  } catch (e) {
    logEvent('getPreviousROFVisitData fail', e);
    yield put(getPreviousRofVisitDataFailure({ error: e.message }));
  }
}

function* rofPriceListSaga() {
  yield takeLatest(ROF_ACTIONS.SYNC_ROF_PRICE_LIST_REQUEST, syncRofPriceList);
  yield takeLatest(ROF_ACTIONS.GET_ROF_PRICE_LIST_REQUEST, getRofPriceList);
  yield takeLatest(
    ROF_ACTIONS.SAVE_OR_UPDATE_ROF_DATA_REQUEST,
    saveROFToolData,
  );
  yield takeLatest(ROF_ACTIONS.GET_ROF_SUMMARY_REQUEST, getROFSummary);
  yield takeLatest(
    ROF_ACTIONS.DOWNLOAD_ROF_GRAPH_REQUEST,
    downloadRofExcelOrImageSaga,
  );
  yield takeLatest(
    ROF_ACTIONS.SHARE_ROF_GRAPH_REQUEST,
    shareRofExcelOrImageSaga,
  );
  yield takeLatest(
    ROF_ACTIONS.GET_PREVIOUS_ROF_VISIT_DATA_REQUEST,
    getPreviousROFVisitData,
  );
}

export default rofPriceListSaga;
